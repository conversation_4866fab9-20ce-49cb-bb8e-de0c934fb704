# 四频五步相移相位展开程序

这是一个用于结构光三维测量的四频五步相移相位展开程序，支持从低频到高频的分层相位展开算法。

## 功能特点

- **四频相移**: 支持四个不同频率的条纹图案 (像素周期: 1080, 512, 64, 8)
- **五步相移**: 每个频率使用五步相移算法计算包裹相位
- **分层展开**: 从低频到高频逐级展开，避免相位跳跃
- **质量控制**: 基于调制度的质量掩码，过滤低质量区域
- **可视化**: 自动生成相位图和结果分析图表

## 文件结构

```
├── phase_unwrapping_four_frequency.py  # 主要的相位展开算法
├── generate_test_images.py             # 测试图像生成器
├── test_phase_unwrapping.py            # 完整测试脚本
└── README.md                           # 说明文档
```

## 安装依赖

```bash
pip install numpy opencv-python matplotlib
```

## 使用方法

### 1. 快速测试

运行完整的测试程序：

```bash
python test_phase_unwrapping.py
```

这将：
- 生成测试图像
- 执行相位展开
- 显示结果分析
- 创建对比图表

### 2. 使用自己的图像

#### 图像命名规则

图像文件需要按以下格式命名：
```
freq_0_step_0.png  # 频率0(1080像素周期), 相移步骤0
freq_0_step_1.png  # 频率0(1080像素周期), 相移步骤1
...
freq_3_step_4.png  # 频率3(8像素周期), 相移步骤4
```

#### 代码示例

```python
from phase_unwrapping_four_frequency import FourFrequencyPhaseUnwrapping

# 创建相位展开对象
unwrapper = FourFrequencyPhaseUnwrapping(pixel_periods=[1080, 512, 64, 8])

# 处理相位展开
results = unwrapper.process_phase_unwrapping(
    image_folder="your_images_folder",
    output_folder="results",
    min_modulation=0.1
)

# 获取展开相位
unwrapped_phase = results['unwrapped_phase_masked']
```

### 3. 生成测试图像

如果需要生成测试图像：

```python
from generate_test_images import generate_test_images

# 生成基础测试图像
generate_test_images(
    output_folder="test_images",
    width=640,
    height=480,
    pixel_periods=[1080, 512, 64, 8],
    noise_level=0.02
)
```

## 算法原理

### 五步相移算法

对于每个频率，使用五幅相移图像计算包裹相位：

```
I₁ = I₀ + I_m cos(φ + 0)
I₂ = I₀ + I_m cos(φ + π/2)
I₃ = I₀ + I_m cos(φ + π)
I₄ = I₀ + I_m cos(φ + 3π/2)
I₅ = I₀ + I_m cos(φ + 2π)

φ = arctan2(2(I₂ - I₄), 2I₃ - I₁ - I₅)
```

### 分层相位展开

1. 从最低频(1080像素周期)开始，其包裹相位即为展开相位
2. 对于每个更高频率：
   - 将前一级展开相位按频率比缩放
   - 计算理论包裹相位
   - 通过相位差确定展开系数
   - 更新展开相位

### 质量控制

使用调制度作为质量指标：
```
调制度 = AC分量 / DC分量
质量掩码 = 所有频率的调制度 > 阈值
```

## 参数说明

### FourFrequencyPhaseUnwrapping 类参数

- `pixel_periods`: 像素周期列表，从低频到高频 [默认: [1080, 512, 64, 8]]

### process_phase_unwrapping 方法参数

- `image_folder`: 输入图像文件夹路径
- `output_folder`: 输出结果文件夹路径 [默认: "results"]
- `min_modulation`: 最小调制度阈值 [默认: 0.1]

## 输出结果

程序会在输出文件夹中生成：

1. **包裹相位图**: `wrapped_phase_period_XXX.png`
2. **展开相位图**: `unwrapped_phase.png`
3. **质量掩码**: `quality_mask.png`

返回的结果字典包含：
- `wrapped_phases`: 各频率的包裹相位列表
- `modulations`: 各频率的调制度列表
- `quality_mask`: 质量掩码
- `unwrapped_phase`: 原始展开相位
- `unwrapped_phase_masked`: 应用质量掩码后的展开相位

## 注意事项

1. **图像质量**: 确保输入图像有足够的对比度和信噪比
2. **像素周期**: 确保像素周期设置与实际投影的条纹周期匹配
3. **调制度阈值**: 根据实际情况调整最小调制度阈值
4. **内存使用**: 大图像可能需要较多内存，建议分块处理

## 故障排除

### 常见问题

1. **图像文件不存在**: 检查文件命名是否符合规则
2. **相位展开失败**: 检查图像质量和调制度阈值
3. **结果有噪声**: 降低调制度阈值或改善图像质量

### 调试建议

1. 使用测试图像验证算法正确性
2. 检查各频率的调制度分布
3. 可视化包裹相位确认条纹质量

## 扩展功能

可以根据需要扩展的功能：
- 支持更多频率
- 不同的相移步数
- 其他质量评估方法
- GPU加速处理
- 实时处理能力

## 联系方式

如有问题或建议，请联系开发者。
