# 四频相位解包三维重建最终系统

## 📦 系统概览

这是一个完整的工业级四频相位解包三维重建系统，包含标定、重建、融合和验证的完整工作流程。

**系统版本**: v2.0 (CCD_DX修正版)  
**开发日期**: 2024年7月  
**技术特点**: 20微米精细异常检测、工业级精度、标准化坐标系统

## 🎯 系统组件

### 📁 核心程序

#### 1. 标定程序
- **`phase_height_calibration_final.py`** - 最终标定系统 (基于C++参考的多项式映射)
- **`run_final_calibration.py`** - 标定运行脚本
- **标定精度**: RMSE 0.433mm (优秀级别)

#### 2. 重建融合程序  
- **`fusion_with_final_calibration.py`** - 数据重建与融合主程序 (CCD_DX修正版)
- **`four_freq_phase_unwrap.py`** - 四频相位解包核心算法
- **融合特点**: 20微米精细异常检测、自适应质量融合

#### 3. 辅助工具
- **`generate_point_cloud.py`** - 点云生成参考程序
- **`verify_point_cloud_scale.py`** - 点云尺度验证工具

#### 4. 参考实现
- **`标定参考程序/`** - C++多平面投影仪标定参考代码

### 📖 技术文档
- **`README_最终标定系统.md`** - 标定系统详细说明
- **`corrected_datasets_fusion_summary.md`** - 数据处理总结报告
- **`CCD_DX_correction_report.md`** - CCD_DX修正技术报告
- **`README_系统使用说明.md`** - 本文档

## 🚀 快速开始

### 第一步: 系统标定

```bash
# 使用标定数据进行系统标定
python run_final_calibration.py

# 输出: final_calibration_results_four_freq/ 文件夹
# 包含: mapping_tables/*.tiff, quality_report.json
```

### 第二步: 数据重建与融合

```bash
# 处理测试数据，生成高质量点云
python fusion_with_final_calibration.py \
    --test_folder "测试/pcb" \
    --calibration_folder "final_calibration_results_four_freq" \
    --output_folder "final_results" \
    --use_20um_detection

# 输出: 
#   - fused_depth.npy (深度图)
#   - fused_point_cloud.ply (点云文件)
#   - fusion_info.txt (质量报告)
```

### 第三步: 结果验证

```bash
# 验证点云尺度正确性
python verify_point_cloud_scale.py

# 检查CCD_DX系数一致性和物理尺度
```

## 🔧 详细使用说明

### 标定系统

#### 数据要求
- **格式**: BMP图像，84张/投影仪
- **命名**: 1.bmp ~ 84.bmp
- **内容**: 包含四频(1080,512,64,8像素周期)五步相移图像

#### 运行标定
```bash
python phase_height_calibration_final.py \
    --data_dir "标定数据/Test-0" \
    --output_dir "calibration_results" \
    --num_projectors 4
```

#### 标定输出
```
calibration_results/
├── mapping_tables/
│   ├── DLP-0-0.tiff  # 投影仪0常数项映射
│   ├── DLP-0-1.tiff  # 投影仪0线性项映射  
│   ├── DLP-0-2.tiff  # 投影仪0二次项映射
│   └── ...
├── calibration_results.pkl
└── quality_report.json
```

### 重建融合系统

#### 基本用法
```bash
python fusion_with_final_calibration.py \
    --test_folder "测试数据路径" \
    --calibration_folder "标定结果路径" \
    --output_folder "输出路径" \
    [--use_20um_detection]  # 可选：启用20微米精细检测
```

#### 参数说明
- `--test_folder`: 测试图像文件夹路径
- `--calibration_folder`: 标定结果文件夹 (包含mapping_tables)
- `--output_folder`: 输出结果保存路径
- `--use_20um_detection`: 启用20微米精细异常检测 (推荐)

#### 重建输出
```
output_folder/
├── fused_depth.npy          # 融合深度图
├── fused_point_cloud.ply    # 三维点云 (PLY格式)
└── fusion_info.txt          # 融合质量报告
```

### 高级功能

#### 批量处理
```bash
# 处理多个数据组
datasets=("pcb" "pcb1" "pcb2" "pcb3" "Test")
for dataset in "${datasets[@]}"; do
    python fusion_with_final_calibration.py \
        --test_folder "测试/$dataset" \
        --calibration_folder "final_calibration_results_four_freq" \
        --output_folder "results_$dataset" \
        --use_20um_detection
done
```

#### 质量验证
```bash
# 验证所有结果的物理尺度
python verify_point_cloud_scale.py

# 检查融合质量报告
cat results_*/fusion_info.txt
```

## 📊 技术指标

### 系统精度
- **标定精度**: RMSE 0.433mm (优秀)
- **深度精度**: 亚毫米级 (0.1-1.0mm标准差)
- **空间分辨率**: 0.0154mm/pixel (X、Y方向)
- **异常检测**: 20微米精细阈值

### 处理能力  
- **图像分辨率**: 2848×2848像素
- **投影仪数量**: 最大支持4个投影仪
- **处理速度**: ~2-3分钟/数据组
- **内存需求**: ~2GB RAM

### 输出格式
- **深度图**: NumPy .npy格式 (float64)
- **点云**: PLY格式 (ASCII，包含RGB颜色)
- **坐标系**: 毫米单位，右手坐标系
- **兼容性**: MeshLab, CloudCompare, PCL

## ⚙️ 系统配置

### 关键参数
```python
# CCD相机参数
CCD_DX = 0.0154  # mm/pixel (相机像素物理尺寸)

# 相位解包参数
PERIODS = [1080, 512, 64, 8]  # 四频像素周期
STEPS = 5  # 五步相移

# 融合参数
FINE_THRESHOLD = 0.02      # 20微米精细阈值
COARSE_THRESHOLD = 0.5     # 500微米粗糙阈值
```

### 文件路径配置
```python
# 标定数据路径
CALIBRATION_DATA = "标定数据/Test-*"
MAPPING_TABLES = "final_calibration_results_four_freq/mapping_tables/"

# 测试数据路径  
TEST_DATA = "测试/pcb*"
TEST_DATA = "测试/Test"
```

## 🔍 质量控制

### 质量评估指标
- **质量分数**: 0.0-1.0 (>0.8为优秀)
- **有效覆盖率**: 百分比 (目标>95%)
- **深度标准差**: 毫米 (<1.0mm为良好)
- **20微米一致性**: 百分比 (>15%为较好)

### 质量报告示例
```
融合策略: multi_projector_20um
有效投影仪: [0, 1, 2, 3] (4/4)
最终覆盖率: 100.00%

各投影仪质量:
  投影仪 0: 质量分数 0.931
  投影仪 1: 质量分数 0.942
  投影仪 2: 质量分数 0.944
  投影仪 3: 质量分数 0.945

深度统计:
  范围: -0.90 ~ 11.13 mm
  标准差: 0.527 mm (优秀)
```

## 🛠️ 故障排除

### 常见问题

#### 1. 标定失败
```
问题: "找不到标定数据"
解决: 检查数据路径，确保包含1.bmp~84.bmp
```

#### 2. 重建质量差
```
问题: 质量分数<0.5
解决: 
- 检查图像质量
- 调整光照条件
- 验证标定结果
```

#### 3. 点云尺度错误
```
问题: 物理尺寸不合理
解决: 运行 verify_point_cloud_scale.py 检查CCD_DX
```

#### 4. 内存不足
```
问题: 处理大图像时内存溢出
解决: 增加系统RAM或使用分块处理
```

### 日志分析
```bash
# 查看详细处理日志
python fusion_with_final_calibration.py ... 2>&1 | tee processing.log

# 检查质量报告
grep "质量分数\|标准差" fusion_info.txt
```

## 📈 性能优化

### 处理速度优化
- 使用SSD存储提升I/O性能
- 增加系统RAM (推荐8GB+)
- 使用多核CPU并行处理

### 质量优化
- 启用20微米精细检测: `--use_20um_detection`
- 优化光照条件获得更好的调制度
- 使用高质量标定数据

### 存储优化
- 点云文件较大(~300MB/组)，确保充足存储空间
- 可选择压缩PLY格式减小文件大小
- 定期清理中间结果文件

## 🎯 应用示例

### 工业检测
```bash
# 高精度PCB检测
python fusion_with_final_calibration.py \
    --test_folder "产品数据/PCB_批次1" \
    --calibration_folder "标定结果" \
    --output_folder "检测结果/PCB_批次1" \
    --use_20um_detection
```

### 科研分析
```bash
# 算法性能评估
for quality in low medium high; do
    python fusion_with_final_calibration.py \
        --test_folder "研究数据/${quality}_quality" \
        --output_folder "分析结果/${quality}" \
        --use_20um_detection
done
```

### 系统验证
```bash
# 完整系统测试
python run_final_calibration.py  # 标定
python fusion_with_final_calibration.py --test_folder "测试/Test" --use_20um_detection  # 重建
python verify_point_cloud_scale.py  # 验证
```

## 📞 技术支持

### 联系信息
- **技术文档**: 参考系统内各README文件
- **问题反馈**: 查看fusion_info.txt质量报告
- **系统版本**: v2.0 (CCD_DX修正版)

### 更新日志
- **v2.0**: CCD_DX系数修正、20微米精细检测集成
- **v1.5**: 多投影仪自适应融合、质量报告
- **v1.0**: 基础四频相位解包、标定系统

## 📋 检查清单

### 部署前检查
- [ ] Python环境安装 (NumPy, OpenCV, tifffile等)
- [ ] 标定数据准备 (84张图像/投影仪)
- [ ] 存储空间充足 (>5GB推荐)
- [ ] 系统内存充足 (>4GB推荐)

### 运行前检查
- [ ] 标定结果文件夹存在
- [ ] 测试数据格式正确 (BMP图像)
- [ ] 输出路径有写入权限
- [ ] CCD_DX参数正确设置

### 质量验证
- [ ] 质量分数 > 0.7
- [ ] 覆盖率 > 95%
- [ ] 深度标准差 < 1.0mm
- [ ] 物理尺寸合理 (~44mm)

---

**系统就绪！** 🚀  
**开始您的工业级三维重建之旅！** ✨ 