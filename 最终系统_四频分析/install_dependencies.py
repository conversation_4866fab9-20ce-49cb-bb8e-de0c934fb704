#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
四频相位解包三维重建系统 - 依赖包安装脚本
自动安装系统所需的Python依赖包
"""

import subprocess
import sys
import os

def run_pip_install(packages):
    """运行pip安装命令"""
    cmd = [sys.executable, "-m", "pip", "install"] + packages
    try:
        print(f"🔧 运行: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print(f"✅ 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        print(f"输出: {e.stdout}")
        print(f"错误: {e.stderr}")
        return False

def main():
    """主安装函数"""
    print("=" * 60)
    print("📦 四频相位解包三维重建系统 - 依赖包安装")
    print("=" * 60)
    
    # 必需的包列表
    required_packages = [
        "numpy>=1.19.0",
        "opencv-python>=4.5.0", 
        "tifffile>=2021.7.2",
        "tqdm>=4.60.0",
        "matplotlib>=3.3.0",
        "scipy>=1.7.0"
    ]
    
    # 可选的包
    optional_packages = [
        "psutil",  # 用于系统资源检查
        "jupyter",  # 用于交互式分析
        "scikit-image"  # 额外图像处理功能
    ]
    
    print("🎯 将安装以下必需包:")
    for pkg in required_packages:
        print(f"   • {pkg}")
    
    print("\n💡 可选包 (推荐安装):")
    for pkg in optional_packages:
        print(f"   • {pkg}")
    
    # 询问用户确认
    response = input("\n继续安装? [Y/n]: ").strip().lower()
    if response in ['n', 'no']:
        print("❌ 安装已取消")
        return
    
    print("\n开始安装必需包...")
    
    # 逐个安装必需包
    success_count = 0
    for package in required_packages:
        print(f"\n📦 安装 {package}...")
        if run_pip_install([package]):
            success_count += 1
    
    print(f"\n必需包安装完成: {success_count}/{len(required_packages)}")
    
    # 询问是否安装可选包
    if success_count == len(required_packages):
        install_optional = input("是否安装可选包? [Y/n]: ").strip().lower()
        if install_optional not in ['n', 'no']:
            print("\n安装可选包...")
            for package in optional_packages:
                print(f"\n📦 安装 {package}...")
                run_pip_install([package])
    
    print("\n" + "=" * 60)
    
    if success_count == len(required_packages):
        print("🎉 依赖包安装完成！")
        print("✨ 建议运行系统检查: python system_check.py")
    else:
        print(f"⚠️  部分包安装失败 ({success_count}/{len(required_packages)})")
        print("💡 请手动安装失败的包或检查网络连接")
    
    print("=" * 60)

if __name__ == "__main__":
    main() 