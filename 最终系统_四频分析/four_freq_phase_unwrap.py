"""
four_freq_phase_unwrap.py
================================
基于五步相移 (5-step phase shifting) 的四频相位展开算法示例。

算法假设已获取 4 组不同空间频率的条纹图像，每组 5 帧，
相移步长均为 2π/5 (即 72°)。

对应条纹的空间周期(像素数)依次为:
    1080, 512, 64, 8
其中 8 像素周期为最高频。

本脚本提供两个主要函数:
1. five_step_phase(frames) -> wrapped_phase
   输入 frames[...,5]，返回 [-π, π) 范围的包裹相位。

2. unwrap_four_frequency(frames_list, periods) -> unwrapped_phase
   frames_list: 按周期从大到小(低频→高频)的 frames 数组列表。
   periods: 与 frames_list 对应的周期列表。
   返回最高频(8 像素周期)的绝对相位 (已展开)。

用法示例见 __main__。
"""

from __future__ import annotations

import numpy as np
from typing import List, Sequence


# ------------------------- 五步相移相位提取 -------------------------

def five_step_phase(frames: np.ndarray) -> np.ndarray:
    """根据五步相移算法计算包裹相位。

    参数
    ----
    frames : ndarray
        ndarray[..., 5]，最后一维为 5 个相移帧 (0, 72°, 144°, 216°, 288°)。

    返回
    ----
    ndarray
        与 frames[...,0] 同形状的包裹相位，取值范围 [-π, π)。
    """
    if frames.shape[-1] != 5:
        raise ValueError("five_step_phase: 输入的最后一维必须为 5")

    # 依次取出五帧
    I0, I1, I2, I3, I4 = [frames[..., i] for i in range(5)]

    # 五步相移解析公式 (等相位步长情况)
    A = 2.0 * (I1 - I3)
    B = I0 - 2.0 * I2 + I4

    wrapped_phase = np.arctan2(A, B)
    return wrapped_phase


# ------------------------- 多频相位展开 -------------------------

def unwrap_multifrequency(
    phase_list: Sequence[np.ndarray],
    period_list: Sequence[float],
) -> List[np.ndarray]:
    """使用递阶方式进行多频相位展开。

    参数
    ----
    phase_list : Sequence[np.ndarray]
        按周期从大到小(低频→高频)排列的包裹相位数组。
    period_list : Sequence[float]
        与 phase_list 对应的条纹周期(像素)。

    返回
    ----
    List[np.ndarray]
        与 phase_list 对应的**已展开相位**列表，最后一个即最高频绝对相位。
    """
    if len(phase_list) != len(period_list):
        raise ValueError("phase_list 与 period_list 长度不一致")

    # 先处理最粗周期，相位直接取其包裹值(假设不会超 ±π)
    unwrapped = phase_list[0].copy()
    unwrapped_list: List[np.ndarray] = [unwrapped]

    # 递阶展开: 利用上一层(已展开)相位预测当前高频相位
    for k in range(1, len(phase_list)):
        phi_wrapped = phase_list[k]
        ratio = period_list[k - 1] / period_list[k]  # 周期比, >1

        # 预测当前频率的相位值
        phi_pred = unwrapped * ratio

        # 计算需要添加的 2π 整数倍，使其与预测值最接近
        delta = phi_pred - phi_wrapped
        m = np.round(delta / (2.0 * np.pi))

        unwrapped = phi_wrapped + 2.0 * np.pi * m
        unwrapped_list.append(unwrapped)

    return unwrapped_list


# ------------------------- 集成接口 -------------------------

def unwrap_four_frequency(
    frames_list: Sequence[np.ndarray],
    periods: Sequence[float] | None = None,
) -> np.ndarray:
    """四频相位展开主接口。

    参数
    ----
    frames_list : Sequence[np.ndarray]
        4 组 ndarray[...,5]，按周期从大到小 (1080→8) 排列。
    periods : Sequence[float] | None
        对应的周期列表，默认 [1080,512,64,8]。

    返回
    ----
    ndarray
        最高频 (8 像素周期) 的绝对相位。
    """
    if periods is None:
        periods = [1080.0, 512.0, 64.0, 8.0]

    if len(frames_list) != 4:
        raise ValueError("frames_list 必须包含 4 组数据")

    # 步骤 1: 逐频率计算包裹相位
    wrapped_phases = [five_step_phase(f) for f in frames_list]

    # 步骤 2: 递阶相位展开
    unwrapped_phases = unwrap_multifrequency(wrapped_phases, periods)

    # 返回最高频已展开相位
    return unwrapped_phases[-1]


# ------------------------- 示例/自测 -------------------------
if __name__ == "__main__":
    # 生成一维测试数据 (可删除，只作示例说明)
    x = np.linspace(0, 3000, 3001)  # 像素坐标
    ground_truth = 2.0 * np.pi * x / 8.0  # 真实最高频相位

    # 构造 4 组五步相移条纹
    periods_demo = [1080, 512, 64, 8]
    frames_all: List[np.ndarray] = []
    for p in periods_demo:
        # 5 帧: φ0 = 0°, φ1 = 72°, ... φ4 = 288°
        frames = np.stack([
            np.cos(2.0 * np.pi * x / p + s * 2.0 * np.pi / 5.0)
            for s in range(5)
        ], axis=-1)
        frames_all.append(frames)

    abs_phase = unwrap_four_frequency(frames_all, periods_demo)

    # 与真值相比, 差应当为 2π 整数倍 (测试用)
    diff = (abs_phase - ground_truth) / (2.0 * np.pi)
    print(
        "Mean integer difference:", np.mean(np.round(diff) - diff),
        "\nMax error (rad):", np.max(np.abs((abs_phase - ground_truth) % (2*np.pi))),
    ) 