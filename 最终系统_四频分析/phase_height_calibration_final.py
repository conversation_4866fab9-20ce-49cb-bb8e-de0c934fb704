#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于您的相位展开程序的最终标定程序
参考mulPlaneProsCalib.cpp的算法实现
"""

import numpy as np
import cv2
import os
import matplotlib.pyplot as plt
import pickle
import json
from typing import List, Tuple, Dict, Optional
import warnings
warnings.filterwarnings('ignore')

# 导入您的相位展开程序
from four_freq_phase_unwrap import five_step_phase, unwrap_multifrequency
from practical_phase_unwrap import PracticalPhaseUnwrapper


class PhaseHeightCalibrationFinal:
    """基于您的相位展开程序的最终标定类"""
    
    def __init__(self, calibration_data_folder: str = "标定数据"):
        """
        初始化标定器
        Args:
            calibration_data_folder: 标定数据文件夹路径
        """
        self.calibration_data_folder = calibration_data_folder
        
        # 投影仪配置：每个投影仪对应的图像编号范围
        self.projector_configs = {
            0: {"name": "Projector_0", "image_range": (1, 20)},
            1: {"name": "Projector_1", "image_range": (21, 40)},
            2: {"name": "Projector_2", "image_range": (41, 60)},
            3: {"name": "Projector_3", "image_range": (61, 80)}
        }
        
        # 频率配置：每个频率对应的5张相移图像
        self.frequency_configs = {
            8: {"image_indices": [1, 2, 3, 4, 5], "name": "8px_period"},      
            64: {"image_indices": [6, 7, 8, 9, 10], "name": "64px_period"},    
            512: {"image_indices": [11, 12, 13, 14, 15], "name": "512px_period"}, 
            1080: {"image_indices": [16, 17, 18, 19, 20], "name": "1080px_period"} 
        }
        
        # 标定参数
        self.height_step = 1.0  # mm，每次移动1mm
        self.num_positions = 11  # Test-0 到 Test-10
        self.fit_degree = 3  # 多项式拟合度数，参考C++程序默认值
        
        # 创建相位展开器
        self.practical_unwrapper = PracticalPhaseUnwrapper(periods=[1080.0, 512.0, 64.0, 8.0])
        
        # 结果存储
        self.calibration_results = {}
        self.mapping_tables = {}  # 存储映射表，类似C++程序中的m_dlps_mapping_table
        
    def load_phase_shift_images(self, test_folder: str, projector_id: int) -> Dict[int, List[np.ndarray]]:
        """
        加载指定投影仪的所有频率相移图像
        Args:
            test_folder: 测试文件夹路径
            projector_id: 投影仪ID (0-3)
        Returns:
            images_by_frequency: 按频率组织的图像字典
        """
        start_idx, _ = self.projector_configs[projector_id]["image_range"]
        images_by_frequency = {}
        
        for frequency, config in self.frequency_configs.items():
            frequency_images = []
            for img_idx in config["image_indices"]:
                actual_img_idx = start_idx + img_idx - 1
                img_path = os.path.join(test_folder, f"{actual_img_idx}.bmp")
                
                if os.path.exists(img_path):
                    img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
                    if img is not None:
                        frequency_images.append(img.astype(np.float64))  # 使用double精度，参考C++程序
                    else:
                        print(f"警告: 无法读取图像 {img_path}")
                        return None
                else:
                    print(f"警告: 图像文件不存在 {img_path}")
                    return None
            
            if len(frequency_images) == 5:
                images_by_frequency[frequency] = frequency_images
            else:
                print(f"警告: 频率 {frequency} 的图像数量不足")
                return None
        
        return images_by_frequency
    
    def process_single_position_unwrapping(self, test_folder: str, projector_id: int, 
                                         use_four_freq: bool = True) -> Optional[np.ndarray]:
        """
        处理单个位置的相位展开
        Args:
            test_folder: 测试文件夹路径
            projector_id: 投影仪ID
            use_four_freq: 是否使用四频展开（True）或实用展开（False）
        Returns:
            unwrapped_phase: 展开的绝对相位图
        """
        try:
            print(f"    使用{'四频' if use_four_freq else '实用'}相位展开处理...")
            
            # 加载图像
            images_by_frequency = self.load_phase_shift_images(test_folder, projector_id)
            
            if images_by_frequency is None:
                print(f"    图像加载失败")
                return None
            
            if use_four_freq:
                # 使用四频相位展开
                # 准备四个频率的图像组
                frames_list = []
                for freq in [1080, 512, 64, 8]:  # 按照从低频到高频的顺序
                    if freq in images_by_frequency:
                        # 转换为numpy数组，形状为 (height, width, 5)
                        freq_images = images_by_frequency[freq]  # 列表包含5张图像
                        # 将5张图像堆叠成 (height, width, 5) 的格式
                        freq_frames = np.stack(freq_images, axis=-1)
                        frames_list.append(freq_frames)
                    else:
                        print(f"    缺少频率 {freq} 的图像")
                        return None

                # 调用四频相位展开
                try:
                    # 使用unwrap_multifrequency函数
                    periods = [1080.0, 512.0, 64.0, 8.0]

                    # 计算各频率的包裹相位
                    wrapped_phases = []
                    for frames in frames_list:
                        wrapped_phase = five_step_phase(frames)
                        wrapped_phases.append(wrapped_phase)

                    # 进行多频展开
                    unwrapped_phases_list = unwrap_multifrequency(wrapped_phases, periods)
                    # 取最后一个（最高频的）展开相位
                    unwrapped_phase = unwrapped_phases_list[-1]

                except Exception as e:
                    print(f"    四频相位展开失败: {e}")
                    return None

            else:
                # 使用实用相位展开
                # 准备四个频率的图像组
                frames_list = []
                for freq in [1080, 512, 64, 8]:  # 按照从低频到高频的顺序
                    if freq in images_by_frequency:
                        # 转换为numpy数组，形状为 (height, width, 5)
                        freq_images = images_by_frequency[freq]  # 列表包含5张图像
                        freq_frames = np.stack(freq_images, axis=-1)
                        frames_list.append(freq_frames)
                    else:
                        print(f"    缺少频率 {freq} 的图像")
                        return None

                try:
                    # 使用实用相位展开器
                    unwrapped_phase, info = self.practical_unwrapper.unwrap_four_frequency_practical(frames_list)

                    # 打印处理信息
                    self.practical_unwrapper.print_info(info)

                except Exception as e:
                    print(f"    实用相位展开失败: {e}")
                    return None
            
            if unwrapped_phase is not None:
                # 输出统计信息
                valid_mask = ~np.isnan(unwrapped_phase)
                if np.any(valid_mask):
                    valid_phase = unwrapped_phase[valid_mask]
                    print(f"    展开相位范围: [{np.min(valid_phase):.3f}, {np.max(valid_phase):.3f}] rad")
                    print(f"    展开相位均值: {np.mean(valid_phase):.3f} rad")
                    print(f"    有效像素比例: {np.sum(valid_mask) / unwrapped_phase.size * 100:.1f}%")
                else:
                    print(f"    没有有效的展开相位")
                    return None
            
            return unwrapped_phase
            
        except Exception as e:
            print(f"    相位展开处理失败: {e}")
            return None
    
    def collect_unwrapped_phases(self, projector_id: int, use_four_freq: bool = True) -> Tuple[List[np.ndarray], List[float]]:
        """
        收集所有位置的展开相位数据
        Args:
            projector_id: 投影仪ID
            use_four_freq: 是否使用四频展开
        Returns:
            unwrapped_phases: 展开相位图列表
            heights: 对应的高度列表
        """
        print(f"正在收集投影仪 {projector_id} 的展开相位数据...")
        print(f"使用{'四频' if use_four_freq else '实用'}相位展开算法")
        
        unwrapped_phases = []
        heights = []
        
        for position in range(self.num_positions):
            test_folder = os.path.join(self.calibration_data_folder, f"Test-{position}")
            
            if not os.path.exists(test_folder):
                print(f"警告: 文件夹不存在 {test_folder}")
                continue
            
            print(f"  处理位置 {position} (高度 {position * self.height_step:.1f} mm)...")
            
            # 使用相位展开处理
            unwrapped_phase = self.process_single_position_unwrapping(test_folder, projector_id, use_four_freq)
            
            if unwrapped_phase is not None:
                unwrapped_phases.append(unwrapped_phase)
                heights.append(position * self.height_step)
            else:
                print(f"    跳过位置 {position}（处理失败）")
        
        print(f"成功收集到 {len(unwrapped_phases)} 个位置的展开相位数据")
        return unwrapped_phases, heights
    
    def create_mapping_table(self, unwrapped_phases: List[np.ndarray], heights: List[float], 
                           projector_id: int) -> List[np.ndarray]:
        """
        创建映射表，参考C++程序的__creatMappingTable方法
        Args:
            unwrapped_phases: 展开相位图列表
            heights: 对应的高度列表
            projector_id: 投影仪ID
        Returns:
            mapping_table: 映射表列表，每个元素对应一个多项式系数图
        """
        print(f"为投影仪 {projector_id} 创建映射表...")
        print(f"拟合度数: {self.fit_degree}")
        
        if len(unwrapped_phases) == 0:
            print("没有有效的展开相位数据")
            return []
        
        # 获取图像尺寸
        r_num, c_num = unwrapped_phases[0].shape
        n_plane_count = len(unwrapped_phases)
        
        # 确保拟合度数至少为2
        if self.fit_degree < 2:
            self.fit_degree = 2
        
        # 初始化映射表
        mapping_table = []
        for d in range(self.fit_degree):
            mapping_table.append(np.zeros((r_num, c_num), dtype=np.float64))
        
        # 构建高度向量b
        b = np.array(heights, dtype=np.float64).reshape(-1, 1)
        
        print(f"开始逐像素拟合 ({r_num}x{c_num} = {r_num*c_num} 个像素)...")
        
        # 逐像素进行多项式拟合
        total_pixels = r_num * c_num
        processed_pixels = 0
        
        for p in range(total_pixels):
            row = p // c_num
            col = p % c_num
            
            # 构建设计矩阵A
            A = np.zeros((n_plane_count, self.fit_degree), dtype=np.float64)
            
            # 检查该像素在所有位置是否都有有效相位
            valid_pixel = True
            for n in range(n_plane_count):
                phase_value = unwrapped_phases[n][row, col]
                if np.isnan(phase_value) or np.isinf(phase_value):
                    valid_pixel = False
                    break
                
                # 构建多项式基函数 [1, φ, φ², φ³, ...]
                for d in range(self.fit_degree):
                    A[n, d] = np.power(phase_value, d)
            
            if valid_pixel:
                try:
                    # 使用SVD求解最小二乘问题
                    coeffs, residuals, rank, s = np.linalg.lstsq(A, b.flatten(), rcond=None)
                    
                    # 将系数存储到映射表中
                    for d in range(self.fit_degree):
                        mapping_table[d][row, col] = coeffs[d]
                        
                except np.linalg.LinAlgError:
                    # 如果求解失败，设置为NaN
                    for d in range(self.fit_degree):
                        mapping_table[d][row, col] = np.nan
            else:
                # 无效像素设置为NaN
                for d in range(self.fit_degree):
                    mapping_table[d][row, col] = np.nan
            
            processed_pixels += 1
            if processed_pixels % 10000 == 0:
                progress = processed_pixels / total_pixels * 100
                print(f"  进度: {progress:.1f}% ({processed_pixels}/{total_pixels})")
        
        print(f"映射表创建完成")
        
        # 统计有效像素
        valid_pixels = np.sum(~np.isnan(mapping_table[0]))
        total_pixels = mapping_table[0].size
        valid_ratio = valid_pixels / total_pixels * 100
        print(f"有效像素比例: {valid_ratio:.1f}% ({valid_pixels}/{total_pixels})")
        
        return mapping_table
    
    def predict_height_from_phase(self, phase_map: np.ndarray, projector_id: int) -> np.ndarray:
        """
        使用映射表从相位预测高度
        Args:
            phase_map: 相位图
            projector_id: 投影仪ID
        Returns:
            height_map: 高度图
        """
        if projector_id not in self.mapping_tables:
            raise ValueError(f"投影仪 {projector_id} 未标定")
        
        mapping_table = self.mapping_tables[projector_id]
        height_map = np.zeros_like(phase_map, dtype=np.float64)
        
        # 使用多项式计算高度: h = c0 + c1*φ + c2*φ² + c3*φ³ + ...
        for d in range(len(mapping_table)):
            coeff_map = mapping_table[d]
            height_map += coeff_map * np.power(phase_map, d)
        
        # 将无效区域设置为NaN
        invalid_mask = np.isnan(mapping_table[0])
        height_map[invalid_mask] = np.nan
        
        return height_map
    
    def evaluate_mapping_quality(self, unwrapped_phases: List[np.ndarray], heights: List[float], 
                                mapping_table: List[np.ndarray]) -> Dict:
        """
        评估映射表质量
        Args:
            unwrapped_phases: 展开相位图列表
            heights: 真实高度列表
            mapping_table: 映射表
        Returns:
            quality_metrics: 质量指标字典
        """
        print("评估映射表质量...")
        
        all_errors = []
        position_errors = []
        
        for i, (phase_map, true_height) in enumerate(zip(unwrapped_phases, heights)):
            # 使用映射表预测高度
            predicted_height_map = np.zeros_like(phase_map, dtype=np.float64)
            
            for d in range(len(mapping_table)):
                coeff_map = mapping_table[d]
                predicted_height_map += coeff_map * np.power(phase_map, d)
            
            # 计算误差
            valid_mask = ~np.isnan(predicted_height_map) & ~np.isnan(phase_map) & ~np.isnan(mapping_table[0])
            
            if np.any(valid_mask):
                errors = np.abs(predicted_height_map[valid_mask] - true_height)
                all_errors.extend(errors)
                
                mean_error = np.mean(errors)
                std_error = np.std(errors)
                max_error = np.max(errors)
                
                position_errors.append({
                    'position': i,
                    'true_height': true_height,
                    'mean_error': mean_error,
                    'std_error': std_error,
                    'max_error': max_error,
                    'valid_pixels': np.sum(valid_mask)
                })
                
                print(f"  位置 {i} (高度 {true_height}mm): 平均误差 {mean_error:.3f}mm, 标准差 {std_error:.3f}mm")
        
        if len(all_errors) > 0:
            all_errors = np.array(all_errors)
            
            quality_metrics = {
                'overall_mean_error': np.mean(all_errors),
                'overall_std_error': np.std(all_errors),
                'overall_max_error': np.max(all_errors),
                'overall_rmse': np.sqrt(np.mean(all_errors**2)),
                'position_errors': position_errors,
                'total_valid_pixels': len(all_errors)
            }
            
            print(f"整体质量评估:")
            print(f"  平均误差: {quality_metrics['overall_mean_error']:.3f} mm")
            print(f"  标准差: {quality_metrics['overall_std_error']:.3f} mm")
            print(f"  RMSE: {quality_metrics['overall_rmse']:.3f} mm")
            print(f"  最大误差: {quality_metrics['overall_max_error']:.3f} mm")
            
            return quality_metrics
        else:
            print("没有有效数据进行质量评估")
            return None

    def calibrate_single_projector(self, projector_id: int, use_four_freq: bool = True) -> Dict:
        """
        标定单个投影仪
        Args:
            projector_id: 投影仪ID
            use_four_freq: 是否使用四频展开
        Returns:
            calibration_result: 标定结果字典
        """
        print(f"\n=== 开始标定投影仪 {projector_id} ===")
        print(f"使用{'四频' if use_four_freq else '实用'}相位展开算法")

        # 收集展开相位数据
        unwrapped_phases, heights = self.collect_unwrapped_phases(projector_id, use_four_freq)

        if len(unwrapped_phases) == 0:
            print(f"投影仪 {projector_id} 没有有效数据，跳过标定")
            return None

        # 创建映射表
        mapping_table = self.create_mapping_table(unwrapped_phases, heights, projector_id)

        if len(mapping_table) == 0:
            print(f"投影仪 {projector_id} 映射表创建失败")
            return None

        # 评估映射质量
        quality_metrics = self.evaluate_mapping_quality(unwrapped_phases, heights, mapping_table)

        # 保存映射表
        self.mapping_tables[projector_id] = mapping_table

        # 保存标定结果
        calibration_result = {
            'projector_id': projector_id,
            'projector_name': self.projector_configs[projector_id]["name"],
            'use_four_freq': use_four_freq,
            'fit_degree': self.fit_degree,
            'mapping_table': mapping_table,
            'unwrapped_phases': unwrapped_phases,
            'heights': heights,
            'quality_metrics': quality_metrics,
            'num_positions': len(heights),
            'method': 'polynomial_mapping'
        }

        print(f"\n投影仪 {projector_id} 标定完成!")
        if quality_metrics:
            print(f"标定质量: RMSE = {quality_metrics['overall_rmse']:.3f} mm")

        return calibration_result

    def calibrate_all_projectors(self, use_four_freq: bool = True) -> Dict:
        """
        标定所有投影仪
        Args:
            use_four_freq: 是否使用四频展开
        Returns:
            all_results: 所有投影仪的标定结果
        """
        print("=== 开始标定所有投影仪 ===")

        all_results = {}

        for projector_id in range(4):
            result = self.calibrate_single_projector(projector_id, use_four_freq)
            if result is not None:
                all_results[projector_id] = result
                self.calibration_results[projector_id] = result

        print(f"\n标定完成，成功标定 {len(all_results)} 个投影仪")
        return all_results

    def save_mapping_tables(self, output_folder: str = "mapping_tables"):
        """
        保存映射表，参考C++程序的saveMappingTable方法
        Args:
            output_folder: 输出文件夹
        """
        os.makedirs(output_folder, exist_ok=True)

        print(f"保存映射表到 {output_folder}...")

        for projector_id, mapping_table in self.mapping_tables.items():
            print(f"  保存投影仪 {projector_id} 的映射表...")

            for degree_idx, coeff_map in enumerate(mapping_table):
                # 参考C++程序的命名格式: DLP-{projector_id}-{degree}.tiff
                filename = f"DLP-{projector_id}-{degree_idx}.tiff"
                filepath = os.path.join(output_folder, filename)

                # 转换为适合保存的格式
                if coeff_map.dtype == np.float64:
                    # 保存为32位浮点数以节省空间
                    save_map = coeff_map.astype(np.float32)
                else:
                    save_map = coeff_map

                cv2.imwrite(filepath, save_map)
                print(f"    保存: {filename}")

        print("映射表保存完成")

    def load_mapping_tables(self, input_folder: str = "mapping_tables"):
        """
        加载映射表
        Args:
            input_folder: 输入文件夹
        """
        print(f"从 {input_folder} 加载映射表...")

        self.mapping_tables = {}

        for projector_id in range(4):
            mapping_table = []

            for degree_idx in range(self.fit_degree):
                filename = f"DLP-{projector_id}-{degree_idx}.tiff"
                filepath = os.path.join(input_folder, filename)

                if os.path.exists(filepath):
                    coeff_map = cv2.imread(filepath, cv2.IMREAD_UNCHANGED)
                    if coeff_map is not None:
                        # 转换为double精度
                        coeff_map = coeff_map.astype(np.float64)
                        mapping_table.append(coeff_map)
                    else:
                        print(f"警告: 无法读取 {filepath}")
                        break
                else:
                    print(f"警告: 文件不存在 {filepath}")
                    break

            if len(mapping_table) == self.fit_degree:
                self.mapping_tables[projector_id] = mapping_table
                print(f"  成功加载投影仪 {projector_id} 的映射表")
            else:
                print(f"  投影仪 {projector_id} 的映射表加载失败")

        print(f"成功加载 {len(self.mapping_tables)} 个投影仪的映射表")

    def save_calibration_results(self, output_folder: str = "calibration_results_final"):
        """
        保存完整的标定结果
        Args:
            output_folder: 输出文件夹
        """
        os.makedirs(output_folder, exist_ok=True)

        # 保存映射表
        mapping_folder = os.path.join(output_folder, "mapping_tables")
        self.save_mapping_tables(mapping_folder)

        # 保存标定结果（不包含大数组，节省空间）
        simplified_results = {}
        for proj_id, result in self.calibration_results.items():
            simplified_results[proj_id] = {
                'projector_id': result['projector_id'],
                'projector_name': result['projector_name'],
                'use_four_freq': result['use_four_freq'],
                'fit_degree': result['fit_degree'],
                'quality_metrics': result['quality_metrics'],
                'num_positions': result['num_positions'],
                'method': result['method']
            }

        with open(os.path.join(output_folder, "calibration_results.pkl"), 'wb') as f:
            pickle.dump(simplified_results, f)

        # 保存质量报告
        quality_report = self.generate_quality_report()
        with open(os.path.join(output_folder, "quality_report.json"), 'w', encoding='utf-8') as f:
            json.dump(quality_report, f, indent=2, ensure_ascii=False)

        print(f"标定结果已保存到 {output_folder}")
        return quality_report

    def generate_quality_report(self) -> Dict:
        """生成质量报告"""
        if not self.calibration_results:
            return {"error": "没有标定结果"}

        quality_report = {
            "method": "polynomial_mapping",
            "fit_degree": self.fit_degree,
            "overall_quality": "Unknown",
            "projector_reports": {},
            "recommendations": []
        }

        all_rmse_values = []
        all_mean_errors = []

        for proj_id, result in self.calibration_results.items():
            if result['quality_metrics']:
                metrics = result['quality_metrics']
                rmse = metrics['overall_rmse']
                mean_error = metrics['overall_mean_error']

                all_rmse_values.append(rmse)
                all_mean_errors.append(mean_error)

                # 单个投影仪质量评估
                if rmse < 0.5 and mean_error < 0.3:
                    proj_quality = "Excellent"
                elif rmse < 1.0 and mean_error < 0.5:
                    proj_quality = "Good"
                elif rmse < 2.0 and mean_error < 1.0:
                    proj_quality = "Fair"
                else:
                    proj_quality = "Poor"

                quality_report["projector_reports"][proj_id] = {
                    "quality": proj_quality,
                    "rmse": rmse,
                    "mean_error": mean_error,
                    "std_error": metrics['overall_std_error'],
                    "max_error": metrics['overall_max_error'],
                    "valid_pixels": metrics['total_valid_pixels'],
                    "use_four_freq": result['use_four_freq']
                }

        # 整体质量评估
        if len(all_rmse_values) > 0:
            avg_rmse = np.mean(all_rmse_values)
            avg_mean_error = np.mean(all_mean_errors)

            if avg_rmse < 0.5 and avg_mean_error < 0.3:
                overall_quality = "Excellent"
            elif avg_rmse < 1.0 and avg_mean_error < 0.5:
                overall_quality = "Good"
            elif avg_rmse < 2.0 and avg_mean_error < 1.0:
                overall_quality = "Fair"
            else:
                overall_quality = "Poor"

            quality_report["overall_quality"] = overall_quality
            quality_report["average_rmse"] = avg_rmse
            quality_report["average_mean_error"] = avg_mean_error
            quality_report["num_calibrated_projectors"] = len(self.calibration_results)

            # 生成建议
            recommendations = []
            if avg_rmse < 0.5:
                recommendations.append("标定质量优秀，可以用于高精度测量")
            elif avg_rmse < 1.0:
                recommendations.append("标定质量良好，适合一般精度测量")
            else:
                recommendations.append("标定质量需要改进，建议检查数据质量或增加拟合度数")

            if avg_mean_error < 0.3:
                recommendations.append("平均误差优秀 (< 0.3mm)")
            elif avg_mean_error < 0.5:
                recommendations.append("平均误差良好 (< 0.5mm)")

            poor_projectors = [proj_id for proj_id, report in quality_report["projector_reports"].items()
                              if report["quality"] == "Poor"]
            if poor_projectors:
                recommendations.append(f"投影仪 {poor_projectors} 标定质量较差，建议重新采集数据")

            quality_report["recommendations"] = recommendations

        return quality_report
