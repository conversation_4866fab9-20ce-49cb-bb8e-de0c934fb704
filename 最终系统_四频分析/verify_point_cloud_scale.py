#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
点云尺度验证脚本
对比修正前后的点云坐标尺度差异
"""

import numpy as np
import re
from pathlib import Path

def read_ply_coordinates(ply_file: str, max_lines: int = 10000):
    """读取PLY文件的前N个点坐标"""
    coordinates = []
    
    with open(ply_file, 'r') as f:
        # 跳过文件头
        in_header = True
        for line in f:
            if line.strip() == "end_header":
                in_header = False
                continue
            
            if not in_header:
                parts = line.strip().split()
                if len(parts) >= 3:
                    try:
                        x, y, z = float(parts[0]), float(parts[1]), float(parts[2])
                        coordinates.append([x, y, z])
                        
                        if len(coordinates) >= max_lines:
                            break
                    except ValueError:
                        continue
    
    return np.array(coordinates)

def analyze_point_cloud_scale(ply_file: str, name: str):
    """分析点云的尺度"""
    print(f"\n分析 {name}:")
    print(f"文件: {ply_file}")
    
    coords = read_ply_coordinates(ply_file)
    
    if len(coords) == 0:
        print("  错误: 无法读取坐标数据")
        return None
    
    print(f"  采样点数: {len(coords):,}")
    
    # 计算坐标范围
    x_range = (np.min(coords[:, 0]), np.max(coords[:, 0]))
    y_range = (np.min(coords[:, 1]), np.max(coords[:, 1]))
    z_range = (np.min(coords[:, 2]), np.max(coords[:, 2]))
    
    print(f"  X坐标范围: {x_range[0]:.3f} ~ {x_range[1]:.3f} mm (跨度: {x_range[1]-x_range[0]:.3f} mm)")
    print(f"  Y坐标范围: {y_range[0]:.3f} ~ {y_range[1]:.3f} mm (跨度: {y_range[1]-y_range[0]:.3f} mm)")
    print(f"  Z坐标范围: {z_range[0]:.3f} ~ {z_range[1]:.3f} mm (跨度: {z_range[1]-z_range[0]:.3f} mm)")
    
    # 计算图像物理尺寸
    img_width_mm = x_range[1] - x_range[0]
    img_height_mm = y_range[1] - y_range[0]
    
    print(f"  图像物理尺寸: {img_width_mm:.1f} x {img_height_mm:.1f} mm")
    
    return {
        'coords': coords,
        'x_range': x_range,
        'y_range': y_range,
        'z_range': z_range,
        'img_width_mm': img_width_mm,
        'img_height_mm': img_height_mm
    }

def main():
    """主函数"""
    print("="*60)
    print("点云尺度验证分析")
    print("="*60)
    
    # 分析修正前的点云 (CCD_DX = 0.0048)
    old_ply = "final_fusion_results_20um/fused_point_cloud.ply"
    new_ply = "corrected_fusion_results/fused_point_cloud.ply"
    
    if not Path(old_ply).exists():
        print(f"错误: 找不到文件 {old_ply}")
        return
    
    if not Path(new_ply).exists():
        print(f"错误: 找不到文件 {new_ply}")
        return
    
    # 分析两个点云
    old_analysis = analyze_point_cloud_scale(old_ply, "修正前 (CCD_DX=0.0048)")
    new_analysis = analyze_point_cloud_scale(new_ply, "修正后 (CCD_DX=0.0154)")
    
    if old_analysis and new_analysis:
        print(f"\n{'='*60}")
        print("尺度对比分析")
        print(f"{'='*60}")
        
        # 计算尺度比例
        x_ratio = new_analysis['img_width_mm'] / old_analysis['img_width_mm']
        y_ratio = new_analysis['img_height_mm'] / old_analysis['img_height_mm']
        
        print(f"图像宽度比例: {x_ratio:.3f} (新/旧)")
        print(f"图像高度比例: {y_ratio:.3f} (新/旧)")
        print(f"理论比例: {0.0154/0.0048:.3f} (应该的比例)")
        
        # 分析系数修正的合理性
        expected_ratio = 0.0154 / 0.0048
        print(f"\n系数修正验证:")
        print(f"  期望比例: {expected_ratio:.3f}")
        print(f"  实际X比例: {x_ratio:.3f}")
        print(f"  实际Y比例: {y_ratio:.3f}")
        print(f"  差异: X={abs(x_ratio-expected_ratio):.3f}, Y={abs(y_ratio-expected_ratio):.3f}")
        
        if abs(x_ratio - expected_ratio) < 0.01 and abs(y_ratio - expected_ratio) < 0.01:
            print("  ✅ 系数修正成功！比例符合预期")
        else:
            print("  ❌ 系数修正可能有问题，比例不符合预期")
        
        # 分析图像尺寸合理性
        print(f"\n图像尺寸合理性分析:")
        print(f"  修正前图像尺寸: {old_analysis['img_width_mm']:.1f} x {old_analysis['img_height_mm']:.1f} mm")
        print(f"  修正后图像尺寸: {new_analysis['img_width_mm']:.1f} x {new_analysis['img_height_mm']:.1f} mm")
        
        # 根据2848x2848像素判断合理性
        pixel_count = 2848
        old_pixel_size = old_analysis['img_width_mm'] / pixel_count
        new_pixel_size = new_analysis['img_width_mm'] / pixel_count
        
        print(f"\n计算的像素物理尺寸:")
        print(f"  修正前: {old_pixel_size:.6f} mm/pixel")
        print(f"  修正后: {new_pixel_size:.6f} mm/pixel")
        print(f"  设定值前: 0.0048 mm/pixel")
        print(f"  设定值后: 0.0154 mm/pixel")
        
        if abs(new_pixel_size - 0.0154) < 0.001:
            print("  ✅ 修正后的像素尺寸与设定值一致")
        else:
            print("  ⚠️ 修正后的像素尺寸与设定值略有差异")

if __name__ == "__main__":
    main() 