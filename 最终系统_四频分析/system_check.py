#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
四频相位解包三维重建系统 - 完整性检查工具
用于验证系统所有组件是否正确安装和配置
"""

import os
import sys
import importlib

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 7:
        print(f"   ✅ Python {version.major}.{version.minor}.{version.micro} (兼容)")
        return True
    else:
        print(f"   ❌ Python {version.major}.{version.minor}.{version.micro} (需要Python 3.7+)")
        return False

def check_dependencies():
    """检查必要的Python依赖包"""
    print("\n📦 检查Python依赖包...")
    
    required_packages = {
        'numpy': 'NumPy (数值计算)',
        'cv2': 'OpenCV (图像处理)',
        'tifffile': 'TiffFile (TIFF图像读写)',
        'tqdm': 'TQDM (进度条)',
        'matplotlib': 'Matplotlib (可视化)',
        'scipy': 'SciPy (科学计算)',
        'pickle': 'Pickle (数据序列化)',
        'argparse': 'ArgParse (命令行参数)',
        'json': 'JSON (数据格式)',
        'glob': 'Glob (文件匹配)'
    }
    
    missing_packages = []
    
    for package, description in required_packages.items():
        try:
            if package == 'cv2':
                importlib.import_module('cv2')
            elif package == 'pickle':
                import pickle
            elif package == 'argparse':
                import argparse
            elif package == 'json':
                import json
            elif package == 'glob':
                import glob
            else:
                importlib.import_module(package)
            print(f"   ✅ {package} - {description}")
        except ImportError:
            print(f"   ❌ {package} - {description} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n💡 安装缺失的包:")
        if 'cv2' in missing_packages:
            print("   pip install opencv-python")
            missing_packages.remove('cv2')
        for pkg in missing_packages:
            if pkg not in ['pickle', 'argparse', 'json', 'glob']:
                print(f"   pip install {pkg}")
        return False
    
    return True

def check_system_files():
    """检查系统核心文件"""
    print("\n📁 检查系统核心文件...")
    
    required_files = {
        'phase_height_calibration_final.py': '最终标定系统',
        'run_final_calibration.py': '标定运行脚本',
        'fusion_with_final_calibration.py': '重建融合主程序',
        'four_freq_phase_unwrap.py': '四频相位解包算法',
        'verify_point_cloud_scale.py': '点云尺度验证工具',
        'generate_point_cloud.py': '点云生成参考程序'
    }
    
    missing_files = []
    
    for filename, description in required_files.items():
        if os.path.exists(filename):
            file_size = os.path.getsize(filename) / 1024  # KB
            print(f"   ✅ {filename} - {description} ({file_size:.1f}KB)")
        else:
            print(f"   ❌ {filename} - {description} (缺失)")
            missing_files.append(filename)
    
    return len(missing_files) == 0

def check_documentation():
    """检查文档文件"""
    print("\n📖 检查文档文件...")
    
    doc_files = {
        'README_系统使用说明.md': '系统使用说明',
        'README_最终标定系统.md': '标定系统说明',
        'corrected_datasets_fusion_summary.md': '数据处理总结',
        'CCD_DX_correction_report.md': 'CCD_DX修正报告'
    }
    
    for filename, description in doc_files.items():
        if os.path.exists(filename):
            print(f"   ✅ {filename} - {description}")
        else:
            print(f"   ⚠️  {filename} - {description} (可选文档)")

def check_reference_code():
    """检查C++参考代码"""
    print("\n⚙️ 检查C++参考代码...")
    
    ref_dir = "标定参考程序"
    if os.path.exists(ref_dir):
        ref_files = ['main.cpp', 'mulPlaneProsCalib.cpp', 'mulPlaneProsCalib.h']
        all_present = True
        
        for filename in ref_files:
            filepath = os.path.join(ref_dir, filename)
            if os.path.exists(filepath):
                print(f"   ✅ {filename} - C++参考实现")
            else:
                print(f"   ❌ {filename} - C++参考实现 (缺失)")
                all_present = False
        
        if all_present:
            print("   💡 可用于理解标定算法原理")
        return all_present
    else:
        print(f"   ❌ {ref_dir}/ - C++参考代码目录 (缺失)")
        return False

def check_system_config():
    """检查系统配置"""
    print("\n⚙️ 检查系统配置...")
    
    try:
        # 检查fusion程序中的CCD_DX设置
        with open('fusion_with_final_calibration.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'self.CCD_DX = 0.0154' in content:
                print("   ✅ CCD_DX = 0.0154 mm/pixel (已修正)")
            elif 'CCD_DX' in content:
                print("   ⚠️  CCD_DX参数存在但值可能不正确")
            else:
                print("   ❌ 未找到CCD_DX参数配置")
        
        # 检查20微米检测功能
        if 'FINE_THRESHOLD = 0.02' in content or '20微米' in content:
            print("   ✅ 20微米精细异常检测 (已集成)")
        else:
            print("   ⚠️  20微米精细检测可能未完全集成")
            
        return True
    except Exception as e:
        print(f"   ❌ 配置检查失败: {e}")
        return False

def check_system_requirements():
    """检查系统要求"""
    print("\n💻 检查系统要求...")
    
    try:
        import psutil
        
        # 检查内存
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        if memory_gb >= 4:
            print(f"   ✅ 系统内存: {memory_gb:.1f}GB (充足)")
        else:
            print(f"   ⚠️  系统内存: {memory_gb:.1f}GB (推荐4GB+)")
        
        # 检查磁盘空间
        disk = psutil.disk_usage('.')
        free_gb = disk.free / (1024**3)
        if free_gb >= 5:
            print(f"   ✅ 可用磁盘空间: {free_gb:.1f}GB (充足)")
        else:
            print(f"   ⚠️  可用磁盘空间: {free_gb:.1f}GB (推荐5GB+)")
            
    except ImportError:
        print("   ℹ️  psutil未安装，跳过系统资源检查")
        print("   💡 可安装: pip install psutil")

def display_usage_guide():
    """显示使用指南"""
    print("\n🚀 系统使用指南:")
    print("   1. 系统标定:")
    print("      python run_final_calibration.py")
    print()
    print("   2. 数据重建与融合:")
    print("      python fusion_with_final_calibration.py \\")
    print("          --test_folder \"测试/pcb\" \\")
    print("          --calibration_folder \"final_calibration_results_four_freq\" \\")
    print("          --output_folder \"results\" \\")
    print("          --use_20um_detection")
    print()
    print("   3. 结果验证:")
    print("      python verify_point_cloud_scale.py")
    print()
    print("   📖 详细说明请参考: README_系统使用说明.md")

def main():
    """主检查函数"""
    print("=" * 60)
    print("🔍 四频相位解包三维重建系统 - 完整性检查")
    print("=" * 60)
    
    checks = [
        check_python_version(),
        check_dependencies(),
        check_system_files(),
        check_system_config()
    ]
    
    # 可选检查
    check_documentation()
    check_reference_code()
    check_system_requirements()
    
    print("\n" + "=" * 60)
    
    if all(checks):
        print("🎉 系统检查完成！所有核心组件正常")
        print("✨ 系统已准备就绪，可以开始使用")
        display_usage_guide()
    else:
        print("⚠️  系统检查发现问题，请修复后重新检查")
        print("💡 运行: python system_check.py")
    
    print("=" * 60)

if __name__ == "__main__":
    main() 