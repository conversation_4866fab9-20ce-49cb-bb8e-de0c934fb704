#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
相位高度映射标定程序
专门用于处理结构光三维测量的标定数据
"""

import numpy as np
import cv2
import os
import matplotlib.pyplot as plt
import pickle
import json
from typing import List, Tuple, Dict, Optional
from sklearn.linear_model import LinearRegression, RANSACRegressor
from sklearn.preprocessing import PolynomialFeatures
from sklearn.pipeline import Pipeline
from sklearn.metrics import r2_score, mean_squared_error
import warnings
warnings.filterwarnings('ignore')


class PhaseHeightMappingCalibration:
    """相位高度映射标定类"""
    
    def __init__(self, calibration_data_folder: str = "标定数据"):
        """
        初始化标定器
        Args:
            calibration_data_folder: 标定数据文件夹路径
        """
        self.calibration_data_folder = calibration_data_folder
        
        # 投影仪配置：每个投影仪对应的图像编号范围
        self.projector_configs = {
            0: {"name": "Projector_0", "image_range": (1, 20)},
            1: {"name": "Projector_1", "image_range": (21, 40)},
            2: {"name": "Projector_2", "image_range": (41, 60)},
            3: {"name": "Projector_3", "image_range": (61, 80)}
        }
        
        # 频率配置：每个频率对应的5张相移图像
        self.frequency_configs = {
            8: {"image_indices": [1, 2, 3, 4, 5], "name": "8px_period"},      
            64: {"image_indices": [6, 7, 8, 9, 10], "name": "64px_period"},    
            512: {"image_indices": [11, 12, 13, 14, 15], "name": "512px_period"}, 
            1080: {"image_indices": [16, 17, 18, 19, 20], "name": "1080px_period"} 
        }
        
        # 标定参数
        self.height_step = 1.0  # mm，每次移动1mm
        self.num_positions = 11  # Test-0 到 Test-10
        
        # 结果存储
        self.calibration_results = {}
        
    def load_phase_shift_images(self, test_folder: str, projector_id: int, frequency: int) -> List[np.ndarray]:
        """
        加载指定投影仪和频率的五步相移图像
        Args:
            test_folder: 测试文件夹路径
            projector_id: 投影仪ID (0-3)
            frequency: 频率（像素周期）
        Returns:
            images: 五张相移图像列表
        """
        start_idx, _ = self.projector_configs[projector_id]["image_range"]
        img_indices = self.frequency_configs[frequency]["image_indices"]
        
        images = []
        for img_idx in img_indices:
            actual_img_idx = start_idx + img_idx - 1
            img_path = os.path.join(test_folder, f"{actual_img_idx}.bmp")
            
            if os.path.exists(img_path):
                img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
                if img is not None:
                    images.append(img.astype(np.float32))
                else:
                    print(f"警告: 无法读取图像 {img_path}")
                    return None
            else:
                print(f"警告: 图像文件不存在 {img_path}")
                return None
        
        return images if len(images) == 5 else None
    
    def calculate_wrapped_phase(self, images: List[np.ndarray]) -> Tuple[np.ndarray, np.ndarray]:
        """
        使用五步相移算法计算包裹相位
        Args:
            images: 五张相移图像 [I0, I1, I2, I3, I4]
        Returns:
            wrapped_phase: 包裹相位 [-π, π]
            modulation: 调制度
        """
        if len(images) != 5:
            raise ValueError("需要5张相移图像")
        
        I1, I2, I3, I4, I5 = images
        
        # 五步相移算法
        # 相移步长为π/2: [0, π/2, π, 3π/2, 2π]
        numerator = 2 * (I2 - I4)
        denominator = 2 * I3 - I1 - I5
        
        # 避免除零
        denominator = np.where(np.abs(denominator) < 1e-10, 1e-10, denominator)
        
        # 计算包裹相位
        wrapped_phase = np.arctan2(numerator, denominator)
        
        # 计算调制度（用于质量评估）
        ac_component = np.sqrt(numerator**2 + denominator**2) / 2
        dc_component = (I1 + I2 + I3 + I4 + I5) / 5
        modulation = np.where(dc_component > 0, ac_component / dc_component, 0)
        
        return wrapped_phase, modulation
    
    def collect_calibration_data(self, projector_id: int, frequency: int) -> Tuple[List[np.ndarray], List[np.ndarray], List[float]]:
        """
        收集所有高度位置的标定数据
        Args:
            projector_id: 投影仪ID
            frequency: 频率（像素周期）
        Returns:
            phase_maps: 包裹相位图列表
            modulation_maps: 调制度图列表
            heights: 对应的高度列表
        """
        phase_maps = []
        modulation_maps = []
        heights = []
        
        print(f"正在收集投影仪 {projector_id} 频率 {frequency} 的标定数据...")
        
        for position in range(self.num_positions):
            test_folder = os.path.join(self.calibration_data_folder, f"Test-{position}")
            
            if not os.path.exists(test_folder):
                print(f"警告: 文件夹不存在 {test_folder}")
                continue
            
            print(f"  处理位置 {position} (高度 {position * self.height_step:.1f} mm)...")
            
            # 加载五步相移图像
            images = self.load_phase_shift_images(test_folder, projector_id, frequency)
            
            if images is not None:
                try:
                    # 计算包裹相位和调制度
                    wrapped_phase, modulation = self.calculate_wrapped_phase(images)
                    
                    phase_maps.append(wrapped_phase)
                    modulation_maps.append(modulation)
                    heights.append(position * self.height_step)
                    
                except Exception as e:
                    print(f"    处理位置 {position} 时出错: {e}")
                    continue
            else:
                print(f"    跳过位置 {position}（图像加载失败）")
        
        print(f"成功收集到 {len(phase_maps)} 个位置的数据")
        return phase_maps, modulation_maps, heights
    
    def extract_valid_phase_height_pairs(self, phase_maps: List[np.ndarray], 
                                       modulation_maps: List[np.ndarray],
                                       heights: List[float],
                                       min_modulation: float = 0.1,
                                       sample_ratio: float = 0.001) -> Tuple[np.ndarray, np.ndarray]:
        """
        提取有效的相位-高度数据对
        Args:
            phase_maps: 相位图列表
            modulation_maps: 调制度图列表
            heights: 高度列表
            min_modulation: 最小调制度阈值
            sample_ratio: 采样比例（减少数据量）
        Returns:
            phases: 相位值数组
            height_values: 对应的高度值数组
        """
        all_phases = []
        all_heights = []
        
        for phase_map, modulation_map, height in zip(phase_maps, modulation_maps, heights):
            # 创建质量掩码
            quality_mask = modulation_map > min_modulation
            
            # 提取有效相位
            valid_phases = phase_map[quality_mask]
            
            if len(valid_phases) > 0:
                # 随机采样以减少数据量
                n_samples = max(1000, int(len(valid_phases) * sample_ratio))
                if len(valid_phases) > n_samples:
                    indices = np.random.choice(len(valid_phases), n_samples, replace=False)
                    sampled_phases = valid_phases[indices]
                else:
                    sampled_phases = valid_phases
                
                all_phases.extend(sampled_phases)
                all_heights.extend([height] * len(sampled_phases))
        
        return np.array(all_phases), np.array(all_heights)
    
    def fit_phase_height_model(self, phases: np.ndarray, heights: np.ndarray, 
                             model_type: str = 'polynomial', degree: int = 2) -> Dict:
        """
        拟合相位-高度映射模型
        Args:
            phases: 相位值数组
            heights: 高度值数组
            model_type: 模型类型 ('linear', 'polynomial')
            degree: 多项式度数
        Returns:
            model_info: 模型信息字典
        """
        print(f"拟合{model_type}模型，数据点数: {len(phases)}")
        
        # 数据预处理：移除异常值
        phase_std = np.std(phases)
        phase_mean = np.mean(phases)
        valid_mask = np.abs(phases - phase_mean) < 3 * phase_std
        
        clean_phases = phases[valid_mask]
        clean_heights = heights[valid_mask]
        
        print(f"数据清理后: {len(clean_phases)} 个数据点")
        
        if len(clean_phases) < 100:
            print("警告: 有效数据点太少")
            return None
        
        # 构建模型
        if model_type == 'linear':
            model = RANSACRegressor(
                LinearRegression(),
                min_samples=50,
                residual_threshold=0.5,  # 0.5mm的残差阈值
                random_state=42
            )
            X = clean_phases.reshape(-1, 1)
        elif model_type == 'polynomial':
            base_model = Pipeline([
                ('poly', PolynomialFeatures(degree=degree)),
                ('linear', LinearRegression())
            ])
            model = RANSACRegressor(
                base_model,
                min_samples=100,
                residual_threshold=0.5,
                random_state=42
            )
            X = clean_phases.reshape(-1, 1)
        else:
            raise ValueError(f"不支持的模型类型: {model_type}")
        
        try:
            # 训练模型
            model.fit(X, clean_heights)
            
            # 预测和评估
            y_pred = model.predict(X)
            r2 = r2_score(clean_heights, y_pred)
            rmse = np.sqrt(mean_squared_error(clean_heights, y_pred))
            
            # 计算残差统计
            residuals = clean_heights - y_pred
            residual_std = np.std(residuals)
            
            # 计算相关系数
            correlation = np.corrcoef(clean_phases, clean_heights)[0, 1]
            
            model_info = {
                'model': model,
                'model_type': model_type,
                'degree': degree if model_type == 'polynomial' else 1,
                'r2_score': r2,
                'rmse': rmse,
                'residual_std': residual_std,
                'correlation': correlation,
                'n_samples': len(clean_phases),
                'phase_range': (np.min(clean_phases), np.max(clean_phases)),
                'height_range': (np.min(clean_heights), np.max(clean_heights)),
                'inlier_ratio': np.sum(model.inlier_mask_) / len(model.inlier_mask_)
            }
            
            print(f"模型拟合完成:")
            print(f"  相关系数: {correlation:.6f}")
            print(f"  R² 分数: {r2:.6f}")
            print(f"  RMSE: {rmse:.4f} mm")
            print(f"  内点比例: {model_info['inlier_ratio']:.3f}")
            
            return model_info
            
        except Exception as e:
            print(f"模型拟合失败: {e}")
            return None
    
    def calibrate_single_projector(self, projector_id: int, frequency: int = 8) -> Dict:
        """
        标定单个投影仪
        Args:
            projector_id: 投影仪ID
            frequency: 使用的频率（默认8像素周期，最敏感）
        Returns:
            calibration_result: 标定结果字典
        """
        print(f"\n=== 开始标定投影仪 {projector_id} (频率: {frequency}像素周期) ===")
        
        # 收集标定数据
        phase_maps, modulation_maps, heights = self.collect_calibration_data(projector_id, frequency)
        
        if len(phase_maps) == 0:
            print(f"投影仪 {projector_id} 没有有效数据，跳过标定")
            return None
        
        # 提取相位-高度对
        phases, height_values = self.extract_valid_phase_height_pairs(
            phase_maps, modulation_maps, heights)
        
        if len(phases) == 0:
            print(f"投影仪 {projector_id} 没有有效的相位-高度对，跳过标定")
            return None
        
        print(f"提取到 {len(phases)} 个有效的相位-高度对")
        
        # 尝试不同的模型
        models = [
            ('linear', 1),
            ('polynomial', 2),
            ('polynomial', 3)
        ]
        
        best_model = None
        best_score = -np.inf
        
        for model_type, degree in models:
            print(f"\n尝试 {model_type} 模型 (度数: {degree})...")
            model_info = self.fit_phase_height_model(phases, height_values, model_type, degree)
            
            if model_info is not None and model_info['r2_score'] > best_score:
                best_model = model_info
                best_score = model_info['r2_score']
        
        if best_model is None:
            print(f"投影仪 {projector_id} 所有模型拟合都失败")
            return None
        
        # 保存标定结果
        calibration_result = {
            'projector_id': projector_id,
            'projector_name': self.projector_configs[projector_id]["name"],
            'frequency': frequency,
            'frequency_name': self.frequency_configs[frequency]["name"],
            'model_info': best_model,
            'phase_maps': phase_maps,
            'modulation_maps': modulation_maps,
            'heights': heights,
            'calibration_phases': phases,
            'calibration_heights': height_values,
            'num_positions': len(heights),
            'total_pixels_used': len(phases)
        }
        
        print(f"\n投影仪 {projector_id} 标定完成!")
        print(f"最佳模型: {best_model['model_type']} (度数: {best_model['degree']})")
        print(f"最终性能: R² = {best_model['r2_score']:.6f}, RMSE = {best_model['rmse']:.4f} mm")
        
        return calibration_result

    def calibrate_all_projectors(self, frequency: int = 8) -> Dict:
        """
        标定所有投影仪
        Args:
            frequency: 使用的频率
        Returns:
            all_results: 所有投影仪的标定结果
        """
        print("=== 开始标定所有投影仪 ===")

        all_results = {}

        for projector_id in range(4):
            result = self.calibrate_single_projector(projector_id, frequency)
            if result is not None:
                all_results[projector_id] = result
                self.calibration_results[projector_id] = result

        print(f"\n标定完成，成功标定 {len(all_results)} 个投影仪")
        return all_results

    def predict_height_from_phase(self, phase_map: np.ndarray, projector_id: int) -> np.ndarray:
        """
        使用标定模型从相位图预测高度
        Args:
            phase_map: 相位图
            projector_id: 投影仪ID
        Returns:
            height_map: 高度图
        """
        if projector_id not in self.calibration_results:
            raise ValueError(f"投影仪 {projector_id} 未标定")

        model_info = self.calibration_results[projector_id]['model_info']
        model = model_info['model']

        # 创建输出高度图
        height_map = np.full_like(phase_map, np.nan)

        # 展平相位图
        flat_phases = phase_map.flatten()
        valid_mask = ~np.isnan(flat_phases)

        if np.any(valid_mask):
            valid_phases = flat_phases[valid_mask]
            X = valid_phases.reshape(-1, 1)

            # 预测高度
            predicted_heights = model.predict(X)

            # 重新整形并填入结果
            flat_heights = np.full_like(flat_phases, np.nan)
            flat_heights[valid_mask] = predicted_heights
            height_map = flat_heights.reshape(phase_map.shape)

        return height_map

    def evaluate_calibration_quality(self) -> Dict:
        """
        评估标定质量
        Returns:
            quality_report: 质量报告
        """
        if not self.calibration_results:
            return {"error": "没有标定结果"}

        quality_report = {
            "overall_quality": "Unknown",
            "projector_reports": {},
            "recommendations": []
        }

        all_r2_scores = []
        all_rmse_values = []

        for proj_id, result in self.calibration_results.items():
            model_info = result['model_info']
            r2 = model_info['r2_score']
            rmse = model_info['rmse']
            correlation = model_info['correlation']

            all_r2_scores.append(r2)
            all_rmse_values.append(rmse)

            # 单个投影仪质量评估
            if r2 > 0.95 and rmse < 0.2 and abs(correlation) > 0.8:
                proj_quality = "Excellent"
            elif r2 > 0.85 and rmse < 0.5 and abs(correlation) > 0.6:
                proj_quality = "Good"
            elif r2 > 0.7 and rmse < 1.0 and abs(correlation) > 0.4:
                proj_quality = "Fair"
            else:
                proj_quality = "Poor"

            quality_report["projector_reports"][proj_id] = {
                "quality": proj_quality,
                "r2_score": r2,
                "rmse": rmse,
                "correlation": correlation,
                "model_type": model_info['model_type'],
                "sample_count": model_info['n_samples'],
                "inlier_ratio": model_info['inlier_ratio']
            }

        # 整体质量评估
        avg_r2 = np.mean(all_r2_scores)
        avg_rmse = np.mean(all_rmse_values)

        if avg_r2 > 0.95 and avg_rmse < 0.2:
            overall_quality = "Excellent"
        elif avg_r2 > 0.85 and avg_rmse < 0.5:
            overall_quality = "Good"
        elif avg_r2 > 0.7 and avg_rmse < 1.0:
            overall_quality = "Fair"
        else:
            overall_quality = "Poor"

        quality_report["overall_quality"] = overall_quality
        quality_report["average_r2"] = avg_r2
        quality_report["average_rmse"] = avg_rmse
        quality_report["num_calibrated_projectors"] = len(self.calibration_results)

        # 生成建议
        recommendations = []

        if avg_r2 < 0.8:
            recommendations.append("R²分数较低，建议检查数据质量或尝试更高阶的多项式模型")

        if avg_rmse > 0.5:
            recommendations.append("RMSE较高，建议检查标定数据的一致性和测量精度")

        poor_projectors = [proj_id for proj_id, report in quality_report["projector_reports"].items()
                          if report["quality"] == "Poor"]
        if poor_projectors:
            recommendations.append(f"投影仪 {poor_projectors} 标定质量较差，建议重新采集数据")

        low_correlation_projectors = [proj_id for proj_id, report in quality_report["projector_reports"].items()
                                    if abs(report["correlation"]) < 0.5]
        if low_correlation_projectors:
            recommendations.append(f"投影仪 {low_correlation_projectors} 相位-高度相关性较低，检查相位计算是否正确")

        if not recommendations:
            recommendations.append("标定质量良好，可以用于实际测量")

        quality_report["recommendations"] = recommendations

        return quality_report

    def save_calibration_results(self, output_folder: str = "calibration_results"):
        """
        保存标定结果
        Args:
            output_folder: 输出文件夹
        """
        os.makedirs(output_folder, exist_ok=True)

        # 保存完整的标定结果
        with open(os.path.join(output_folder, "calibration_results.pkl"), 'wb') as f:
            pickle.dump(self.calibration_results, f)

        # 保存简化的模型文件（仅包含模型，用于实际应用）
        simplified_results = {}
        for proj_id, result in self.calibration_results.items():
            model_info = result['model_info']
            simplified_results[proj_id] = {
                'model': model_info['model'],
                'model_type': model_info['model_type'],
                'degree': model_info['degree'],
                'phase_range': model_info['phase_range'],
                'height_range': model_info['height_range'],
                'projector_name': result['projector_name'],
                'frequency': result['frequency']
            }

        with open(os.path.join(output_folder, "calibration_models.pkl"), 'wb') as f:
            pickle.dump(simplified_results, f)

        # 保存质量报告
        quality_report = self.evaluate_calibration_quality()
        with open(os.path.join(output_folder, "quality_report.json"), 'w', encoding='utf-8') as f:
            json.dump(quality_report, f, indent=2, ensure_ascii=False)

        print(f"标定结果已保存到 {output_folder}")
        return quality_report

    def visualize_calibration_results(self, output_folder: str = "calibration_results"):
        """
        可视化标定结果
        Args:
            output_folder: 输出文件夹
        """
        if not self.calibration_results:
            print("没有标定结果可视化")
            return

        os.makedirs(output_folder, exist_ok=True)

        # 为每个投影仪创建可视化
        for proj_id, result in self.calibration_results.items():
            self._plot_single_projector_results(result, output_folder)

        # 创建总结图
        self._plot_calibration_summary(output_folder)

    def _plot_single_projector_results(self, result: Dict, output_folder: str):
        """绘制单个投影仪的标定结果"""
        proj_id = result['projector_id']
        model_info = result['model_info']
        phases = result['calibration_phases']
        heights = result['calibration_heights']

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'Projector {proj_id} Calibration Results', fontsize=16)

        # 1. 散点图和拟合曲线
        ax1 = axes[0, 0]

        # 随机采样显示点（如果数据太多）
        if len(phases) > 5000:
            indices = np.random.choice(len(phases), 5000, replace=False)
            sample_phases = phases[indices]
            sample_heights = heights[indices]
        else:
            sample_phases = phases
            sample_heights = heights

        ax1.scatter(sample_phases, sample_heights, alpha=0.3, s=1, c='blue', label='Data points')

        # 绘制拟合曲线
        phase_range = np.linspace(model_info['phase_range'][0], model_info['phase_range'][1], 1000)
        X_plot = phase_range.reshape(-1, 1)
        height_pred = model_info['model'].predict(X_plot)
        ax1.plot(phase_range, height_pred, 'r-', linewidth=2, label='Fitted curve')

        ax1.set_xlabel('Phase (rad)')
        ax1.set_ylabel('Height (mm)')
        ax1.set_title('Phase-Height Relationship')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 残差图
        ax2 = axes[0, 1]
        X_residual = sample_phases.reshape(-1, 1)
        height_pred_sample = model_info['model'].predict(X_residual)
        residuals = sample_heights - height_pred_sample

        ax2.scatter(sample_phases, residuals, alpha=0.3, s=1, c='green')
        ax2.axhline(y=0, color='r', linestyle='--')
        ax2.set_xlabel('Phase (rad)')
        ax2.set_ylabel('Residuals (mm)')
        ax2.set_title('Residuals vs Phase')
        ax2.grid(True, alpha=0.3)

        # 3. 残差直方图
        ax3 = axes[1, 0]
        ax3.hist(residuals, bins=50, alpha=0.7, color='orange', edgecolor='black')
        ax3.axvline(x=0, color='r', linestyle='--')
        ax3.set_xlabel('Residuals (mm)')
        ax3.set_ylabel('Frequency')
        ax3.set_title('Residuals Distribution')
        ax3.grid(True, alpha=0.3)

        # 4. 统计信息
        ax4 = axes[1, 1]
        ax4.axis('off')

        stats_text = f"""
        Projector: {proj_id}
        Frequency: {result['frequency']} pixels
        Model Type: {model_info['model_type']}
        Degree: {model_info['degree']}

        Performance Metrics:
        R² Score: {model_info['r2_score']:.6f}
        RMSE: {model_info['rmse']:.4f} mm
        Correlation: {model_info['correlation']:.6f}
        Residual Std: {model_info['residual_std']:.4f} mm

        Data Statistics:
        Sample Count: {model_info['n_samples']:,}
        Inlier Ratio: {model_info['inlier_ratio']:.3f}
        Phase Range: [{model_info['phase_range'][0]:.3f}, {model_info['phase_range'][1]:.3f}] rad
        Height Range: [{model_info['height_range'][0]:.1f}, {model_info['height_range'][1]:.1f}] mm
        """

        ax4.text(0.05, 0.95, stats_text, transform=ax4.transAxes, fontsize=10,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))

        plt.tight_layout()
        plt.savefig(os.path.join(output_folder, f'projector_{proj_id}_calibration.png'),
                   dpi=150, bbox_inches='tight')
        plt.close()

    def _plot_calibration_summary(self, output_folder: str):
        """绘制标定总结图"""
        if not self.calibration_results:
            return

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Phase-Height Calibration Summary', fontsize=16)

        # 收集统计数据
        proj_ids = []
        r2_scores = []
        rmse_values = []
        correlations = []

        for proj_id, result in self.calibration_results.items():
            model_info = result['model_info']
            proj_ids.append(f'Proj {proj_id}')
            r2_scores.append(model_info['r2_score'])
            rmse_values.append(model_info['rmse'])
            correlations.append(abs(model_info['correlation']))

        # 1. R²分数对比
        ax1 = axes[0, 0]
        bars1 = ax1.bar(proj_ids, r2_scores, color='skyblue', edgecolor='navy')
        ax1.set_ylabel('R² Score')
        ax1.set_title('R² Score Comparison')
        ax1.set_ylim(0, 1)
        ax1.grid(True, alpha=0.3)

        for bar, score in zip(bars1, r2_scores):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{score:.3f}', ha='center', va='bottom')

        # 2. RMSE对比
        ax2 = axes[0, 1]
        bars2 = ax2.bar(proj_ids, rmse_values, color='lightcoral', edgecolor='darkred')
        ax2.set_ylabel('RMSE (mm)')
        ax2.set_title('RMSE Comparison')
        ax2.grid(True, alpha=0.3)

        for bar, rmse in zip(bars2, rmse_values):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(rmse_values)*0.01,
                    f'{rmse:.3f}', ha='center', va='bottom')

        # 3. 相关系数对比
        ax3 = axes[1, 0]
        bars3 = ax3.bar(proj_ids, correlations, color='lightgreen', edgecolor='darkgreen')
        ax3.set_ylabel('|Correlation|')
        ax3.set_title('Phase-Height Correlation')
        ax3.set_ylim(0, 1)
        ax3.grid(True, alpha=0.3)

        for bar, corr in zip(bars3, correlations):
            ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{corr:.3f}', ha='center', va='bottom')

        # 4. 总体统计
        ax4 = axes[1, 1]
        ax4.axis('off')

        avg_r2 = np.mean(r2_scores)
        avg_rmse = np.mean(rmse_values)
        avg_corr = np.mean(correlations)

        summary_text = f"""
        Calibration Summary:

        Calibrated Projectors: {len(self.calibration_results)}

        Average Performance:
        R² Score: {avg_r2:.6f}
        RMSE: {avg_rmse:.4f} mm
        |Correlation|: {avg_corr:.6f}

        Quality Assessment:
        {'Excellent' if avg_r2 > 0.95 and avg_rmse < 0.2 else
         'Good' if avg_r2 > 0.85 and avg_rmse < 0.5 else
         'Fair' if avg_r2 > 0.7 and avg_rmse < 1.0 else 'Poor'}

        Recommendations:
        - R² > 0.95: Excellent
        - R² > 0.85: Good
        - R² > 0.70: Fair
        - RMSE < 0.2mm: Excellent
        - RMSE < 0.5mm: Good
        """

        ax4.text(0.05, 0.95, summary_text, transform=ax4.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))

        plt.tight_layout()
        plt.savefig(os.path.join(output_folder, 'calibration_summary.png'),
                   dpi=150, bbox_inches='tight')
        plt.close()
