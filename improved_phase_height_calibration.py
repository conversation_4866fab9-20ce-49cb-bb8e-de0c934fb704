#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的相位高度标定程序
针对标定数据的特点进行优化
"""

import numpy as np
import cv2
import os
import matplotlib.pyplot as plt
import pickle
from typing import List, Tuple, Dict, Optional
from scipy import interpolate
from sklearn.linear_model import LinearRegression, RANSACRegressor
from sklearn.preprocessing import PolynomialFeatures
from sklearn.pipeline import Pipeline
from sklearn.metrics import r2_score, mean_squared_error
import warnings
warnings.filterwarnings('ignore')

from phase_unwrapping_four_frequency import FourFrequencyPhaseUnwrapping


class ImprovedPhaseHeightCalibration:
    """改进的相位高度标定类"""
    
    def __init__(self, calibration_data_folder: str = "标定数据"):
        """初始化标定器"""
        self.calibration_data_folder = calibration_data_folder
        self.pixel_periods = [1080, 512, 64, 8]  # 从低频到高频
        self.projector_configs = {
            0: {"name": "Projector_0", "image_range": (1, 20)},
            1: {"name": "Projector_1", "image_range": (21, 40)},
            2: {"name": "Projector_2", "image_range": (41, 60)},
            3: {"name": "Projector_3", "image_range": (61, 80)}
        }
        self.frequency_configs = {
            8: {"image_indices": [1, 2, 3, 4, 5]},      # 8像素周期
            64: {"image_indices": [6, 7, 8, 9, 10]},    # 64像素周期
            512: {"image_indices": [11, 12, 13, 14, 15]}, # 512像素周期
            1080: {"image_indices": [16, 17, 18, 19, 20]} # 1080像素周期
        }
        
        self.height_step = 1.0  # mm
        self.num_positions = 11
        self.phase_unwrapper = FourFrequencyPhaseUnwrapping(pixel_periods=self.pixel_periods)
        self.calibration_results = {}
        
    def load_calibration_images(self, test_folder: str, projector_id: int) -> Dict[int, List[np.ndarray]]:
        """加载指定投影仪的标定图像"""
        images_by_frequency = {}
        start_idx, end_idx = self.projector_configs[projector_id]["image_range"]
        
        for period, config in self.frequency_configs.items():
            frequency_images = []
            for img_idx in config["image_indices"]:
                actual_img_idx = start_idx + img_idx - 1
                img_path = os.path.join(test_folder, f"{actual_img_idx}.bmp")
                
                if os.path.exists(img_path):
                    img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
                    if img is not None:
                        frequency_images.append(img.astype(np.float32))
                    else:
                        frequency_images.append(np.zeros((480, 640), dtype=np.float32))
                else:
                    frequency_images.append(np.zeros((480, 640), dtype=np.float32))
            
            images_by_frequency[period] = frequency_images
        return images_by_frequency
    
    def calculate_phase_difference_method(self, test_folder: str, projector_id: int, 
                                        reference_position: int = 0) -> Optional[np.ndarray]:
        """
        使用相位差方法计算相对相位变化
        这种方法更适合标定场景，因为我们关心的是相位随高度的变化
        """
        try:
            # 加载当前位置的图像
            current_images = self.load_calibration_images(test_folder, projector_id)
            current_images_array = self.convert_to_phase_unwrapper_format(current_images)
            
            # 加载参考位置的图像
            ref_folder = os.path.join(self.calibration_data_folder, f"Test-{reference_position}")
            ref_images = self.load_calibration_images(ref_folder, projector_id)
            ref_images_array = self.convert_to_phase_unwrapper_format(ref_images)
            
            # 计算包裹相位
            current_wrapped, current_mod = self.phase_unwrapper.calculate_all_wrapped_phases(current_images_array)
            ref_wrapped, ref_mod = self.phase_unwrapper.calculate_all_wrapped_phases(ref_images_array)
            
            # 使用最高频率（最敏感）的相位进行计算
            high_freq_idx = -1  # 8像素周期
            
            current_phase = current_wrapped[high_freq_idx]
            ref_phase = ref_wrapped[high_freq_idx]
            current_modulation = current_mod[high_freq_idx]
            ref_modulation = ref_mod[high_freq_idx]
            
            # 创建质量掩码
            quality_mask = (current_modulation > 0.1) & (ref_modulation > 0.1)
            
            # 计算相位差
            phase_diff = current_phase - ref_phase
            
            # 处理相位跳跃（-π到π的跳跃）
            phase_diff = np.angle(np.exp(1j * phase_diff))
            
            # 应用质量掩码
            phase_diff[~quality_mask] = np.nan
            
            return phase_diff
            
        except Exception as e:
            print(f"计算相位差时出错: {e}")
            return None
    
    def convert_to_phase_unwrapper_format(self, images_by_frequency: Dict[int, List[np.ndarray]]) -> np.ndarray:
        """转换图像格式"""
        ordered_images = []
        for period in self.pixel_periods:
            if period in images_by_frequency:
                ordered_images.append(images_by_frequency[period])
            else:
                h, w = 480, 640
                if ordered_images:
                    h, w = ordered_images[0][0].shape
                empty_images = [np.zeros((h, w), dtype=np.float32) for _ in range(5)]
                ordered_images.append(empty_images)
        return np.array(ordered_images)
    
    def collect_phase_difference_data(self, projector_id: int, reference_position: int = 0) -> Tuple[List[np.ndarray], List[float]]:
        """收集相位差数据"""
        phase_diff_maps = []
        heights = []
        
        print(f"正在收集投影仪 {projector_id} 的相位差数据（参考位置: {reference_position}）...")
        
        for position in range(self.num_positions):
            if position == reference_position:
                # 参考位置的相位差为0
                test_folder = os.path.join(self.calibration_data_folder, f"Test-{position}")
                if os.path.exists(test_folder):
                    # 获取图像尺寸
                    sample_images = self.load_calibration_images(test_folder, projector_id)
                    if sample_images:
                        sample_img = list(sample_images.values())[0][0]
                        zero_phase_diff = np.zeros_like(sample_img, dtype=np.float32)
                        phase_diff_maps.append(zero_phase_diff)
                        heights.append(position * self.height_step)
                continue
            
            test_folder = os.path.join(self.calibration_data_folder, f"Test-{position}")
            
            if not os.path.exists(test_folder):
                print(f"警告: 文件夹不存在 {test_folder}")
                continue
            
            print(f"  处理位置 {position} (高度 {position * self.height_step:.1f} mm)...")
            
            phase_diff = self.calculate_phase_difference_method(test_folder, projector_id, reference_position)
            
            if phase_diff is not None:
                phase_diff_maps.append(phase_diff)
                heights.append(position * self.height_step)
            else:
                print(f"    跳过位置 {position}（处理失败）")
        
        print(f"投影仪 {projector_id} 收集到 {len(phase_diff_maps)} 个有效位置的数据")
        return phase_diff_maps, heights
    
    def extract_valid_phase_height_pairs(self, phase_maps: List[np.ndarray], 
                                       heights: List[float], 
                                       sample_ratio: float = 0.01) -> Tuple[np.ndarray, np.ndarray]:
        """
        提取有效的相位-高度对，使用采样减少数据量
        """
        all_phases = []
        all_heights = []
        
        for phase_map, height in zip(phase_maps, heights):
            # 提取有效像素（非NaN）
            valid_mask = ~np.isnan(phase_map)
            valid_phases = phase_map[valid_mask]
            
            if len(valid_phases) > 0:
                # 随机采样以减少数据量
                n_samples = max(1000, int(len(valid_phases) * sample_ratio))
                if len(valid_phases) > n_samples:
                    indices = np.random.choice(len(valid_phases), n_samples, replace=False)
                    sampled_phases = valid_phases[indices]
                else:
                    sampled_phases = valid_phases
                
                all_phases.extend(sampled_phases)
                all_heights.extend([height] * len(sampled_phases))
        
        return np.array(all_phases), np.array(all_heights)
    
    def fit_robust_calibration_model(self, phases: np.ndarray, heights: np.ndarray) -> Dict:
        """
        使用鲁棒回归拟合标定模型
        """
        print(f"拟合鲁棒标定模型，数据点数: {len(phases)}")
        
        # 移除明显的异常值
        phase_std = np.std(phases)
        phase_mean = np.mean(phases)
        valid_mask = np.abs(phases - phase_mean) < 3 * phase_std
        
        clean_phases = phases[valid_mask]
        clean_heights = heights[valid_mask]
        
        print(f"数据清理后: {len(clean_phases)} 个数据点")
        
        if len(clean_phases) < 100:
            print("警告: 有效数据点太少")
            return None
        
        # 使用RANSAC回归提高鲁棒性
        X = clean_phases.reshape(-1, 1)
        
        # 尝试线性模型
        ransac_linear = RANSACRegressor(
            LinearRegression(),
            min_samples=50,
            residual_threshold=0.5,  # 0.5mm的残差阈值
            random_state=42
        )
        
        try:
            ransac_linear.fit(X, clean_heights)
            y_pred_linear = ransac_linear.predict(X)
            r2_linear = r2_score(clean_heights, y_pred_linear)
            rmse_linear = np.sqrt(mean_squared_error(clean_heights, y_pred_linear))
            
            print(f"线性模型 - R²: {r2_linear:.6f}, RMSE: {rmse_linear:.4f} mm")
            
            # 尝试二次多项式模型
            poly_features = PolynomialFeatures(degree=2)
            X_poly = poly_features.fit_transform(X)
            
            ransac_poly = RANSACRegressor(
                LinearRegression(),
                min_samples=100,
                residual_threshold=0.5,
                random_state=42
            )
            
            ransac_poly.fit(X_poly, clean_heights)
            y_pred_poly = ransac_poly.predict(X_poly)
            r2_poly = r2_score(clean_heights, y_pred_poly)
            rmse_poly = np.sqrt(mean_squared_error(clean_heights, y_pred_poly))
            
            print(f"多项式模型 - R²: {r2_poly:.6f}, RMSE: {rmse_poly:.4f} mm")
            
            # 选择更好的模型
            if r2_poly > r2_linear and rmse_poly < rmse_linear:
                print("选择多项式模型")
                model = Pipeline([
                    ('poly', PolynomialFeatures(degree=2)),
                    ('ransac', ransac_poly)
                ])
                model_type = 'polynomial'
                r2_score_final = r2_poly
                rmse_final = rmse_poly
                y_pred_final = y_pred_poly
            else:
                print("选择线性模型")
                model = ransac_linear
                model_type = 'linear'
                r2_score_final = r2_linear
                rmse_final = rmse_linear
                y_pred_final = y_pred_linear
            
            # 计算残差统计
            residuals = clean_heights - y_pred_final
            residual_std = np.std(residuals)
            
            model_info = {
                'model': model,
                'model_type': model_type,
                'r2_score': r2_score_final,
                'rmse': rmse_final,
                'residual_std': residual_std,
                'n_samples': len(clean_phases),
                'phase_range': (np.min(clean_phases), np.max(clean_phases)),
                'height_range': (np.min(clean_heights), np.max(clean_heights)),
                'inlier_mask': ransac_linear.inlier_mask_ if model_type == 'linear' else ransac_poly.inlier_mask_
            }
            
            return model_info
            
        except Exception as e:
            print(f"模型拟合失败: {e}")
            return None
    
    def calibrate_projector_improved(self, projector_id: int, reference_position: int = 0) -> Dict:
        """改进的投影仪标定方法"""
        print(f"\n=== 开始改进标定投影仪 {projector_id} ===")
        
        # 收集相位差数据
        phase_diff_maps, heights = self.collect_phase_difference_data(projector_id, reference_position)
        
        if len(phase_diff_maps) == 0:
            print(f"投影仪 {projector_id} 没有有效数据，跳过标定")
            return None
        
        # 提取相位-高度对
        phases, height_values = self.extract_valid_phase_height_pairs(phase_diff_maps, heights)
        
        if len(phases) == 0:
            print(f"投影仪 {projector_id} 没有有效的相位-高度对，跳过标定")
            return None
        
        print(f"提取到 {len(phases)} 个有效的相位-高度对")
        
        # 检查相位-高度相关性
        correlation = np.corrcoef(phases, height_values)[0, 1]
        print(f"相位-高度相关系数: {correlation:.6f}")
        
        if abs(correlation) < 0.1:
            print("警告: 相位与高度之间相关性很低")
        
        # 拟合鲁棒标定模型
        model_info = self.fit_robust_calibration_model(phases, height_values)
        
        if model_info is None:
            print(f"投影仪 {projector_id} 模型拟合失败")
            return None
        
        # 保存标定结果
        calibration_result = {
            'projector_id': projector_id,
            'projector_name': self.projector_configs[projector_id]["name"],
            'model_info': model_info,
            'phase_diff_maps': phase_diff_maps,
            'heights': heights,
            'calibration_phases': phases,
            'calibration_heights': height_values,
            'reference_position': reference_position,
            'correlation': correlation
        }
        
        # 打印标定结果
        print(f"改进标定完成:")
        print(f"  相位-高度相关系数: {correlation:.6f}")
        print(f"  R² 分数: {model_info['r2_score']:.6f}")
        print(f"  RMSE: {model_info['rmse']:.4f} mm")
        print(f"  残差标准差: {model_info['residual_std']:.4f} mm")
        print(f"  数据点数: {model_info['n_samples']}")
        print(f"  相位范围: [{model_info['phase_range'][0]:.3f}, {model_info['phase_range'][1]:.3f}] rad")
        
        return calibration_result


def test_improved_calibration():
    """测试改进的标定方法"""
    print("=== 测试改进的标定方法 ===")
    
    calibrator = ImprovedPhaseHeightCalibration()
    
    # 测试投影仪0
    result = calibrator.calibrate_projector_improved(0, reference_position=0)
    
    if result:
        print("改进标定测试成功!")
        
        # 保存结果
        calibrator.calibration_results[0] = result
        
        os.makedirs("improved_calibration_results", exist_ok=True)
        with open("improved_calibration_results/calibration_results.pkl", 'wb') as f:
            pickle.dump(calibrator.calibration_results, f)
        
        print("结果已保存到 improved_calibration_results/")
    else:
        print("改进标定测试失败")


if __name__ == "__main__":
    test_improved_calibration()
