#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
相位高度标定程序
使用四频五步相移数据进行相位到高度的映射标定
"""

import numpy as np
import cv2
import os
import matplotlib.pyplot as plt
import pickle
from typing import List, Tuple, Dict, Optional
from scipy import interpolate
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import PolynomialFeatures
from sklearn.pipeline import Pipeline
from sklearn.metrics import r2_score, mean_squared_error
import warnings
warnings.filterwarnings('ignore')

from phase_unwrapping_four_frequency import FourFrequencyPhaseUnwrapping


class PhaseHeightCalibration:
    """相位高度标定类"""
    
    def __init__(self, calibration_data_folder: str = "标定数据"):
        """
        初始化标定器
        Args:
            calibration_data_folder: 标定数据文件夹路径
        """
        self.calibration_data_folder = calibration_data_folder
        self.pixel_periods = [1080, 512, 64, 8]  # 从低频到高频
        self.projector_configs = {
            0: {"name": "Projector_0", "image_range": (1, 20)},
            1: {"name": "Projector_1", "image_range": (21, 40)},
            2: {"name": "Projector_2", "image_range": (41, 60)},
            3: {"name": "Projector_3", "image_range": (61, 80)}
        }
        self.frequency_configs = {
            8: {"image_indices": [1, 2, 3, 4, 5]},      # 8像素周期
            64: {"image_indices": [6, 7, 8, 9, 10]},    # 64像素周期
            512: {"image_indices": [11, 12, 13, 14, 15]}, # 512像素周期
            1080: {"image_indices": [16, 17, 18, 19, 20]} # 1080像素周期
        }
        
        # 标定参数
        self.height_step = 1.0  # mm，每次移动1mm
        self.num_positions = 11  # Test-0 到 Test-10，共11个位置
        
        # 相位展开器
        self.phase_unwrapper = FourFrequencyPhaseUnwrapping(pixel_periods=self.pixel_periods)
        
        # 标定结果存储
        self.calibration_results = {}
        
    def load_calibration_images(self, test_folder: str, projector_id: int) -> Dict[int, List[np.ndarray]]:
        """
        加载指定投影仪的标定图像
        Args:
            test_folder: 测试文件夹路径
            projector_id: 投影仪ID (0-3)
        Returns:
            images_by_frequency: 按频率组织的图像字典
        """
        images_by_frequency = {}
        
        # 获取投影仪的图像范围
        start_idx, end_idx = self.projector_configs[projector_id]["image_range"]
        
        # 按频率加载图像
        for period, config in self.frequency_configs.items():
            frequency_images = []
            
            for img_idx in config["image_indices"]:
                # 计算实际的图像编号
                actual_img_idx = start_idx + img_idx - 1
                img_path = os.path.join(test_folder, f"{actual_img_idx}.bmp")
                
                if os.path.exists(img_path):
                    img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
                    if img is not None:
                        frequency_images.append(img.astype(np.float32))
                    else:
                        print(f"警告: 无法读取图像 {img_path}")
                        frequency_images.append(np.zeros((480, 640), dtype=np.float32))
                else:
                    print(f"警告: 图像文件不存在 {img_path}")
                    frequency_images.append(np.zeros((480, 640), dtype=np.float32))
            
            images_by_frequency[period] = frequency_images
            
        return images_by_frequency
    
    def convert_to_phase_unwrapper_format(self, images_by_frequency: Dict[int, List[np.ndarray]]) -> np.ndarray:
        """
        将图像转换为相位展开器需要的格式
        Args:
            images_by_frequency: 按频率组织的图像字典
        Returns:
            images: 形状为 (frequencies, steps, height, width) 的图像数组
        """
        # 按照相位展开器期望的频率顺序排列 [1080, 512, 64, 8]
        ordered_images = []
        for period in self.pixel_periods:
            if period in images_by_frequency:
                ordered_images.append(images_by_frequency[period])
            else:
                # 如果缺少某个频率，创建空白图像
                h, w = 480, 640  # 默认尺寸
                if ordered_images:
                    h, w = ordered_images[0][0].shape
                empty_images = [np.zeros((h, w), dtype=np.float32) for _ in range(5)]
                ordered_images.append(empty_images)
        
        return np.array(ordered_images)
    
    def process_single_position(self, test_folder: str, projector_id: int) -> Optional[np.ndarray]:
        """
        处理单个位置的相位展开
        Args:
            test_folder: 测试文件夹路径
            projector_id: 投影仪ID
        Returns:
            unwrapped_phase: 展开的相位图，如果失败返回None
        """
        try:
            # 加载图像
            images_by_frequency = self.load_calibration_images(test_folder, projector_id)
            
            # 转换格式
            images = self.convert_to_phase_unwrapper_format(images_by_frequency)
            
            # 计算包裹相位
            wrapped_phases, modulations = self.phase_unwrapper.calculate_all_wrapped_phases(images)
            
            # 创建质量掩码
            quality_mask = self.phase_unwrapper.create_quality_mask(modulations, min_modulation=0.05)
            
            # 相位展开
            unwrapped_phase = self.phase_unwrapper.phase_unwrapping_hierarchical(wrapped_phases)
            
            # 应用质量掩码
            unwrapped_phase[~quality_mask] = np.nan
            
            return unwrapped_phase
            
        except Exception as e:
            print(f"处理位置 {test_folder} 投影仪 {projector_id} 时出错: {e}")
            return None
    
    def collect_calibration_data(self, projector_id: int) -> Tuple[List[np.ndarray], List[float]]:
        """
        收集指定投影仪的所有标定数据
        Args:
            projector_id: 投影仪ID
        Returns:
            phase_maps: 相位图列表
            heights: 对应的高度列表
        """
        phase_maps = []
        heights = []
        
        print(f"正在收集投影仪 {projector_id} 的标定数据...")
        
        for position in range(self.num_positions):
            test_folder = os.path.join(self.calibration_data_folder, f"Test-{position}")
            
            if not os.path.exists(test_folder):
                print(f"警告: 文件夹不存在 {test_folder}")
                continue
            
            print(f"  处理位置 {position} (高度 {position * self.height_step:.1f} mm)...")
            
            # 处理相位展开
            unwrapped_phase = self.process_single_position(test_folder, projector_id)
            
            if unwrapped_phase is not None:
                phase_maps.append(unwrapped_phase)
                heights.append(position * self.height_step)
            else:
                print(f"    跳过位置 {position}（处理失败）")
        
        print(f"投影仪 {projector_id} 收集到 {len(phase_maps)} 个有效位置的数据")
        return phase_maps, heights
    
    def extract_valid_phase_height_pairs(self, phase_maps: List[np.ndarray], 
                                       heights: List[float]) -> Tuple[np.ndarray, np.ndarray]:
        """
        提取有效的相位-高度对
        Args:
            phase_maps: 相位图列表
            heights: 高度列表
        Returns:
            phases: 有效相位值数组
            height_values: 对应的高度值数组
        """
        all_phases = []
        all_heights = []
        
        for phase_map, height in zip(phase_maps, heights):
            # 提取有效像素（非NaN）
            valid_mask = ~np.isnan(phase_map)
            valid_phases = phase_map[valid_mask]
            
            if len(valid_phases) > 0:
                all_phases.extend(valid_phases)
                all_heights.extend([height] * len(valid_phases))
        
        return np.array(all_phases), np.array(all_heights)
    
    def fit_calibration_model(self, phases: np.ndarray, heights: np.ndarray, 
                            model_type: str = 'polynomial', degree: int = 2) -> Dict:
        """
        拟合相位-高度标定模型
        Args:
            phases: 相位值数组
            heights: 高度值数组
            model_type: 模型类型 ('linear', 'polynomial')
            degree: 多项式度数（仅用于polynomial模型）
        Returns:
            model_info: 包含模型和评估指标的字典
        """
        # 数据预处理：移除异常值
        # 使用四分位数方法移除异常值
        q1_phase, q3_phase = np.percentile(phases, [25, 75])
        iqr_phase = q3_phase - q1_phase
        phase_lower = q1_phase - 1.5 * iqr_phase
        phase_upper = q3_phase + 1.5 * iqr_phase
        
        valid_mask = (phases >= phase_lower) & (phases <= phase_upper)
        clean_phases = phases[valid_mask]
        clean_heights = heights[valid_mask]
        
        print(f"数据清理: {len(phases)} -> {len(clean_phases)} 个数据点")
        
        # 创建模型
        if model_type == 'linear':
            model = LinearRegression()
            X = clean_phases.reshape(-1, 1)
        elif model_type == 'polynomial':
            model = Pipeline([
                ('poly', PolynomialFeatures(degree=degree)),
                ('linear', LinearRegression())
            ])
            X = clean_phases.reshape(-1, 1)
        else:
            raise ValueError(f"不支持的模型类型: {model_type}")
        
        # 训练模型
        model.fit(X, clean_heights)
        
        # 预测和评估
        y_pred = model.predict(X)
        r2 = r2_score(clean_heights, y_pred)
        rmse = np.sqrt(mean_squared_error(clean_heights, y_pred))
        
        # 计算残差统计
        residuals = clean_heights - y_pred
        residual_std = np.std(residuals)
        
        model_info = {
            'model': model,
            'model_type': model_type,
            'degree': degree if model_type == 'polynomial' else 1,
            'r2_score': r2,
            'rmse': rmse,
            'residual_std': residual_std,
            'n_samples': len(clean_phases),
            'phase_range': (np.min(clean_phases), np.max(clean_phases)),
            'height_range': (np.min(clean_heights), np.max(clean_heights))
        }
        
        return model_info
    
    def calibrate_projector(self, projector_id: int, model_type: str = 'polynomial', 
                          degree: int = 2) -> Dict:
        """
        标定单个投影仪
        Args:
            projector_id: 投影仪ID
            model_type: 模型类型
            degree: 多项式度数
        Returns:
            calibration_result: 标定结果字典
        """
        print(f"\n=== 开始标定投影仪 {projector_id} ===")
        
        # 收集标定数据
        phase_maps, heights = self.collect_calibration_data(projector_id)
        
        if len(phase_maps) == 0:
            print(f"投影仪 {projector_id} 没有有效数据，跳过标定")
            return None
        
        # 提取相位-高度对
        phases, height_values = self.extract_valid_phase_height_pairs(phase_maps, heights)
        
        if len(phases) == 0:
            print(f"投影仪 {projector_id} 没有有效的相位-高度对，跳过标定")
            return None
        
        print(f"提取到 {len(phases)} 个有效的相位-高度对")
        
        # 拟合标定模型
        model_info = self.fit_calibration_model(phases, height_values, model_type, degree)
        
        # 保存标定结果
        calibration_result = {
            'projector_id': projector_id,
            'projector_name': self.projector_configs[projector_id]["name"],
            'model_info': model_info,
            'phase_maps': phase_maps,
            'heights': heights,
            'calibration_phases': phases,
            'calibration_heights': height_values
        }
        
        # 打印标定结果
        print(f"标定完成:")
        print(f"  R² 分数: {model_info['r2_score']:.6f}")
        print(f"  RMSE: {model_info['rmse']:.4f} mm")
        print(f"  残差标准差: {model_info['residual_std']:.4f} mm")
        print(f"  数据点数: {model_info['n_samples']}")
        print(f"  相位范围: [{model_info['phase_range'][0]:.3f}, {model_info['phase_range'][1]:.3f}] rad")
        
        return calibration_result

    def calibrate_all_projectors(self, model_type: str = 'polynomial', degree: int = 2) -> Dict:
        """
        标定所有投影仪
        Args:
            model_type: 模型类型
            degree: 多项式度数
        Returns:
            all_results: 所有投影仪的标定结果
        """
        print("=== 开始标定所有投影仪 ===")

        all_results = {}

        for projector_id in range(4):
            result = self.calibrate_projector(projector_id, model_type, degree)
            if result is not None:
                all_results[projector_id] = result
                self.calibration_results[projector_id] = result

        print(f"\n标定完成，成功标定 {len(all_results)} 个投影仪")
        return all_results

    def save_calibration_results(self, output_folder: str = "calibration_results"):
        """
        保存标定结果
        Args:
            output_folder: 输出文件夹
        """
        os.makedirs(output_folder, exist_ok=True)

        # 保存完整的标定结果
        with open(os.path.join(output_folder, "calibration_results.pkl"), 'wb') as f:
            pickle.dump(self.calibration_results, f)

        # 保存简化的模型文件（仅包含模型，用于实际应用）
        simplified_results = {}
        for proj_id, result in self.calibration_results.items():
            simplified_results[proj_id] = {
                'model': result['model_info']['model'],
                'model_type': result['model_info']['model_type'],
                'degree': result['model_info']['degree'],
                'phase_range': result['model_info']['phase_range'],
                'height_range': result['model_info']['height_range']
            }

        with open(os.path.join(output_folder, "calibration_models.pkl"), 'wb') as f:
            pickle.dump(simplified_results, f)

        print(f"标定结果已保存到 {output_folder}")

    def load_calibration_results(self, results_file: str = "calibration_results/calibration_results.pkl"):
        """
        加载标定结果
        Args:
            results_file: 结果文件路径
        """
        if os.path.exists(results_file):
            with open(results_file, 'rb') as f:
                self.calibration_results = pickle.load(f)
            print(f"已加载标定结果: {len(self.calibration_results)} 个投影仪")
        else:
            print(f"标定结果文件不存在: {results_file}")

    def predict_height(self, phase_map: np.ndarray, projector_id: int) -> np.ndarray:
        """
        使用标定模型预测高度
        Args:
            phase_map: 相位图
            projector_id: 投影仪ID
        Returns:
            height_map: 高度图
        """
        if projector_id not in self.calibration_results:
            raise ValueError(f"投影仪 {projector_id} 未标定")

        model_info = self.calibration_results[projector_id]['model_info']
        model = model_info['model']

        # 创建输出高度图
        height_map = np.full_like(phase_map, np.nan)

        # 找到有效像素
        valid_mask = ~np.isnan(phase_map)
        valid_phases = phase_map[valid_mask]

        if len(valid_phases) > 0:
            # 预测高度
            if model_info['model_type'] == 'linear':
                X = valid_phases.reshape(-1, 1)
            else:  # polynomial
                X = valid_phases.reshape(-1, 1)

            predicted_heights = model.predict(X)
            height_map[valid_mask] = predicted_heights

        return height_map

    def visualize_calibration_results(self, output_folder: str = "calibration_results"):
        """
        可视化标定结果
        Args:
            output_folder: 输出文件夹
        """
        if not self.calibration_results:
            print("没有标定结果可视化")
            return

        os.makedirs(output_folder, exist_ok=True)

        # 为每个投影仪创建可视化
        for proj_id, result in self.calibration_results.items():
            self._plot_single_projector_calibration(result, output_folder)

        # 创建总结图
        self._plot_calibration_summary(output_folder)

    def _plot_single_projector_calibration(self, result: Dict, output_folder: str):
        """绘制单个投影仪的标定结果"""
        proj_id = result['projector_id']
        model_info = result['model_info']
        phases = result['calibration_phases']
        heights = result['calibration_heights']

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'Projector {proj_id} Calibration Results', fontsize=16)

        # 1. 散点图和拟合曲线
        ax1 = axes[0, 0]

        # 随机采样显示点（如果数据太多）
        if len(phases) > 10000:
            indices = np.random.choice(len(phases), 10000, replace=False)
            sample_phases = phases[indices]
            sample_heights = heights[indices]
        else:
            sample_phases = phases
            sample_heights = heights

        ax1.scatter(sample_phases, sample_heights, alpha=0.1, s=1, c='blue', label='Data points')

        # 绘制拟合曲线
        phase_range = np.linspace(model_info['phase_range'][0], model_info['phase_range'][1], 1000)
        if model_info['model_type'] == 'linear':
            X_plot = phase_range.reshape(-1, 1)
        else:
            X_plot = phase_range.reshape(-1, 1)

        height_pred = model_info['model'].predict(X_plot)
        ax1.plot(phase_range, height_pred, 'r-', linewidth=2, label='Fitted curve')

        ax1.set_xlabel('Phase (rad)')
        ax1.set_ylabel('Height (mm)')
        ax1.set_title('Phase-Height Relationship')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 残差图
        ax2 = axes[0, 1]
        if model_info['model_type'] == 'linear':
            X_residual = sample_phases.reshape(-1, 1)
        else:
            X_residual = sample_phases.reshape(-1, 1)

        height_pred_sample = model_info['model'].predict(X_residual)
        residuals = sample_heights - height_pred_sample

        ax2.scatter(sample_phases, residuals, alpha=0.1, s=1, c='green')
        ax2.axhline(y=0, color='r', linestyle='--')
        ax2.set_xlabel('Phase (rad)')
        ax2.set_ylabel('Residuals (mm)')
        ax2.set_title('Residuals vs Phase')
        ax2.grid(True, alpha=0.3)

        # 3. 残差直方图
        ax3 = axes[1, 0]
        ax3.hist(residuals, bins=50, alpha=0.7, color='orange', edgecolor='black')
        ax3.axvline(x=0, color='r', linestyle='--')
        ax3.set_xlabel('Residuals (mm)')
        ax3.set_ylabel('Frequency')
        ax3.set_title('Residuals Distribution')
        ax3.grid(True, alpha=0.3)

        # 4. 统计信息
        ax4 = axes[1, 1]
        ax4.axis('off')

        stats_text = f"""
        Model Type: {model_info['model_type']}
        Degree: {model_info['degree']}
        R² Score: {model_info['r2_score']:.6f}
        RMSE: {model_info['rmse']:.4f} mm
        Residual Std: {model_info['residual_std']:.4f} mm
        Sample Count: {model_info['n_samples']:,}
        Phase Range: [{model_info['phase_range'][0]:.3f}, {model_info['phase_range'][1]:.3f}] rad
        Height Range: [{model_info['height_range'][0]:.1f}, {model_info['height_range'][1]:.1f}] mm
        """

        ax4.text(0.1, 0.9, stats_text, transform=ax4.transAxes, fontsize=12,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))

        plt.tight_layout()
        plt.savefig(os.path.join(output_folder, f'projector_{proj_id}_calibration.png'),
                   dpi=150, bbox_inches='tight')
        plt.close()

    def _plot_calibration_summary(self, output_folder: str):
        """绘制标定总结图"""
        if not self.calibration_results:
            return

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Calibration Summary - All Projectors', fontsize=16)

        # 收集所有投影仪的统计数据
        proj_ids = []
        r2_scores = []
        rmse_values = []
        sample_counts = []

        for proj_id, result in self.calibration_results.items():
            model_info = result['model_info']
            proj_ids.append(f'Proj {proj_id}')
            r2_scores.append(model_info['r2_score'])
            rmse_values.append(model_info['rmse'])
            sample_counts.append(model_info['n_samples'])

        # 1. R²分数对比
        ax1 = axes[0, 0]
        bars1 = ax1.bar(proj_ids, r2_scores, color='skyblue', edgecolor='navy')
        ax1.set_ylabel('R² Score')
        ax1.set_title('R² Score Comparison')
        ax1.set_ylim(0, 1)
        ax1.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, score in zip(bars1, r2_scores):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{score:.4f}', ha='center', va='bottom')

        # 2. RMSE对比
        ax2 = axes[0, 1]
        bars2 = ax2.bar(proj_ids, rmse_values, color='lightcoral', edgecolor='darkred')
        ax2.set_ylabel('RMSE (mm)')
        ax2.set_title('RMSE Comparison')
        ax2.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, rmse in zip(bars2, rmse_values):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(rmse_values)*0.01,
                    f'{rmse:.3f}', ha='center', va='bottom')

        # 3. 样本数量对比
        ax3 = axes[1, 0]
        bars3 = ax3.bar(proj_ids, sample_counts, color='lightgreen', edgecolor='darkgreen')
        ax3.set_ylabel('Sample Count')
        ax3.set_title('Sample Count Comparison')
        ax3.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, count in zip(bars3, sample_counts):
            ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(sample_counts)*0.01,
                    f'{count:,}', ha='center', va='bottom', rotation=45)

        # 4. 总体统计
        ax4 = axes[1, 1]
        ax4.axis('off')

        avg_r2 = np.mean(r2_scores)
        avg_rmse = np.mean(rmse_values)
        total_samples = sum(sample_counts)

        summary_text = f"""
        Overall Calibration Quality:

        Average R² Score: {avg_r2:.6f}
        Average RMSE: {avg_rmse:.4f} mm
        Total Samples: {total_samples:,}
        Calibrated Projectors: {len(self.calibration_results)}

        Quality Assessment:
        {'Excellent' if avg_r2 > 0.99 else 'Good' if avg_r2 > 0.95 else 'Fair' if avg_r2 > 0.90 else 'Poor'}
        (R² > 0.99: Excellent, > 0.95: Good, > 0.90: Fair)
        """

        ax4.text(0.1, 0.9, summary_text, transform=ax4.transAxes, fontsize=12,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))

        plt.tight_layout()
        plt.savefig(os.path.join(output_folder, 'calibration_summary.png'),
                   dpi=150, bbox_inches='tight')
        plt.close()

    def check_calibration_quality(self) -> Dict:
        """
        检查标定质量
        Returns:
            quality_report: 质量报告字典
        """
        if not self.calibration_results:
            return {"error": "没有标定结果"}

        quality_report = {
            "overall_quality": "Unknown",
            "projector_reports": {},
            "recommendations": []
        }

        all_r2_scores = []
        all_rmse_values = []

        for proj_id, result in self.calibration_results.items():
            model_info = result['model_info']
            r2 = model_info['r2_score']
            rmse = model_info['rmse']

            all_r2_scores.append(r2)
            all_rmse_values.append(rmse)

            # 单个投影仪质量评估
            if r2 > 0.99 and rmse < 0.1:
                proj_quality = "Excellent"
            elif r2 > 0.95 and rmse < 0.2:
                proj_quality = "Good"
            elif r2 > 0.90 and rmse < 0.5:
                proj_quality = "Fair"
            else:
                proj_quality = "Poor"

            quality_report["projector_reports"][proj_id] = {
                "quality": proj_quality,
                "r2_score": r2,
                "rmse": rmse,
                "sample_count": model_info['n_samples']
            }

        # 整体质量评估
        avg_r2 = np.mean(all_r2_scores)
        avg_rmse = np.mean(all_rmse_values)

        if avg_r2 > 0.99 and avg_rmse < 0.1:
            overall_quality = "Excellent"
        elif avg_r2 > 0.95 and avg_rmse < 0.2:
            overall_quality = "Good"
        elif avg_r2 > 0.90 and avg_rmse < 0.5:
            overall_quality = "Fair"
        else:
            overall_quality = "Poor"

        quality_report["overall_quality"] = overall_quality
        quality_report["average_r2"] = avg_r2
        quality_report["average_rmse"] = avg_rmse

        # 生成建议
        recommendations = []

        if avg_r2 < 0.95:
            recommendations.append("R²分数较低，建议检查数据质量或尝试更高阶的多项式模型")

        if avg_rmse > 0.2:
            recommendations.append("RMSE较高，建议检查标定数据的一致性")

        poor_projectors = [proj_id for proj_id, report in quality_report["projector_reports"].items()
                          if report["quality"] == "Poor"]
        if poor_projectors:
            recommendations.append(f"投影仪 {poor_projectors} 标定质量较差，建议重新采集数据")

        if not recommendations:
            recommendations.append("标定质量良好，可以用于实际测量")

        quality_report["recommendations"] = recommendations

        return quality_report
