#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度感知融合系统 (Depth-Aware Fusion System)
==============================================

结合深度域修正和多投影仪融合的综合系统

主要功能：
1. 四频相位解包重建单个投影仪深度图
2. 深度域异常值检测和修正
3. 多投影仪融合生成高质量点云

处理流程：
Phase Unwrapping → Depth Reconstruction → Depth Domain Correction → Multi-Projector Fusion

作者：AI Assistant  
日期：2024-12-19
版本：v1.0
"""

import numpy as np
import cv2
import os
import time
from pathlib import Path
from typing import List, Tuple, Dict, Optional
import argparse
from tifffile import imread as tiff_imread
from scipy import stats, ndimage, spatial
import json

# 导入相位展开算法
from four_freq_phase_unwrap import five_step_phase, unwrap_multifrequency


class DepthAwareFusionSystem:
    """深度感知融合系统"""
    
    def __init__(self, calibration_folder: str = "final_calibration_results_four_freq"):
        """
        初始化深度感知融合系统
        
        参数:
            calibration_folder: 标定结果文件夹路径
        """
        print("🚀 初始化深度感知融合系统...")
        
        self.calibration_folder = Path(calibration_folder)
        self.mapping_tables_folder = self.calibration_folder / "mapping_tables"
        
        # 相位展开参数 - 四频
        self.periods = [1080.0, 512.0, 64.0, 8.0]  # 低频到高频
        
        # 投影仪配置
        self.projector_configs = {
            0: {"name": "DLP-0", "image_range": (1, 20)},
            1: {"name": "DLP-1", "image_range": (21, 40)},
            2: {"name": "DLP-2", "image_range": (41, 60)},
            3: {"name": "DLP-3", "image_range": (61, 80)}
        }
        
        # 频率配置
        self.frequency_configs = {
            8: {"image_indices": [1, 2, 3, 4, 5], "freq_idx": 3},      # 高频
            64: {"image_indices": [6, 7, 8, 9, 10], "freq_idx": 2},    
            512: {"image_indices": [11, 12, 13, 14, 15], "freq_idx": 1}, 
            1080: {"image_indices": [16, 17, 18, 19, 20], "freq_idx": 0} # 低频
        }
        
        # 加载映射表
        self.mapping_tables = {}
        self._load_mapping_tables()
        
        # CCD参数
        self.CCD_DX = 0.0154  # mm/pixel
        
        # 深度域修正参数
        self.depth_correction_params = {
            "isolation_radius": 5,              # 孤立点检测半径
            "min_neighbors": 3,                 # 最小邻居数
            "depth_jump_threshold": 3.0,        # 深度跳跃阈值(mm)
            "outlier_z_threshold": 2.5,         # Z-score阈值(更严格)
            "local_window_size": 9,             # 局部窗口大小
            "surface_smoothness_threshold": 1.5, # 表面平滑度阈值
            "gradient_threshold": 2.0,          # 梯度阈值
            "max_correction_iterations": 2,     # 最大修正迭代次数
            "smoothing_sigma": 0.8,             # 平滑参数
        }
        
        # 融合参数
        self.fusion_params = {
            "fine_threshold": 0.02,             # 20微米精细阈值
            "coarse_threshold": 0.5,            # 500微米粗糙阈值
            "min_valid_ratio": 0.15,            # 最小有效像素比例
            "min_quality_score": 0.3,           # 最小质量分数
        }
        
        print(f"✅ 系统初始化完成，支持 {len(self.mapping_tables)} 个投影仪")
    
    def _load_mapping_tables(self):
        """加载TIFF映射表"""
        print("📂 加载TIFF映射表...")
        
        for proj_id in range(4):
            proj_name = f"DLP-{proj_id}"
            self.mapping_tables[proj_id] = {}
            
            for degree in range(3):  # 0, 1, 2 对应常数项、一次项、二次项
                tiff_file = self.mapping_tables_folder / f"{proj_name}-{degree}.tiff"
                
                if tiff_file.exists():
                    try:
                        coeff_map = tiff_imread(str(tiff_file)).astype(np.float64)
                        self.mapping_tables[proj_id][degree] = coeff_map
                        print(f"  ✅ {proj_name}-{degree}.tiff, 形状: {coeff_map.shape}")
                    except Exception as e:
                        print(f"  ❌ 加载失败 {tiff_file}: {e}")
                        self.mapping_tables[proj_id][degree] = None
                else:
                    print(f"  ⚠️  文件不存在: {tiff_file}")
                    self.mapping_tables[proj_id][degree] = None
    
    def load_phase_shift_images(self, test_folder: str, projector_id: int) -> Optional[Dict[int, List[np.ndarray]]]:
        """加载指定投影仪的相移图像"""
        test_path = Path(test_folder)
        start_idx, _ = self.projector_configs[projector_id]["image_range"]
        images_by_frequency = {}
        
        for frequency, config in self.frequency_configs.items():
            frequency_images = []
            for img_idx in config["image_indices"]:
                actual_img_idx = start_idx + img_idx - 1
                img_path = test_path / f"{actual_img_idx}.bmp"
                
                if img_path.exists():
                    try:
                        img = cv2.imread(str(img_path), cv2.IMREAD_GRAYSCALE)
                        if img is not None:
                            frequency_images.append(img.astype(np.float64))
                        else:
                            print(f"    ❌ 无法读取图像: {img_path}")
                            return None
                    except Exception as e:
                        print(f"    ❌ 读取失败 {img_path}: {e}")
                        return None
                else:
                    print(f"    ❌ 文件不存在: {img_path}")
                    return None
            
            if len(frequency_images) == 5:
                images_by_frequency[frequency] = frequency_images
            else:
                print(f"    ❌ 投影仪 {projector_id} 频率 {frequency} 图像数量不足")
                return None
        
        return images_by_frequency
    
    def phase_unwrapping_four_frequency(self, images_by_frequency: Dict[int, List[np.ndarray]]) -> Tuple[Optional[np.ndarray], Dict]:
        """四频相位展开"""
        try:
            # 按低频到高频的顺序组织图像
            freq_order = [1080, 512, 64, 8]
            frames_list = []
            
            for freq in freq_order:
                if freq in images_by_frequency:
                    freq_images = images_by_frequency[freq]
                    frames_array = np.stack(freq_images, axis=-1)  # (H, W, 5)
                    frames_list.append(frames_array)
                else:
                    raise ValueError(f"缺少频率 {freq} 的图像")
            
            # 计算包裹相位
            wrapped_phases = []
            for frames_array in frames_list:
                wrapped_phase = five_step_phase(frames_array)
                wrapped_phases.append(wrapped_phase)
            
            # 四频相位展开
            unwrapped_phases = unwrap_multifrequency(wrapped_phases, self.periods)
            unwrapped_phase = unwrapped_phases[-1]  # 最高频的展开相位
            
            phase_info = {
                "periods": self.periods,
                "valid_pixels": np.sum(np.isfinite(unwrapped_phase)),
                "total_pixels": unwrapped_phase.size,
                "valid_ratio": np.sum(np.isfinite(unwrapped_phase)) / unwrapped_phase.size
            }
            
            return unwrapped_phase, phase_info
            
        except Exception as e:
            print(f"    ❌ 相位展开失败: {e}")
            return None, {"error": str(e)}
    
    def phase_to_height_polynomial(self, phase: np.ndarray, projector_id: int) -> Optional[np.ndarray]:
        """使用多项式映射将相位转换为高度"""
        if projector_id not in self.mapping_tables:
            raise ValueError(f"投影仪 {projector_id} 的映射表未加载")
        
        mapping_table = self.mapping_tables[projector_id]
        
        if 0 not in mapping_table or mapping_table[0] is None:
            raise ValueError(f"投影仪 {projector_id} 缺少常数项映射表")
        
        # 获取映射表尺寸并调整相位图
        ref_shape = mapping_table[0].shape
        if phase.shape != ref_shape:
            phase_resized = cv2.resize(phase, (ref_shape[1], ref_shape[0]), 
                                     interpolation=cv2.INTER_LINEAR)
        else:
            phase_resized = phase.copy()
        
        # 计算多项式：h = c0 + c1*φ + c2*φ²
        height_map = np.zeros_like(phase_resized)
        
        # 添加各项系数
        for degree in range(3):
            if degree in mapping_table and mapping_table[degree] is not None:
                if degree == 0:
                    height_map += mapping_table[degree]
                else:
                    height_map += mapping_table[degree] * (phase_resized ** degree)
        
        # 处理无效值
        valid_phase = np.isfinite(phase_resized)
        height_map[~valid_phase] = np.nan
        
        return height_map
    
    def detect_isolated_points(self, depth_map: np.ndarray) -> np.ndarray:
        """检测深度域孤立点"""
        valid_mask = np.isfinite(depth_map)
        if np.sum(valid_mask) == 0:
            return np.zeros_like(depth_map, dtype=bool)
        
        height, width = depth_map.shape
        isolation_radius = self.depth_correction_params["isolation_radius"]
        min_neighbors = self.depth_correction_params["min_neighbors"]
        
        isolated_mask = np.zeros_like(depth_map, dtype=bool)
        
        # 创建圆形结构元素
        y, x = np.ogrid[-isolation_radius:isolation_radius+1, -isolation_radius:isolation_radius+1]
        structure = (x*x + y*y) <= isolation_radius*isolation_radius
        
        # 检查每个有效点的邻居数量
        for r in range(height):
            for c in range(width):
                if not valid_mask[r, c]:
                    continue
                
                # 提取邻域
                r_start = max(0, r - isolation_radius)
                r_end = min(height, r + isolation_radius + 1)
                c_start = max(0, c - isolation_radius)
                c_end = min(width, c + isolation_radius + 1)
                
                neighborhood = valid_mask[r_start:r_end, c_start:c_end]
                neighbor_count = np.sum(neighborhood) - 1  # 减去自己
                
                if neighbor_count < min_neighbors:
                    isolated_mask[r, c] = True
        
        return isolated_mask
    
    def detect_depth_jumps(self, depth_map: np.ndarray) -> np.ndarray:
        """检测深度跳跃异常"""
        valid_mask = np.isfinite(depth_map)
        if np.sum(valid_mask) == 0:
            return np.zeros_like(depth_map, dtype=bool)
        
        jump_threshold = self.depth_correction_params["depth_jump_threshold"]
        
        # 计算深度梯度
        grad_x = cv2.Sobel(depth_map, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(depth_map, cv2.CV_64F, 0, 1, ksize=3)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        
        # 检测大梯度点
        jump_mask = valid_mask & (gradient_magnitude > jump_threshold)
        
        # 形态学开运算去除噪声
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        jump_mask = cv2.morphologyEx(jump_mask.astype(np.uint8), cv2.MORPH_OPEN, kernel).astype(bool)
        
        return jump_mask
    
    def detect_statistical_outliers(self, depth_map: np.ndarray) -> np.ndarray:
        """检测统计异常值"""
        valid_mask = np.isfinite(depth_map)
        if np.sum(valid_mask) == 0:
            return np.zeros_like(depth_map, dtype=bool)
        
        window_size = self.depth_correction_params["local_window_size"]
        z_threshold = self.depth_correction_params["outlier_z_threshold"]
        
        outlier_mask = np.zeros_like(depth_map, dtype=bool)
        half_window = window_size // 2
        height, width = depth_map.shape
        
        # 局部统计异常检测
        for r in range(half_window, height - half_window, 2):  # 步长优化
            for c in range(half_window, width - half_window, 2):
                if not valid_mask[r, c]:
                    continue
                
                # 提取局部窗口
                window = depth_map[r-half_window:r+half_window+1, c-half_window:c+half_window+1]
                window_valid = valid_mask[r-half_window:r+half_window+1, c-half_window:c+half_window+1]
                
                valid_values = window[window_valid]
                if len(valid_values) < 5:
                    continue
                
                # 计算Z-score
                window_mean = np.mean(valid_values)
                window_std = np.std(valid_values)
                
                if window_std > 0:
                    z_score = abs(depth_map[r, c] - window_mean) / window_std
                    if z_score > z_threshold:
                        outlier_mask[r, c] = True
        
        return outlier_mask
    
    def detect_surface_discontinuities(self, depth_map: np.ndarray) -> np.ndarray:
        """检测表面不连续性"""
        valid_mask = np.isfinite(depth_map)
        if np.sum(valid_mask) == 0:
            return np.zeros_like(depth_map, dtype=bool)
        
        smoothness_threshold = self.depth_correction_params["surface_smoothness_threshold"]
        
        # 计算拉普拉斯算子（二阶导数，检测曲率）
        depth_map_float32 = depth_map.astype(np.float32)
        laplacian = cv2.Laplacian(depth_map_float32, cv2.CV_32F, ksize=3)
        
        # 检测高曲率点
        discontinuity_mask = valid_mask & (np.abs(laplacian) > smoothness_threshold)
        
        # 形态学开运算
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        discontinuity_mask = cv2.morphologyEx(
            discontinuity_mask.astype(np.uint8), cv2.MORPH_OPEN, kernel).astype(bool)
        
        return discontinuity_mask
    
    def intelligent_depth_interpolation(self, depth_map: np.ndarray, outlier_mask: np.ndarray) -> np.ndarray:
        """智能深度插值修复"""
        corrected_depth = depth_map.copy()
        valid_mask = np.isfinite(depth_map) & (~outlier_mask)
        
        if np.sum(valid_mask) == 0:
            return corrected_depth
        
        # 1. 小区域中值滤波
        small_outliers = outlier_mask.copy()
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        
        for iteration in range(2):
            median_filtered = cv2.medianBlur(corrected_depth.astype(np.float32), 5)
            opened = cv2.morphologyEx(small_outliers.astype(np.uint8), cv2.MORPH_OPEN, kernel)
            small_outlier_mask = small_outliers & (opened == 0)
            
            if np.sum(small_outlier_mask) == 0:
                break
            
            corrected_depth[small_outlier_mask] = median_filtered[small_outlier_mask]
            small_outliers &= (~small_outlier_mask)
        
        # 2. 大区域距离加权插值
        if np.sum(small_outliers) > 0:
            height, width = depth_map.shape
            y_coords, x_coords = np.mgrid[0:height, 0:width]
            
            # 有效点坐标和值
            valid_points = np.column_stack((y_coords[valid_mask].ravel(), x_coords[valid_mask].ravel()))
            valid_values = corrected_depth[valid_mask]
            
            # 需要插值的点
            invalid_points = np.column_stack((y_coords[small_outliers].ravel(), x_coords[small_outliers].ravel()))
            
            if len(valid_points) > 0 and len(invalid_points) > 0:
                # KD树最近邻搜索
                tree = spatial.cKDTree(valid_points)
                distances, indices = tree.query(invalid_points, k=min(6, len(valid_points)))
                
                # 距离加权插值
                if distances.ndim == 1:
                    distances = distances.reshape(-1, 1)
                    indices = indices.reshape(-1, 1)
                
                weights = 1.0 / (distances + 0.1)
                weights /= np.sum(weights, axis=1, keepdims=True)
                
                interpolated_values = np.sum(valid_values[indices] * weights, axis=1)
                corrected_depth[small_outliers] = interpolated_values
        
        # 3. 轻微高斯平滑
        smoothing_sigma = self.depth_correction_params["smoothing_sigma"]
        if smoothing_sigma > 0:
            corrected_depth = cv2.GaussianBlur(corrected_depth, (5, 5), smoothing_sigma)
        
        # 保持原始NaN区域
        original_nan_mask = ~np.isfinite(depth_map)
        corrected_depth[original_nan_mask] = np.nan
        
        return corrected_depth
    
    def depth_domain_correction(self, depth_map: np.ndarray, projector_id: int) -> Tuple[np.ndarray, Dict]:
        """深度域异常值修正"""
        print(f"    🔧 深度域修正 - 投影仪 {projector_id}...")
        
        if np.sum(np.isfinite(depth_map)) == 0:
            print("    ❌ 没有有效深度数据")
            return depth_map, {"error": "no_valid_data"}
        
        # 统计原始信息
        original_std = np.nanstd(depth_map)
        original_valid_count = np.sum(np.isfinite(depth_map))
        
        # 检测各类异常
        isolated_mask = self.detect_isolated_points(depth_map)
        jump_mask = self.detect_depth_jumps(depth_map)
        outlier_mask = self.detect_statistical_outliers(depth_map)
        discontinuity_mask = self.detect_surface_discontinuities(depth_map)
        
        # 统计异常信息
        isolated_count = np.sum(isolated_mask)
        jump_count = np.sum(jump_mask)
        outlier_count = np.sum(outlier_mask)
        discontinuity_count = np.sum(discontinuity_mask)
        
        # 合并异常掩码
        combined_mask = isolated_mask | jump_mask | outlier_mask | discontinuity_mask
        total_outliers = np.sum(combined_mask)
        
        correction_info = {
            "original_std": original_std,
            "original_valid_count": original_valid_count,
            "isolated_points": isolated_count,
            "depth_jumps": jump_count,
            "statistical_outliers": outlier_count,
            "surface_discontinuities": discontinuity_count,
            "total_outliers": total_outliers,
            "outlier_ratio": total_outliers / original_valid_count if original_valid_count > 0 else 0
        }
        
        print(f"      孤立点: {isolated_count:,}, 跳跃: {jump_count:,}, 统计异常: {outlier_count:,}, 不连续: {discontinuity_count:,}")
        print(f"      总异常: {total_outliers:,} ({correction_info['outlier_ratio']:.1%})")
        
        if total_outliers == 0:
            print("    ✅ 未发现深度异常")
            return depth_map, correction_info
        
        # 迭代修正
        current_depth = depth_map.copy()
        for iteration in range(self.depth_correction_params["max_correction_iterations"]):
            current_depth = self.intelligent_depth_interpolation(current_depth, combined_mask)
            
            # 重新检测异常
            new_outlier_mask = self.detect_statistical_outliers(current_depth)
            new_outlier_count = np.sum(new_outlier_mask)
            
            if new_outlier_count < total_outliers * 0.2:  # 异常点减少80%
                break
            
            combined_mask = new_outlier_mask
        
        # 评估修正效果
        final_std = np.nanstd(current_depth)
        improvement = (original_std - final_std) / original_std * 100 if original_std > 0 else 0
        
        correction_info.update({
            "final_std": final_std,
            "improvement_percent": improvement,
            "iterations": iteration + 1
        })
        
        print(f"    ✅ 修正完成: 标准差 {original_std:.3f} → {final_std:.3f} mm (改善 {improvement:+.1f}%)")
        
        return current_depth, correction_info
    
    def analyze_depth_quality(self, depth_map: np.ndarray, projector_id: int, 
                             correction_info: Optional[Dict] = None) -> Dict:
        """分析深度图质量"""
        total_pixels = depth_map.size
        valid_mask = np.isfinite(depth_map)
        valid_count = np.sum(valid_mask)
        valid_ratio = valid_count / total_pixels
        
        quality_info = {
            "projector_id": projector_id,
            "total_pixels": total_pixels,
            "valid_pixels": valid_count,
            "valid_ratio": valid_ratio,
            "is_valid": valid_ratio >= self.fusion_params["min_valid_ratio"]
        }
        
        if correction_info:
            quality_info["correction_info"] = correction_info
        
        if valid_count > 100:
            valid_depths = depth_map[valid_mask]
            
            # 基本统计
            depth_stats = {
                "min": np.min(valid_depths),
                "max": np.max(valid_depths),
                "mean": np.mean(valid_depths),
                "std": np.std(valid_depths),
                "median": np.median(valid_depths),
                "range": np.max(valid_depths) - np.min(valid_depths)
            }
            
            # 异常值检测
            if len(valid_depths) > 10:
                z_scores = np.abs(stats.zscore(valid_depths))
                outlier_mask = z_scores > 2.5  # 更严格
                outlier_ratio = np.sum(outlier_mask) / len(valid_depths)
            else:
                outlier_ratio = 0
            
            # 质量评分
            quality_score = self._calculate_quality_score(depth_stats, outlier_ratio, valid_ratio)
            quality_info["is_valid"] = quality_info["is_valid"] and quality_score >= self.fusion_params["min_quality_score"]
            
            quality_info.update({
                "depth_stats": depth_stats,
                "outlier_ratio": outlier_ratio,
                "quality_score": quality_score
            })
        else:
            quality_info.update({
                "depth_stats": None,
                "outlier_ratio": 1.0,
                "quality_score": 0.0,
                "is_valid": False
            })
        
        return quality_info
    
    def _calculate_quality_score(self, depth_stats: Dict, outlier_ratio: float, valid_ratio: float) -> float:
        """计算质量分数"""
        # 有效比例权重 (40%)
        score = valid_ratio * 0.4
        
        # 稳定性权重 (35%) - 更严格的标准差评估
        if depth_stats["std"] > 0:
            stability_score = max(0, 1 - depth_stats["std"] / 5.0)  # 5mm为最大可接受标准差
            score += stability_score * 0.35
        
        # 异常值权重 (25%) - 更严格
        outlier_score = max(0, 1 - outlier_ratio * 3)
        score += outlier_score * 0.25
        
        return min(score, 1.0)
    
    def reconstruct_and_correct_single_projector(self, test_folder: str, projector_id: int) -> Tuple[Optional[np.ndarray], Dict]:
        """重建并修正单个投影仪的深度图"""
        proj_name = self.projector_configs[projector_id]["name"]
        print(f"\n🔄 处理投影仪 {projector_id} ({proj_name})...")
        
        try:
            # 1. 加载图像
            print(f"  📥 加载相移图像...")
            images_by_frequency = self.load_phase_shift_images(test_folder, projector_id)
            if images_by_frequency is None:
                return None, {"projector_id": projector_id, "error": "图像加载失败", "is_valid": False}
            
            # 2. 相位展开
            print(f"  🌊 四频相位展开...")
            unwrapped_phase, phase_info = self.phase_unwrapping_four_frequency(images_by_frequency)
            if unwrapped_phase is None:
                return None, {"projector_id": projector_id, "error": "相位展开失败", "is_valid": False}
            
            # 3. 相位转高度
            print(f"  📏 相位转高度映射...")
            height_map = self.phase_to_height_polynomial(unwrapped_phase, projector_id)
            if height_map is None:
                return None, {"projector_id": projector_id, "error": "高度转换失败", "is_valid": False}
            
            # 4. 深度域修正
            corrected_depth, correction_info = self.depth_domain_correction(height_map, projector_id)
            
            # 5. 质量分析
            quality_info = self.analyze_depth_quality(corrected_depth, projector_id, correction_info)
            quality_info["phase_info"] = phase_info
            
            # 显示结果
            status = "✅ 通过" if quality_info["is_valid"] else "❌ 拒绝"
            print(f"  {status}: 有效比例 {quality_info['valid_ratio']:.1%}, 质量分数 {quality_info['quality_score']:.3f}")
            
            return corrected_depth, quality_info
            
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")
            return None, {"projector_id": projector_id, "error": str(e), "is_valid": False}
    
    def adaptive_multi_projector_fusion(self, depth_maps: List[np.ndarray], 
                                       quality_infos: List[Dict]) -> Tuple[np.ndarray, Dict]:
        """自适应多投影仪融合"""
        print(f"\n🔀 执行自适应多投影仪融合...")
        
        fine_threshold = self.fusion_params["fine_threshold"]
        coarse_threshold = self.fusion_params["coarse_threshold"]
        
        # 筛选有效的投影仪数据
        valid_projectors = []
        valid_depth_maps = []
        
        for i, (depth_map, quality_info) in enumerate(zip(depth_maps, quality_infos)):
            if depth_map is not None and quality_info.get("is_valid", False):
                valid_projectors.append(i)
                valid_depth_maps.append(depth_map)
        
        fusion_info = {
            "total_projectors": len(depth_maps),
            "valid_projectors": valid_projectors,
            "valid_count": len(valid_projectors),
            "fine_threshold_20um": fine_threshold,
            "coarse_threshold": coarse_threshold
        }
        
        print(f"  📊 有效投影仪: {valid_projectors} ({len(valid_projectors)}/{len(depth_maps)})")
        print(f"  🎯 精细阈值: {fine_threshold*1000:.0f}微米, 粗糙阈值: {coarse_threshold*1000:.0f}微米")
        
        if len(valid_projectors) == 0:
            print("  ❌ 没有有效的投影仪数据")
            fusion_info["fusion_strategy"] = "failed"
            return np.full((2848, 2848), np.nan), fusion_info
        
        elif len(valid_projectors) == 1:
            print(f"  🎯 策略: 单投影仪 (投影仪 {valid_projectors[0]})")
            fusion_info["fusion_strategy"] = "single_projector"
            fused_depth = valid_depth_maps[0].copy()
        
        elif len(valid_projectors) == 2:
            print(f"  🎯 策略: 双投影仪20微米精细融合")
            fusion_info["fusion_strategy"] = "dual_projector_20um"
            fused_depth = self._fuse_two_depths_20um(valid_depth_maps[0], valid_depth_maps[1], fine_threshold)
        
        else:
            print(f"  🎯 策略: 多投影仪20微米精细融合")
            fusion_info["fusion_strategy"] = "multi_projector_20um"
            fused_depth = self._fuse_multiple_depths_20um(valid_depth_maps, fine_threshold)
        
        # 统计融合结果
        valid_mask = np.isfinite(fused_depth)
        fusion_info.update({
            "final_valid_pixels": np.sum(valid_mask),
            "final_coverage": np.sum(valid_mask) / fused_depth.size
        })
        
        if np.sum(valid_mask) > 0:
            valid_depths = fused_depth[valid_mask]
            fusion_info["final_depth_stats"] = {
                "min": np.min(valid_depths),
                "max": np.max(valid_depths),
                "mean": np.mean(valid_depths),
                "std": np.std(valid_depths),
                "range": np.max(valid_depths) - np.min(valid_depths)
            }
        
        print(f"  ✅ 融合完成: 覆盖率 {fusion_info['final_coverage']:.1%}")
        
        return fused_depth, fusion_info
    
    def _fuse_two_depths_20um(self, depth1: np.ndarray, depth2: np.ndarray, threshold: float) -> np.ndarray:
        """双投影仪20微米精细融合"""
        print(f"    🔀 执行双投影仪20微米精细融合...")
        
        valid1 = np.isfinite(depth1)
        valid2 = np.isfinite(depth2)
        fused = np.full_like(depth1, np.nan)
        
        # 两个都有效的位置
        both_valid = valid1 & valid2
        both_valid_count = np.sum(both_valid)
        
        if both_valid_count > 0:
            depth_diff = np.abs(depth1[both_valid] - depth2[both_valid])
            consistent_mask = depth_diff <= threshold
            
            # 一致区域：直接平均
            consistent_indices = np.where(both_valid)[0][consistent_mask], np.where(both_valid)[1][consistent_mask]
            if len(consistent_indices[0]) > 0:
                fused[consistent_indices] = (depth1[consistent_indices] + depth2[consistent_indices]) / 2
            
            # 不一致区域：智能选择
            inconsistent_mask = ~consistent_mask
            if np.sum(inconsistent_mask) > 0:
                inconsistent_indices = np.where(both_valid)[0][inconsistent_mask], np.where(both_valid)[1][inconsistent_mask]
                
                if len(consistent_indices[0]) > 100:
                    local_ref = np.median(fused[consistent_indices])
                else:
                    local_ref = np.median(np.concatenate([depth1[both_valid], depth2[both_valid]]))
                
                depth1_vals = depth1[inconsistent_indices]
                depth2_vals = depth2[inconsistent_indices]
                diff1 = np.abs(depth1_vals - local_ref)
                diff2 = np.abs(depth2_vals - local_ref)
                
                fused[inconsistent_indices] = np.where(diff1 <= diff2, depth1_vals, depth2_vals)
            
            consistent_count = np.sum(consistent_mask)
            inconsistent_count = np.sum(inconsistent_mask)
            print(f"      20微米内一致: {consistent_count:,} ({consistent_count/both_valid_count:.1%})")
            print(f"      不一致处理: {inconsistent_count:,} ({inconsistent_count/both_valid_count:.1%})")
        
        # 单独有效区域
        only1_valid = valid1 & (~valid2)
        only2_valid = valid2 & (~valid1)
        fused[only1_valid] = depth1[only1_valid]
        fused[only2_valid] = depth2[only2_valid]
        
        return fused
    
    def _fuse_multiple_depths_20um(self, depth_maps: List[np.ndarray], threshold: float) -> np.ndarray:
        """多投影仪20微米精细融合"""
        print(f"    🔀 执行多投影仪20微米精细融合...")
        
        height, width = depth_maps[0].shape
        fused = np.full((height, width), np.nan)
        
        consistent_pixels = 0
        outlier_removed = 0
        processed_pixels = 0
        
        for r in range(0, height, 2):  # 优化性能
            for c in range(0, width, 2):
                r_end = min(r + 2, height)
                c_end = min(c + 2, width)
                
                for rr in range(r, r_end):
                    for cc in range(c, c_end):
                        # 收集有效深度值
                        valid_depths = []
                        for depth_map in depth_maps:
                            if np.isfinite(depth_map[rr, cc]):
                                valid_depths.append(depth_map[rr, cc])
                        
                        if len(valid_depths) == 0:
                            continue
                        elif len(valid_depths) == 1:
                            fused[rr, cc] = valid_depths[0]
                        else:
                            valid_depths = np.array(valid_depths)
                            median_depth = np.median(valid_depths)
                            depth_diffs = np.abs(valid_depths - median_depth)
                            
                            # 20微米阈值检测
                            normal_mask = depth_diffs <= threshold
                            normal_depths = valid_depths[normal_mask]
                            
                            if len(normal_depths) > 0:
                                fused[rr, cc] = np.mean(normal_depths)
                                if len(normal_depths) == len(valid_depths):
                                    consistent_pixels += 1
                                else:
                                    outlier_removed += 1
                            else:
                                fused[rr, cc] = median_depth
                                outlier_removed += 1
                        
                        processed_pixels += 1
        
        print(f"      处理像素: {processed_pixels:,}")
        print(f"      20微米内一致: {consistent_pixels:,}")
        print(f"      异常值处理: {outlier_removed:,}")
        
        return fused
    
    def load_texture_images(self, test_folder: str, target_height: int, target_width: int) -> Optional[np.ndarray]:
        """加载并处理纹理图像"""
        print("    🎨 加载纹理图像...")
        
        test_path = Path(test_folder)
        
        # 尝试加载RGB纹理图像 (通常是81-83.bmp)
        texture_files = {
            'blue': test_path / "81.bmp",
            'green': test_path / "82.bmp", 
            'red': test_path / "83.bmp"
        }
        
        # 检查纹理文件是否存在
        missing_files = []
        for channel, file_path in texture_files.items():
            if not file_path.exists():
                missing_files.append(f"{channel}({file_path.name})")
        
        if missing_files:
            print(f"    ⚠️  缺少纹理文件: {', '.join(missing_files)}")
            return None
        
        try:
            # 加载RGB通道
            b_channel = cv2.imread(str(texture_files['blue']), cv2.IMREAD_GRAYSCALE)
            g_channel = cv2.imread(str(texture_files['green']), cv2.IMREAD_GRAYSCALE)
            r_channel = cv2.imread(str(texture_files['red']), cv2.IMREAD_GRAYSCALE)
            
            if any(img is None for img in [b_channel, g_channel, r_channel]):
                print("    ❌ 纹理图像读取失败")
                return None
            
            # 调整尺寸匹配深度图
            if b_channel.shape != (target_height, target_width):
                print(f"    🔧 调整纹理尺寸: {b_channel.shape} → ({target_height}, {target_width})")
                b_channel = cv2.resize(b_channel, (target_width, target_height), interpolation=cv2.INTER_LINEAR)
                g_channel = cv2.resize(g_channel, (target_width, target_height), interpolation=cv2.INTER_LINEAR)
                r_channel = cv2.resize(r_channel, (target_width, target_height), interpolation=cv2.INTER_LINEAR)
            
            # 合并为RGB图像
            texture_rgb = np.stack([r_channel, g_channel, b_channel], axis=-1)  # RGB格式
            
            print(f"    ✅ 纹理加载成功: {texture_rgb.shape}")
            return texture_rgb
            
        except Exception as e:
            print(f"    ❌ 纹理处理失败: {e}")
            return None
    
    def save_point_cloud(self, depth_map: np.ndarray, test_folder: str, output_file: str) -> int:
        """保存带纹理的点云"""
        print("    ☁️  生成带纹理点云...")
        
        height, width = depth_map.shape
        
        # 创建坐标网格
        x_coords = np.arange(width, dtype=np.float32) * self.CCD_DX
        y_coords = np.arange(height, dtype=np.float32) * self.CCD_DX
        x_grid, y_grid = np.meshgrid(x_coords, y_coords)
        
        # 创建点云
        points = np.stack((x_grid.ravel(), y_grid.ravel(), depth_map.ravel()), axis=-1)
        
        # 过滤有效点
        valid_mask = np.isfinite(depth_map) & (depth_map > -20) & (depth_map < 100)
        valid_mask_2d = valid_mask.copy()
        valid_mask = valid_mask.ravel()
        filtered_points = points[valid_mask]
        
        if len(filtered_points) == 0:
            print("    ⚠️  没有有效的点云数据")
            return 0
        
        # 加载真实纹理
        texture_rgb = self.load_texture_images(test_folder, height, width)
        
        if texture_rgb is not None:
            # 使用真实纹理
            print("    🎨 应用真实纹理...")
            texture_colors = texture_rgb.reshape(-1, 3)[valid_mask]
            colors = texture_colors.astype(np.uint8)
        else:
            # 使用深度伪彩色作为备选
            print("    🌈 使用深度伪彩色...")
            z_values = filtered_points[:, 2]
            z_min, z_max = np.min(z_values), np.max(z_values)
            z_range = z_max - z_min if z_max > z_min else 1
            z_normalized = (z_values - z_min) / z_range
            
            # 改进的伪彩色映射 (类似jet colormap)
            colors = np.zeros((len(filtered_points), 3), dtype=np.uint8)
            
            # Jet colormap implementation
            for i, norm_val in enumerate(z_normalized):
                if norm_val <= 0.125:
                    # Deep blue to blue
                    colors[i] = [0, 0, int(128 + 127 * norm_val / 0.125)]
                elif norm_val <= 0.375:
                    # Blue to cyan
                    t = (norm_val - 0.125) / 0.25
                    colors[i] = [0, int(255 * t), 255]
                elif norm_val <= 0.625:
                    # Cyan to yellow
                    t = (norm_val - 0.375) / 0.25
                    colors[i] = [int(255 * t), 255, int(255 * (1 - t))]
                elif norm_val <= 0.875:
                    # Yellow to red
                    t = (norm_val - 0.625) / 0.25
                    colors[i] = [255, int(255 * (1 - t)), 0]
                else:
                    # Red to dark red
                    t = (norm_val - 0.875) / 0.125
                    colors[i] = [int(255 * (1 - 0.5 * t)), 0, 0]
        
        # 保存PLY文件
        print(f"    💾 保存点云: {len(filtered_points):,} 点")
        with open(output_file, 'w') as f:
            f.write("ply\n")
            f.write("format ascii 1.0\n")
            f.write(f"element vertex {len(filtered_points)}\n")
            f.write("property float x\n")
            f.write("property float y\n")
            f.write("property float z\n")
            f.write("property uchar red\n")
            f.write("property uchar green\n")
            f.write("property uchar blue\n")
            f.write("end_header\n")
            
            for i in range(len(filtered_points)):
                x, y, z = filtered_points[i]
                r, g, b = colors[i]
                f.write(f"{x:.6f} {y:.6f} {z:.6f} {r} {g} {b}\n")
        
        texture_status = "真实纹理" if texture_rgb is not None else "深度伪彩色"
        print(f"    ✅ 点云保存完成: {output_file} ({texture_status})")
        
        return len(filtered_points)
    
    def save_comprehensive_results(self, fused_depth: np.ndarray, 
                                 fusion_info: Dict,
                                 quality_infos: List[Dict],
                                 test_folder: str,
                                 output_folder: str):
        """保存综合结果"""
        output_path = Path(output_folder)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 1. 保存融合深度图
        depth_file = output_path / "fused_depth_corrected.npy"
        np.save(depth_file, fused_depth)
        print(f"💾 融合深度图: {depth_file}")
        
        # 2. 保存点云
        point_cloud_file = output_path / "fused_point_cloud_corrected.ply"
        point_count = self.save_point_cloud(fused_depth, test_folder, str(point_cloud_file))
        print(f"☁️  点云文件: {point_cloud_file} ({point_count:,} 点)")
        
        # 3. 保存详细报告
        report_file = output_path / "depth_aware_fusion_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("深度感知融合系统处理报告\n")
            f.write("=" * 60 + "\n\n")
            
            f.write(f"测试数据: {test_folder}\n")
            f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"融合策略: {fusion_info['fusion_strategy']}\n")
            f.write(f"有效投影仪: {fusion_info['valid_projectors']} ({fusion_info['valid_count']}/{fusion_info['total_projectors']})\n\n")
            
            f.write("各投影仪处理结果:\n")
            f.write("-" * 40 + "\n")
            for quality_info in quality_infos:
                proj_id = quality_info["projector_id"]
                
                if "error" in quality_info:
                    f.write(f"投影仪 {proj_id}: ❌ 失败 - {quality_info['error']}\n")
                    continue
                
                status = "✅ 通过" if quality_info.get("is_valid", False) else "❌ 拒绝"
                f.write(f"投影仪 {proj_id}: {status}\n")
                f.write(f"  有效像素比例: {quality_info['valid_ratio']:.1%}\n")
                f.write(f"  质量分数: {quality_info['quality_score']:.3f}\n")
                
                if "correction_info" in quality_info:
                    corr = quality_info["correction_info"]
                    f.write(f"  深度域修正:\n")
                    f.write(f"    原始标准差: {corr['original_std']:.3f} mm\n")
                    f.write(f"    修正后标准差: {corr['final_std']:.3f} mm\n")
                    f.write(f"    改善程度: {corr['improvement_percent']:+.1f}%\n")
                    f.write(f"    异常点总数: {corr['total_outliers']:,} ({corr['outlier_ratio']:.1%})\n")
                
                if "depth_stats" in quality_info and quality_info["depth_stats"]:
                    stats = quality_info["depth_stats"]
                    f.write(f"  深度统计: {stats['min']:.2f} ~ {stats['max']:.2f} mm (σ={stats['std']:.3f})\n")
                
                f.write("\n")
            
            f.write("融合结果:\n")
            f.write("-" * 40 + "\n")
            f.write(f"最终有效像素: {fusion_info['final_valid_pixels']:,}\n")
            f.write(f"最终覆盖率: {fusion_info['final_coverage']:.2%}\n")
            f.write(f"点云点数: {point_count:,}\n")
            
            if "final_depth_stats" in fusion_info:
                stats = fusion_info["final_depth_stats"]
                f.write(f"\n最终深度统计 (mm):\n")
                f.write(f"  范围: {stats['min']:.2f} ~ {stats['max']:.2f}\n")
                f.write(f"  均值: {stats['mean']:.2f}\n")
                f.write(f"  标准差: {stats['std']:.3f}\n")
                f.write(f"  深度范围: {stats['range']:.2f}\n")
        
        print(f"📄 处理报告: {report_file}")
        
        # 4. 保存配置参数
        config_file = output_path / "processing_config.json"
        config_data = {
            "depth_correction_params": self.depth_correction_params,
            "fusion_params": self.fusion_params,
            "periods": self.periods,
            "CCD_DX": self.CCD_DX
        }
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
        print(f"⚙️  配置参数: {config_file}")
    
    def process_dataset(self, test_folder: str, output_folder: str):
        """处理完整数据集"""
        dataset_name = Path(test_folder).name
        print(f"\n{'='*80}")
        print(f"🎯 深度感知融合系统 - 处理数据集: {dataset_name}")
        print(f"{'='*80}")
        
        start_time = time.time()
        
        try:
            # 检查输入
            if not Path(test_folder).exists():
                raise FileNotFoundError(f"测试文件夹不存在: {test_folder}")
            
            # 1. 重建并修正各投影仪深度图
            print(f"\n🔄 阶段1: 单投影仪深度重建与修正")
            print("-" * 50)
            
            depth_maps = []
            quality_infos = []
            
            for proj_id in range(4):
                depth_map, quality_info = self.reconstruct_and_correct_single_projector(test_folder, proj_id)
                depth_maps.append(depth_map)
                quality_infos.append(quality_info)
            
            # 2. 多投影仪融合
            print(f"\n🔀 阶段2: 多投影仪自适应融合")
            print("-" * 50)
            
            fused_depth, fusion_info = self.adaptive_multi_projector_fusion(depth_maps, quality_infos)
            
            # 3. 保存结果
            print(f"\n💾 阶段3: 保存综合结果")
            print("-" * 50)
            
            self.save_comprehensive_results(fused_depth, fusion_info, quality_infos, test_folder, output_folder)
            
            # 4. 处理总结
            processing_time = time.time() - start_time
            
            print(f"\n{'='*80}")
            print(f"🎉 数据集 '{dataset_name}' 处理完成!")
            print(f"{'='*80}")
            print(f"⏱️  处理时间: {processing_time:.1f} 秒")
            print(f"📊 融合策略: {fusion_info['fusion_strategy']}")
            print(f"📈 有效投影仪: {fusion_info['valid_projectors']} ({fusion_info['valid_count']}/4)")
            print(f"🎯 最终覆盖率: {fusion_info['final_coverage']:.1%}")
            
            if "final_depth_stats" in fusion_info:
                stats = fusion_info["final_depth_stats"]
                print(f"📏 深度范围: {stats['min']:.2f} ~ {stats['max']:.2f} mm")
                print(f"📐 深度精度: σ = {stats['std']:.3f} mm")
            
            point_count = fusion_info.get("point_count", 0)
            if point_count > 0:
                print(f"☁️  点云规模: {point_count:,} 点")
            
            print(f"📁 结果保存: {output_folder}")
            print(f"{'='*80}")
            
            return True
            
        except Exception as e:
            print(f"\n❌ 处理失败: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="深度感知融合系统 - 单投影仪深度域修正 + 多投影仪融合")
    parser.add_argument("--test_folder", type=str, default="测试/Test", help="测试数据文件夹")
    parser.add_argument("--calibration_folder", type=str, default="final_calibration_results_four_freq", help="标定结果文件夹")
    parser.add_argument("--output_folder", type=str, default="depth_aware_fusion_results", help="输出文件夹")
    
    args = parser.parse_args()
    
    print("🚀 深度感知融合系统")
    print("📋 功能: 单投影仪深度域修正 + 多投影仪精密融合")
    print(f"📂 测试数据: {args.test_folder}")
    print(f"📂 标定数据: {args.calibration_folder}")
    print(f"📂 输出目录: {args.output_folder}")
    
    # 创建系统实例
    try:
        fusion_system = DepthAwareFusionSystem(args.calibration_folder)
        
        # 处理数据集
        success = fusion_system.process_dataset(args.test_folder, args.output_folder)
        
        if success:
            print("\n🎉 系统处理成功完成!")
        else:
            print("\n❌ 系统处理失败!")
            exit(1)
            
    except Exception as e:
        print(f"\n💥 系统初始化失败: {e}")
        import traceback
        traceback.print_exc()
        exit(1)


if __name__ == "__main__":
    main() 