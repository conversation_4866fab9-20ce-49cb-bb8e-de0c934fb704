"""
adaptive_quality_fusion.py
==========================

基于数据质量的自适应融合算法

融合策略：
1. 判定四组光机数据有无异常值，有则去掉然后正常融合
2. 如果四个光机数据不全，按已有的数据进行融合
3. 如果只有一组光机数据正常，使用该单光机数据

作者：AI Assistant
日期：2025-07-05
"""

import numpy as np
import cv2
from pathlib import Path
from PIL import Image
from typing import List, Tuple, Dict, Optional
import argparse
from scipy import stats

# 导入优化的相位展开算法
from practical_phase_unwrap import PracticalPhaseUnwrapper
# 导入点云生成工具
from generate_point_cloud import save_colored_point_cloud_to_ply, CCD_DX

class AdaptiveQualityFusion:
    """基于数据质量的自适应融合器"""
    
    def __init__(self, 
                 use_improved_calibration: bool = True,
                 calibration_prefix: str = "fast_improved_calibration",
                 outlier_threshold: float = 3.0,
                 min_valid_ratio: float = 0.1):
        """
        初始化自适应融合器
        
        参数:
            use_improved_calibration: 是否使用改进的标定结果
            calibration_prefix: 标定文件前缀
            outlier_threshold: 异常值检测阈值（标准差倍数）
            min_valid_ratio: 最小有效数据比例
        """
        self.use_improved_calibration = use_improved_calibration
        self.calibration_prefix = calibration_prefix
        self.outlier_threshold = outlier_threshold
        self.min_valid_ratio = min_valid_ratio
        
        # 相位展开参数
        self.periods = [1080.0, 512.0, 64.0, 8.0]
        
        # 图像组织参数
        self.block_size = 20
        self.indices_by_freq = {
            8: np.arange(0, 5),       # 1-5
            64: np.arange(5, 10),     # 6-10
            512: np.arange(10, 15),   # 11-15
            1080: np.arange(15, 20),  # 16-20
        }
        
        # 按低频到高频顺序
        self.frames_ordered = [
            self.indices_by_freq[1080],
            self.indices_by_freq[512],
            self.indices_by_freq[64],
            self.indices_by_freq[8],
        ]
        
        # 初始化相位展开器
        self.phase_unwrapper = PracticalPhaseUnwrapper(self.periods)
    
    def load_frames(self, folder: Path, frame_numbers: List[int]) -> np.ndarray:
        """按给定文件序号读取图像"""
        frames = []
        for num in frame_numbers:
            img_path = folder / f"{num}.bmp"
            if not img_path.exists():
                raise FileNotFoundError(f"图像文件不存在: {img_path}")
            
            img = Image.open(img_path).convert("L")
            frames.append(np.asarray(img, dtype=np.float32) / 255.0)
        
        return np.stack(frames, axis=-1)  # (H,W,5)
    
    def gather_engine_data(self, folder: Path, engine_idx: int) -> List[np.ndarray]:
        """收集指定光机的四频率数据"""
        start = engine_idx * self.block_size  # 0-based
        
        frames_list = []
        for rel_indices in self.frames_ordered:
            # 转换为绝对的1-based文件名
            nums = (start + rel_indices + 1).tolist()
            frames = self.load_frames(folder, nums)
            frames_list.append(frames)
        
        return frames_list  # len=4, 每个 (H,W,5)
    
    def load_calibration_parameters(self, engine_idx: int) -> Tuple[np.ndarray, np.ndarray]:
        """加载标定参数"""
        if self.use_improved_calibration:
            slope_file = f"{self.calibration_prefix}_engine{engine_idx}_slope.npy"
            intercept_file = f"{self.calibration_prefix}_engine{engine_idx}_intercept.npy"
        else:
            slope_file = f"calibration_engine{engine_idx}_slope.npy"
            intercept_file = f"calibration_engine{engine_idx}_intercept.npy"
        
        if not Path(slope_file).exists():
            raise FileNotFoundError(f"标定文件不存在: {slope_file}")
        if not Path(intercept_file).exists():
            raise FileNotFoundError(f"标定文件不存在: {intercept_file}")
        
        slope = np.load(slope_file)
        intercept = np.load(intercept_file)
        
        return slope, intercept
    
    def phase_to_height(self, phase: np.ndarray, 
                       slope: np.ndarray, 
                       intercept: np.ndarray) -> np.ndarray:
        """相位转高度"""
        # 确保尺寸匹配
        if phase.shape != slope.shape or phase.shape != intercept.shape:
            if slope.shape != phase.shape:
                slope = cv2.resize(slope, (phase.shape[1], phase.shape[0]), interpolation=cv2.INTER_LINEAR)
            if intercept.shape != phase.shape:
                intercept = cv2.resize(intercept, (phase.shape[1], phase.shape[0]), interpolation=cv2.INTER_LINEAR)
        
        # 计算高度
        height = slope * phase + intercept
        
        # 处理无效值
        valid_phase = np.isfinite(phase)
        valid_slope = np.isfinite(slope) & (np.abs(slope) > 1e-6)
        valid_intercept = np.isfinite(intercept)
        
        valid_mask = valid_phase & valid_slope & valid_intercept
        
        # 在无效区域设为NaN
        height[~valid_mask] = np.nan
        
        return height
    
    def analyze_depth_quality(self, depth_map: np.ndarray, engine_idx: int) -> Dict:
        """分析单个光机深度图的质量"""
        total_pixels = depth_map.size
        
        # 有效性判断
        valid_mask = np.isfinite(depth_map)
        valid_count = np.sum(valid_mask)
        valid_ratio = valid_count / total_pixels
        
        quality_info = {
            "engine_idx": engine_idx,
            "total_pixels": total_pixels,
            "valid_pixels": valid_count,
            "valid_ratio": valid_ratio,
            "is_valid": valid_ratio >= self.min_valid_ratio
        }
        
        if valid_count > 0:
            valid_depths = depth_map[valid_mask]
            
            # 基本统计
            depth_stats = {
                "min": np.min(valid_depths),
                "max": np.max(valid_depths),
                "mean": np.mean(valid_depths),
                "std": np.std(valid_depths),
                "median": np.median(valid_depths),
                "range": np.max(valid_depths) - np.min(valid_depths)
            }
            
            # 异常值检测
            z_scores = np.abs(stats.zscore(valid_depths))
            outlier_mask = z_scores > self.outlier_threshold
            outlier_count = np.sum(outlier_mask)
            outlier_ratio = outlier_count / len(valid_depths)
            
            # 质量评估
            quality_score = self.calculate_quality_score(depth_stats, outlier_ratio, valid_ratio)
            
            quality_info.update({
                "depth_stats": depth_stats,
                "outlier_count": outlier_count,
                "outlier_ratio": outlier_ratio,
                "quality_score": quality_score,
                "has_outliers": outlier_ratio > 0.2,  # 20%以上异常值认为有问题
                "outlier_mask": outlier_mask
            })
        else:
            quality_info.update({
                "depth_stats": None,
                "outlier_count": 0,
                "outlier_ratio": 0,
                "quality_score": 0,
                "has_outliers": False,
                "outlier_mask": None
            })
        
        return quality_info
    
    def calculate_quality_score(self, depth_stats: Dict, outlier_ratio: float, valid_ratio: float) -> float:
        """计算质量分数"""
        # 基础分数：有效比例
        score = valid_ratio * 0.4
        
        # 稳定性分数：标准差越小越好
        if depth_stats["std"] > 0:
            stability_score = max(0, 1 - depth_stats["std"] / 20)  # 假设20mm为最大可接受标准差
            score += stability_score * 0.3
        
        # 异常值分数：异常值越少越好
        outlier_score = max(0, 1 - outlier_ratio * 2)  # 50%异常值时分数为0
        score += outlier_score * 0.3
        
        return min(score, 1.0)
    
    def remove_outliers(self, depth_map: np.ndarray, outlier_mask: np.ndarray) -> np.ndarray:
        """移除异常值"""
        cleaned_depth = depth_map.copy()
        valid_mask = np.isfinite(depth_map)
        
        # 将异常值设为NaN
        full_outlier_mask = np.zeros_like(depth_map, dtype=bool)
        full_outlier_mask[valid_mask] = outlier_mask
        
        cleaned_depth[full_outlier_mask] = np.nan
        
        return cleaned_depth
    
    def reconstruct_single_engine(self, pcb_dir: str, engine_idx: int) -> Tuple[np.ndarray, Dict]:
        """重建单个光机的深度图"""
        print(f"重建光机 {engine_idx}...")
        
        try:
            pcb_path = Path(pcb_dir)
            
            # 1. 加载图像数据
            frames_list = self.gather_engine_data(pcb_path, engine_idx)
            
            # 2. 相位展开
            phase, phase_info = self.phase_unwrapper.unwrap_four_frequency_practical(frames_list)
            
            # 3. 加载标定参数
            slope, intercept = self.load_calibration_parameters(engine_idx)
            
            # 4. 相位转高度
            depth_map = self.phase_to_height(phase, slope, intercept)
            
            # 5. 分析质量
            quality_info = self.analyze_depth_quality(depth_map, engine_idx)
            quality_info["phase_info"] = phase_info
            
            print(f"  光机 {engine_idx}: 有效比例 {quality_info['valid_ratio']:.1%}, "
                  f"质量分数 {quality_info['quality_score']:.3f}")
            
            return depth_map, quality_info
            
        except Exception as e:
            print(f"  光机 {engine_idx} 重建失败: {e}")
            return None, {"engine_idx": engine_idx, "error": str(e), "is_valid": False}
    
    def adaptive_fusion(self, depth_maps: List[np.ndarray], 
                       quality_infos: List[Dict]) -> Tuple[np.ndarray, Dict]:
        """自适应融合算法 - 基于20微米阈值的异常检测"""
        print("\n执行自适应质量融合（20微米阈值异常检测）...")
        
        # 20微米的阈值（单位：mm）
        DEPTH_THRESHOLD = 0.02  # 20微米 = 0.02毫米
        
        # 1. 筛选有效的光机数据
        valid_engines = []
        valid_depth_maps = []
        
        for i, (depth_map, quality_info) in enumerate(zip(depth_maps, quality_infos)):
            if depth_map is not None and quality_info.get("is_valid", False):
                valid_engines.append(i)
                valid_depth_maps.append(depth_map)
        
        fusion_info = {
            "total_engines": len(depth_maps),
            "valid_engines": valid_engines,
            "valid_count": len(valid_engines),
            "fusion_strategy": "",
            "depth_threshold": DEPTH_THRESHOLD
        }
        
        print(f"  有效光机数量: {len(valid_engines)}/{len(depth_maps)}")
        print(f"  深度差异阈值: {DEPTH_THRESHOLD*1000:.0f}微米")
        
        # 2. 根据有效光机数量选择融合策略
        if len(valid_engines) == 0:
            print("  错误: 没有有效的光机数据")
            fusion_info["fusion_strategy"] = "failed"
            return np.full((2848, 2848), np.nan), fusion_info
        
        elif len(valid_engines) == 1:
            print(f"  策略: 使用单光机数据 (光机 {valid_engines[0]})")
            fusion_info["fusion_strategy"] = "single_engine"
            fused_depth = valid_depth_maps[0].copy()
        
        elif len(valid_engines) == 2:
            print(f"  策略: 双光机20微米阈值融合 (光机 {valid_engines})")
            fusion_info["fusion_strategy"] = "dual_engine_20um"
            fused_depth = self.fuse_two_engines_20um_threshold(
                valid_depth_maps[0], valid_depth_maps[1], DEPTH_THRESHOLD)
        
        elif len(valid_engines) >= 3:
            print(f"  策略: 多光机20微米阈值融合 (光机 {valid_engines})")
            fusion_info["fusion_strategy"] = "multi_engine_20um"
            fused_depth = self.fuse_multiple_engines_20um_threshold(
                valid_depth_maps, DEPTH_THRESHOLD)
        
        # 3. 计算融合后的统计信息
        valid_mask = np.isfinite(fused_depth)
        fusion_info.update({
            "final_valid_pixels": np.sum(valid_mask),
            "final_coverage": np.sum(valid_mask) / fused_depth.size
        })
        
        if np.sum(valid_mask) > 0:
            valid_depths = fused_depth[valid_mask]
            fusion_info["final_depth_stats"] = {
                "min": np.min(valid_depths),
                "max": np.max(valid_depths),
                "mean": np.mean(valid_depths),
                "std": np.std(valid_depths),
                "range": np.max(valid_depths) - np.min(valid_depths)
            }
        
        return fused_depth, fusion_info
    
    def fuse_two_engines_20um_threshold(self, depth1: np.ndarray, depth2: np.ndarray, 
                                       threshold: float = 0.02) -> np.ndarray:
        """双光机融合 - 20微米阈值异常检测"""
        print(f"    执行双光机20微米阈值融合...")
        
        valid1 = np.isfinite(depth1)
        valid2 = np.isfinite(depth2)
        
        fused = np.full_like(depth1, np.nan)
        
        # 两个都有效的位置
        both_valid = valid1 & valid2
        both_valid_count = np.sum(both_valid)
        
        if both_valid_count > 0:
            # 计算深度差异
            depth_diff = np.abs(depth1[both_valid] - depth2[both_valid])
            
            # 检查哪些位置的差异超过阈值
            abnormal_mask = depth_diff > threshold
            normal_mask = ~abnormal_mask
            
            abnormal_count = np.sum(abnormal_mask)
            normal_count = np.sum(normal_mask)
            
            print(f"      两个光机都有效: {both_valid_count:,} 像素")
            print(f"      深度差异正常: {normal_count:,} 像素 ({normal_count/both_valid_count:.1%})")
            print(f"      深度差异异常: {abnormal_count:,} 像素 ({abnormal_count/both_valid_count:.1%})")
            
            # 创建索引数组来映射回原始位置
            both_valid_indices = np.where(both_valid)
            
            # 正常位置：取平均值
            if normal_count > 0:
                normal_indices = (both_valid_indices[0][normal_mask], both_valid_indices[1][normal_mask])
                fused[normal_indices] = (depth1[normal_indices] + depth2[normal_indices]) / 2
            
            # 异常位置：去掉异常值，保留更合理的那个
            if abnormal_count > 0:
                abnormal_indices = (both_valid_indices[0][abnormal_mask], both_valid_indices[1][abnormal_mask])
                
                # 简单策略：选择与周围区域更一致的值
                # 这里我们选择中位数值（更稳定）
                depth1_abnormal = depth1[abnormal_indices]
                depth2_abnormal = depth2[abnormal_indices]
                
                # 使用更保守的策略：选择距离均值更近的那个
                combined_depths = np.concatenate([depth1_abnormal, depth2_abnormal])
                overall_median = np.median(combined_depths)
                
                diff1_to_median = np.abs(depth1_abnormal - overall_median)
                diff2_to_median = np.abs(depth2_abnormal - overall_median)
                
                # 选择距离中位数更近的值
                choose_depth1 = diff1_to_median <= diff2_to_median
                
                fused[abnormal_indices] = np.where(choose_depth1, depth1_abnormal, depth2_abnormal)
                
                removed_count = np.sum(~choose_depth1) + np.sum(choose_depth1)
                print(f"      异常位置处理: 保留光机1 {np.sum(choose_depth1)} 个，光机2 {np.sum(~choose_depth1)} 个")
        
        # 只有一个有效的位置：直接使用该值
        only1_valid = valid1 & (~valid2)
        only2_valid = valid2 & (~valid1)
        
        fused[only1_valid] = depth1[only1_valid]
        fused[only2_valid] = depth2[only2_valid]
        
        print(f"      仅光机1有效: {np.sum(only1_valid):,} 像素")
        print(f"      仅光机2有效: {np.sum(only2_valid):,} 像素")
        
        return fused
    
    def fuse_multiple_engines_20um_threshold(self, depth_maps: List[np.ndarray], 
                                           threshold: float = 0.02) -> np.ndarray:
        """多光机融合（3个或4个）- 20微米阈值异常检测"""
        print(f"    执行多光机20微米阈值融合...")
        
        if len(depth_maps) < 3:
            raise ValueError("此方法需要至少3个深度图")
        
        depth_stack = np.stack(depth_maps, axis=0)  # (N, H, W)
        valid_mask = np.isfinite(depth_stack)  # (N, H, W)
        valid_count = np.sum(valid_mask, axis=0)  # (H, W)
        
        fused = np.full_like(depth_maps[0], np.nan)
        
        height, width = depth_maps[0].shape
        
        # 统计信息
        processed_pixels = 0
        interpolated_pixels = 0
        outlier_removed_count = 0
        
        for r in range(height):
            for c in range(width):
                pixel_valid_count = valid_count[r, c]
                
                if pixel_valid_count == 0:
                    # 没有有效数据，保持NaN
                    continue
                elif pixel_valid_count == 1:
                    # 只有一个有效值，直接使用
                    for engine_idx in range(len(depth_maps)):
                        if valid_mask[engine_idx, r, c]:
                            fused[r, c] = depth_stack[engine_idx, r, c]
                            break
                    processed_pixels += 1
                else:
                    # 多个有效值，检查异常
                    valid_depths = depth_stack[valid_mask[:, r, c], r, c]
                    
                    if len(valid_depths) == 2:
                        # 两个值：检查差异
                        depth_diff = abs(valid_depths[0] - valid_depths[1])
                        if depth_diff <= threshold:
                            # 差异在阈值内，取平均
                            fused[r, c] = np.mean(valid_depths)
                        else:
                            # 差异超过阈值，选择更合理的值
                            # 使用周边像素信息辅助判断
                            median_val = np.median(valid_depths)
                            diff_to_median = np.abs(valid_depths - median_val)
                            fused[r, c] = valid_depths[np.argmin(diff_to_median)]
                            outlier_removed_count += 1
                    else:
                        # 三个或更多值：检查是否有异常值
                        median_depth = np.median(valid_depths)
                        depth_diffs = np.abs(valid_depths - median_depth)
                        
                        # 找出差异在阈值内的值
                        normal_mask = depth_diffs <= threshold
                        normal_depths = valid_depths[normal_mask]
                        
                        if len(normal_depths) > 0:
                            # 有正常值，使用正常值的平均
                            fused[r, c] = np.mean(normal_depths)
                            if len(normal_depths) < len(valid_depths):
                                outlier_removed_count += 1
                        else:
                            # 所有值都异常，使用插值
                            interpolated_value = self.interpolate_from_neighbors(fused, r, c, height, width)
                            if np.isfinite(interpolated_value):
                                fused[r, c] = interpolated_value
                                interpolated_pixels += 1
                            else:
                                # 插值失败，使用中位数
                                fused[r, c] = median_depth
                    
                    processed_pixels += 1
        
        print(f"      处理像素: {processed_pixels:,}")
        print(f"      移除异常值: {outlier_removed_count:,} 位置")
        print(f"      插值处理: {interpolated_pixels:,} 位置")
        
        return fused
    
    def interpolate_from_neighbors(self, depth_map: np.ndarray, row: int, col: int, 
                                 height: int, width: int, window_size: int = 5) -> float:
        """从周边像素插值"""
        # 定义邻域窗口
        half_window = window_size // 2
        
        r_start = max(0, row - half_window)
        r_end = min(height, row + half_window + 1)
        c_start = max(0, col - half_window)
        c_end = min(width, col + half_window + 1)
        
        # 提取邻域
        neighborhood = depth_map[r_start:r_end, c_start:c_end]
        
        # 找出有效的邻居值
        valid_neighbors = neighborhood[np.isfinite(neighborhood)]
        
        if len(valid_neighbors) >= 3:
            # 使用邻居的中位数
            return np.median(valid_neighbors)
        elif len(valid_neighbors) > 0:
            # 邻居太少，使用均值
            return np.mean(valid_neighbors)
        else:
            # 没有有效邻居
            return np.nan
    
    def load_color_texture(self, pcb_dir: str = 'pcb') -> np.ndarray:
        """加载彩色纹理图像"""
        try:
            blue_channel_path = Path(pcb_dir) / '81.bmp'
            green_channel_path = Path(pcb_dir) / '82.bmp'
            red_channel_path = Path(pcb_dir) / '83.bmp'

            # 使用cv2.imdecode处理中文路径
            def read_image_chinese_path(image_path):
                try:
                    # 方法1: 使用numpy和cv2.imdecode
                    with open(image_path, 'rb') as f:
                        image_data = f.read()
                    image_array = np.frombuffer(image_data, np.uint8)
                    image = cv2.imdecode(image_array, cv2.IMREAD_GRAYSCALE)
                    return image
                except:
                    # 方法2: 使用PIL作为备选
                    try:
                        from PIL import Image
                        pil_image = Image.open(image_path).convert('L')
                        return np.array(pil_image)
                    except:
                        return None

            b = read_image_chinese_path(str(blue_channel_path))
            g = read_image_chinese_path(str(green_channel_path))
            r = read_image_chinese_path(str(red_channel_path))

            if b is None or g is None or r is None:
                raise FileNotFoundError("一个或多个颜色通道图像缺失或无法读取。")

            color_texture_bgr = cv2.merge([b, g, r])
            color_texture = cv2.cvtColor(color_texture_bgr, cv2.COLOR_BGR2RGB)

            print("彩色纹理图加载成功。")
            return color_texture

        except Exception as e:
            print(f"加载彩色图像时出错: {e}")
            return None
    
    def save_point_cloud(self, depth_map: np.ndarray, 
                        output_path: str,
                        pcb_dir: str = "pcb") -> int:
        """保存点云"""
        height, width = depth_map.shape
        
        # 创建坐标网格
        c_coords = np.arange(width, dtype=np.float32)
        r_coords = np.arange(height, dtype=np.float32)
        c_grid, r_grid = np.meshgrid(c_coords, r_coords)
        
        # 计算3D坐标
        x_coords = c_grid * CCD_DX
        y_coords = r_grid * CCD_DX
        z_coords = depth_map
        
        # 组装点云
        points = np.stack((x_coords.ravel(), y_coords.ravel(), z_coords.ravel()), axis=-1)
        
        # 过滤有效点
        valid_mask = np.isfinite(z_coords) & (z_coords > -10) & (z_coords < 50)
        valid_mask = valid_mask.ravel()
        filtered_points = points[valid_mask]
        
        if len(filtered_points) == 0:
            print("警告: 没有有效的点云数据")
            return 0
        
        # 处理颜色
        color_texture = self.load_color_texture(pcb_dir)
        if color_texture is not None and color_texture.shape[:2] == (height, width):
            colors_rgb = color_texture.reshape(-1, 3)
            filtered_colors = colors_rgb[valid_mask]
        else:
            filtered_colors = np.full((len(filtered_points), 3), [128, 128, 128], dtype=np.uint8)
        
        # 保存点云
        save_colored_point_cloud_to_ply(output_path, filtered_points, filtered_colors)
        
        return len(filtered_points)
    
    def print_fusion_info(self, fusion_info: Dict, quality_infos: List[Dict]):
        """打印融合信息"""
        print(f"\n{'='*60}")
        print(f"自适应质量融合结果")
        print(f"{'='*60}")
        
        print(f"融合策略: {fusion_info['fusion_strategy']}")
        print(f"有效光机: {fusion_info['valid_engines']} ({fusion_info['valid_count']}/{fusion_info['total_engines']})")
        
        print(f"\n各光机质量分析:")
        for quality_info in quality_infos:
            engine_idx = quality_info["engine_idx"]
            if "error" in quality_info:
                print(f"  光机 {engine_idx}: 失败 - {quality_info['error']}")
            elif quality_info.get("is_valid", False):
                score = quality_info.get("quality_score", 0)
                valid_ratio = quality_info.get("valid_ratio", 0)
                outlier_ratio = quality_info.get("outlier_ratio", 0)
                print(f"  光机 {engine_idx}: 质量分数 {score:.3f}, 有效比例 {valid_ratio:.1%}, 异常值 {outlier_ratio:.1%}")
            else:
                print(f"  光机 {engine_idx}: 无效 - 有效数据不足")
        
        print(f"\n融合结果:")
        print(f"  最终有效像素: {fusion_info['final_valid_pixels']:,}")
        print(f"  最终覆盖率: {fusion_info['final_coverage']:.2%}")
        
        if "final_depth_stats" in fusion_info:
            stats = fusion_info["final_depth_stats"]
            print(f"\n深度统计 (mm):")
            print(f"  范围: {stats['min']:.1f} ~ {stats['max']:.1f}")
            print(f"  均值: {stats['mean']:.1f}")
            print(f"  标准差: {stats['std']:.3f}")
            print(f"  深度范围: {stats['range']:.1f}")

def main():
    parser = argparse.ArgumentParser(description="基于数据质量的自适应融合重建")
    parser.add_argument("--pcb_dir", type=str, default="pcb", help="PCB图像目录")
    parser.add_argument("--output_dir", type=str, default="adaptive_fusion_results", help="输出目录")
    parser.add_argument("--no_improved_calibration", action="store_true", help="不使用改进的标定")
    parser.add_argument("--calibration_prefix", type=str, default="fast_improved_calibration", help="标定文件前缀")
    parser.add_argument("--outlier_threshold", type=float, default=3.0, help="异常值检测阈值")
    parser.add_argument("--min_valid_ratio", type=float, default=0.1, help="最小有效数据比例")
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # 创建融合器
    fusion_engine = AdaptiveQualityFusion(
        use_improved_calibration=not args.no_improved_calibration,
        calibration_prefix=args.calibration_prefix,
        outlier_threshold=args.outlier_threshold,
        min_valid_ratio=args.min_valid_ratio
    )
    
    try:
        print("开始自适应质量融合重建...")
        
        # 重建所有光机
        depth_maps = []
        quality_infos = []
        
        for engine_idx in range(4):
            depth_map, quality_info = fusion_engine.reconstruct_single_engine(args.pcb_dir, engine_idx)
            depth_maps.append(depth_map)
            quality_infos.append(quality_info)
        
        # 执行自适应融合
        fused_depth, fusion_info = fusion_engine.adaptive_fusion(depth_maps, quality_infos)
        
        # 打印融合信息
        fusion_engine.print_fusion_info(fusion_info, quality_infos)
        
        # 保存结果
        depth_file = output_dir / "adaptive_fused_depth.npy"
        np.save(depth_file, fused_depth)
        print(f"\n融合深度图已保存: {depth_file}")
        
        # 保存点云
        pointcloud_file = output_dir / "adaptive_fused_pointcloud.ply"
        point_count = fusion_engine.save_point_cloud(
            fused_depth, str(pointcloud_file), args.pcb_dir)
        print(f"融合点云已保存: {pointcloud_file} (点数: {point_count:,})")
        
        print(f"\n所有结果已保存到: {output_dir}")
        
    except Exception as e:
        print(f"融合失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
