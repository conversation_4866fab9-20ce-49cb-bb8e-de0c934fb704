相位差标定详细报告
======================================================================

标定概述:
- 标定时间: 2025-07-25 17:57:49
- 标定方法: 相位差法
- 成功标定投影仪数量: 4
- 使用频率: 1080像素周期
- 参考位置: Test-0
- 高度范围: 0-10mm (步长1mm)

方法说明:
相位差法是通过计算各高度位置与参考位置的相位差来建立
相位差与高度的映射关系。这种方法避免了相位展开的复杂性，
直接利用包裹相位的差值进行标定，具有更高的稳定性和精度。

整体质量评估:
- 整体质量: Good
- 平均R²分数: 0.967630
- 平均RMSE: 0.5689 mm
- 平均相关系数: 0.982060

各投影仪详细信息:
----------------------------------------------------------------------

投影仪 0 (Projector_0):
  频率: 1080像素周期
  参考位置: Test-0
  模型类型: polynomial_2
  性能指标:
    R² 分数: 0.967001
    RMSE: 0.5744 mm
    相关系数: 0.981673
    残差标准差: 0.5744 mm
  数据统计:
    样本数量: 89,221
    相位差范围: [-0.010, 0.838] rad
    高度范围: [0.0, 10.0] mm
  质量评估: Good

投影仪 1 (Projector_1):
  频率: 1080像素周期
  参考位置: Test-0
  模型类型: polynomial_2
  性能指标:
    R² 分数: 0.967211
    RMSE: 0.5726 mm
    相关系数: 0.981906
    残差标准差: 0.5726 mm
  数据统计:
    样本数量: 89,221
    相位差范围: [-0.007, 0.832] rad
    高度范围: [0.0, 10.0] mm
  质量评估: Good

投影仪 2 (Projector_2):
  频率: 1080像素周期
  参考位置: Test-0
  模型类型: polynomial_2
  性能指标:
    R² 分数: 0.968744
    RMSE: 0.5591 mm
    相关系数: 0.982633
    残差标准差: 0.5591 mm
  数据统计:
    样本数量: 89,221
    相位差范围: [0.000, 0.848] rad
    高度范围: [0.0, 10.0] mm
  质量评估: Good

投影仪 3 (Projector_3):
  频率: 1080像素周期
  参考位置: Test-0
  模型类型: polynomial_2
  性能指标:
    R² 分数: 0.967562
    RMSE: 0.5695 mm
    相关系数: 0.982026
    残差标准差: 0.5695 mm
  数据统计:
    样本数量: 89,221
    相位差范围: [-0.011, 0.843] rad
    高度范围: [0.0, 10.0] mm
  质量评估: Good

使用说明:
1. 标定模型已保存在 calibration_models.pkl 文件中
2. 使用时需要提供当前相位图和参考相位图
3. 调用 predict_height_from_phase_difference() 方法进行高度预测
4. 参考相位图应该是Test-0位置的相位图
5. 确保输入相位差的范围在标定范围内

建议:
1. 标定质量优秀，可以用于高精度测量
2. 测量精度良好 (RMSE < 1.0mm)
