#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于相位差的最终标定程序
使用相位差方法进行高精度的相位高度映射标定
"""

import numpy as np
import cv2
import os
import matplotlib.pyplot as plt
import pickle
import json
from typing import List, Tuple, Dict, Optional
from sklearn.linear_model import LinearRegression, RANSACRegressor
from sklearn.preprocessing import PolynomialFeatures
from sklearn.pipeline import Pipeline
from sklearn.metrics import r2_score, mean_squared_error
import warnings
warnings.filterwarnings('ignore')


class PhaseDifferenceCalibration:
    """基于相位差的标定类"""
    
    def __init__(self, calibration_data_folder: str = "标定数据"):
        """初始化标定器"""
        self.calibration_data_folder = calibration_data_folder
        
        # 投影仪配置
        self.projector_configs = {
            0: {"name": "Projector_0", "image_range": (1, 20)},
            1: {"name": "Projector_1", "image_range": (21, 40)},
            2: {"name": "Projector_2", "image_range": (41, 60)},
            3: {"name": "Projector_3", "image_range": (61, 80)}
        }
        
        # 频率配置
        self.frequency_configs = {
            8: {"image_indices": [1, 2, 3, 4, 5], "name": "8px_period"},      
            64: {"image_indices": [6, 7, 8, 9, 10], "name": "64px_period"},    
            512: {"image_indices": [11, 12, 13, 14, 15], "name": "512px_period"}, 
            1080: {"image_indices": [16, 17, 18, 19, 20], "name": "1080px_period"} 
        }
        
        self.height_step = 1.0  # mm
        self.num_positions = 11
        self.calibration_results = {}
        
    def load_phase_shift_images(self, test_folder: str, projector_id: int, frequency: int) -> List[np.ndarray]:
        """加载五步相移图像"""
        start_idx, _ = self.projector_configs[projector_id]["image_range"]
        img_indices = self.frequency_configs[frequency]["image_indices"]
        
        images = []
        for img_idx in img_indices:
            actual_img_idx = start_idx + img_idx - 1
            img_path = os.path.join(test_folder, f"{actual_img_idx}.bmp")
            
            if os.path.exists(img_path):
                img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
                if img is not None:
                    images.append(img.astype(np.float32))
                else:
                    return None
            else:
                return None
        
        return images if len(images) == 5 else None
    
    def calculate_wrapped_phase(self, images: List[np.ndarray]) -> Tuple[np.ndarray, np.ndarray]:
        """计算包裹相位和调制度"""
        if len(images) != 5:
            raise ValueError("需要5张相移图像")
        
        I1, I2, I3, I4, I5 = images
        
        # 五步相移算法
        numerator = 2 * (I2 - I4)
        denominator = 2 * I3 - I1 - I5
        denominator = np.where(np.abs(denominator) < 1e-10, 1e-10, denominator)
        
        wrapped_phase = np.arctan2(numerator, denominator)
        
        # 计算调制度
        ac_component = np.sqrt(numerator**2 + denominator**2) / 2
        dc_component = (I1 + I2 + I3 + I4 + I5) / 5
        modulation = np.where(dc_component > 0, ac_component / dc_component, 0)
        
        return wrapped_phase, modulation
    
    def collect_phase_difference_data(self, projector_id: int, frequency: int = 1080, 
                                    reference_position: int = 0) -> Tuple[List[np.ndarray], List[float]]:
        """
        收集相位差数据
        Args:
            projector_id: 投影仪ID
            frequency: 频率（推荐使用1080，最稳定）
            reference_position: 参考位置
        Returns:
            phase_diff_maps: 相位差图列表
            heights: 对应的高度列表
        """
        print(f"正在收集投影仪 {projector_id} 频率 {frequency} 的相位差数据...")
        print(f"参考位置: Test-{reference_position}")
        
        # 加载参考位置的相位
        ref_folder = os.path.join(self.calibration_data_folder, f"Test-{reference_position}")
        ref_images = self.load_phase_shift_images(ref_folder, projector_id, frequency)
        
        if ref_images is None:
            print(f"无法加载参考位置 {reference_position} 的图像")
            return [], []
        
        ref_phase, ref_mod = self.calculate_wrapped_phase(ref_images)
        
        phase_diff_maps = []
        heights = []
        
        for position in range(self.num_positions):
            print(f"  处理位置 {position} (高度 {position * self.height_step:.1f} mm)...")
            
            if position == reference_position:
                # 参考位置的相位差为0
                phase_diff = np.zeros_like(ref_phase)
            else:
                test_folder = os.path.join(self.calibration_data_folder, f"Test-{position}")
                
                if not os.path.exists(test_folder):
                    print(f"    警告: 文件夹不存在 {test_folder}")
                    continue
                
                images = self.load_phase_shift_images(test_folder, projector_id, frequency)
                
                if images is None:
                    print(f"    警告: 无法加载位置 {position} 的图像")
                    continue
                
                current_phase, current_mod = self.calculate_wrapped_phase(images)
                
                # 计算相位差
                phase_diff = current_phase - ref_phase
                
                # 处理相位跳跃（-π到π的跳跃）
                phase_diff = np.angle(np.exp(1j * phase_diff))
                
                # 应用质量掩码
                quality_mask = (current_mod > 0.1) & (ref_mod > 0.1)
                phase_diff[~quality_mask] = np.nan
            
            phase_diff_maps.append(phase_diff)
            heights.append(position * self.height_step)
            
            # 输出统计信息
            valid_diff = phase_diff[~np.isnan(phase_diff)]
            if len(valid_diff) > 0:
                print(f"    相位差均值: {np.mean(valid_diff):.6f} rad")
            else:
                print(f"    无有效相位差")
        
        print(f"成功收集到 {len(phase_diff_maps)} 个位置的相位差数据")
        return phase_diff_maps, heights
    
    def extract_phase_difference_height_pairs(self, phase_diff_maps: List[np.ndarray], 
                                            heights: List[float],
                                            sample_ratio: float = 0.001) -> Tuple[np.ndarray, np.ndarray]:
        """提取相位差-高度数据对"""
        all_phase_diffs = []
        all_heights = []
        
        for phase_diff, height in zip(phase_diff_maps, heights):
            valid_mask = ~np.isnan(phase_diff)
            valid_diffs = phase_diff[valid_mask]
            
            if len(valid_diffs) > 0:
                # 随机采样
                n_samples = max(1000, int(len(valid_diffs) * sample_ratio))
                if len(valid_diffs) > n_samples:
                    indices = np.random.choice(len(valid_diffs), n_samples, replace=False)
                    sampled_diffs = valid_diffs[indices]
                else:
                    sampled_diffs = valid_diffs
                
                all_phase_diffs.extend(sampled_diffs)
                all_heights.extend([height] * len(sampled_diffs))
        
        return np.array(all_phase_diffs), np.array(all_heights)
    
    def fit_phase_difference_model(self, phase_diffs: np.ndarray, heights: np.ndarray) -> Dict:
        """拟合相位差-高度模型"""
        print(f"拟合相位差-高度模型，数据点数: {len(phase_diffs)}")
        
        # 数据预处理
        phase_std = np.std(phase_diffs)
        phase_mean = np.mean(phase_diffs)
        valid_mask = np.abs(phase_diffs - phase_mean) < 3 * phase_std
        
        clean_phase_diffs = phase_diffs[valid_mask]
        clean_heights = heights[valid_mask]
        
        print(f"数据清理后: {len(clean_phase_diffs)} 个数据点")
        
        if len(clean_phase_diffs) < 100:
            print("警告: 有效数据点太少")
            return None
        
        # 尝试线性模型和多项式模型
        models = [
            ('linear', LinearRegression()),
            ('polynomial_2', Pipeline([
                ('poly', PolynomialFeatures(degree=2)),
                ('linear', LinearRegression())
            ])),
            ('robust_linear', RANSACRegressor(
                LinearRegression(),
                min_samples=100,
                residual_threshold=0.5,
                random_state=42
            ))
        ]
        
        best_model = None
        best_score = -np.inf
        best_name = ""
        
        X = clean_phase_diffs.reshape(-1, 1)
        
        for model_name, model in models:
            try:
                if model_name == 'polynomial_2':
                    model.fit(X, clean_heights)
                else:
                    model.fit(X, clean_heights)
                
                y_pred = model.predict(X)
                r2 = r2_score(clean_heights, y_pred)
                rmse = np.sqrt(mean_squared_error(clean_heights, y_pred))
                
                print(f"{model_name} 模型: R² = {r2:.6f}, RMSE = {rmse:.4f} mm")
                
                if r2 > best_score:
                    best_model = model
                    best_score = r2
                    best_name = model_name
                    
            except Exception as e:
                print(f"{model_name} 模型拟合失败: {e}")
        
        if best_model is None:
            print("所有模型拟合都失败")
            return None
        
        # 使用最佳模型计算最终结果
        y_pred = best_model.predict(X)
        r2 = r2_score(clean_heights, y_pred)
        rmse = np.sqrt(mean_squared_error(clean_heights, y_pred))
        correlation = np.corrcoef(clean_phase_diffs, clean_heights)[0, 1]
        
        # 计算残差统计
        residuals = clean_heights - y_pred
        residual_std = np.std(residuals)
        
        model_info = {
            'model': best_model,
            'model_type': best_name,
            'r2_score': r2,
            'rmse': rmse,
            'residual_std': residual_std,
            'correlation': correlation,
            'n_samples': len(clean_phase_diffs),
            'phase_diff_range': (np.min(clean_phase_diffs), np.max(clean_phase_diffs)),
            'height_range': (np.min(clean_heights), np.max(clean_heights))
        }
        
        print(f"最佳模型: {best_name}")
        print(f"  相关系数: {correlation:.6f}")
        print(f"  R² 分数: {r2:.6f}")
        print(f"  RMSE: {rmse:.4f} mm")
        print(f"  残差标准差: {residual_std:.4f} mm")
        
        return model_info
    
    def calibrate_projector_with_phase_difference(self, projector_id: int, 
                                                frequency: int = 1080,
                                                reference_position: int = 0) -> Dict:
        """使用相位差方法标定投影仪"""
        print(f"\n=== 使用相位差方法标定投影仪 {projector_id} ===")
        print(f"频率: {frequency}像素周期")
        
        # 收集相位差数据
        phase_diff_maps, heights = self.collect_phase_difference_data(
            projector_id, frequency, reference_position)
        
        if len(phase_diff_maps) == 0:
            print(f"投影仪 {projector_id} 没有有效数据，跳过标定")
            return None
        
        # 提取相位差-高度对
        phase_diffs, height_values = self.extract_phase_difference_height_pairs(
            phase_diff_maps, heights)
        
        if len(phase_diffs) == 0:
            print(f"投影仪 {projector_id} 没有有效的相位差-高度对，跳过标定")
            return None
        
        print(f"提取到 {len(phase_diffs)} 个有效的相位差-高度对")
        
        # 拟合模型
        model_info = self.fit_phase_difference_model(phase_diffs, height_values)
        
        if model_info is None:
            print(f"投影仪 {projector_id} 模型拟合失败")
            return None
        
        # 保存标定结果
        calibration_result = {
            'projector_id': projector_id,
            'projector_name': self.projector_configs[projector_id]["name"],
            'frequency': frequency,
            'frequency_name': self.frequency_configs[frequency]["name"],
            'reference_position': reference_position,
            'model_info': model_info,
            'phase_diff_maps': phase_diff_maps,
            'heights': heights,
            'calibration_phase_diffs': phase_diffs,
            'calibration_heights': height_values,
            'method': 'phase_difference'
        }
        
        print(f"\n投影仪 {projector_id} 相位差标定完成!")
        return calibration_result
    
    def calibrate_all_projectors_phase_difference(self, frequency: int = 1080) -> Dict:
        """使用相位差方法标定所有投影仪"""
        print("=== 使用相位差方法标定所有投影仪 ===")
        
        all_results = {}
        
        for projector_id in range(4):
            result = self.calibrate_projector_with_phase_difference(projector_id, frequency)
            if result is not None:
                all_results[projector_id] = result
                self.calibration_results[projector_id] = result
        
        print(f"\n相位差标定完成，成功标定 {len(all_results)} 个投影仪")
        return all_results
    
    def predict_height_from_phase_difference(self, current_phase_map: np.ndarray, 
                                           reference_phase_map: np.ndarray,
                                           projector_id: int) -> np.ndarray:
        """
        使用相位差预测高度
        Args:
            current_phase_map: 当前相位图
            reference_phase_map: 参考相位图
            projector_id: 投影仪ID
        Returns:
            height_map: 高度图
        """
        if projector_id not in self.calibration_results:
            raise ValueError(f"投影仪 {projector_id} 未标定")
        
        # 计算相位差
        phase_diff = current_phase_map - reference_phase_map
        phase_diff = np.angle(np.exp(1j * phase_diff))
        
        # 使用标定模型预测高度
        model_info = self.calibration_results[projector_id]['model_info']
        model = model_info['model']
        
        height_map = np.full_like(phase_diff, np.nan)
        valid_mask = ~np.isnan(phase_diff)
        
        if np.any(valid_mask):
            valid_phase_diffs = phase_diff[valid_mask]
            X = valid_phase_diffs.reshape(-1, 1)
            predicted_heights = model.predict(X)
            
            flat_heights = np.full_like(phase_diff.flatten(), np.nan)
            flat_heights[valid_mask.flatten()] = predicted_heights
            height_map = flat_heights.reshape(phase_diff.shape)
        
        return height_map
    
    def save_calibration_results(self, output_folder: str = "phase_difference_calibration_results"):
        """保存标定结果"""
        os.makedirs(output_folder, exist_ok=True)
        
        # 保存完整结果
        with open(os.path.join(output_folder, "calibration_results.pkl"), 'wb') as f:
            pickle.dump(self.calibration_results, f)
        
        # 保存简化模型
        simplified_results = {}
        for proj_id, result in self.calibration_results.items():
            model_info = result['model_info']
            simplified_results[proj_id] = {
                'model': model_info['model'],
                'model_type': model_info['model_type'],
                'phase_diff_range': model_info['phase_diff_range'],
                'height_range': model_info['height_range'],
                'projector_name': result['projector_name'],
                'frequency': result['frequency'],
                'reference_position': result['reference_position'],
                'method': 'phase_difference'
            }
        
        with open(os.path.join(output_folder, "calibration_models.pkl"), 'wb') as f:
            pickle.dump(simplified_results, f)
        
        # 生成质量报告
        quality_report = self.evaluate_calibration_quality()
        with open(os.path.join(output_folder, "quality_report.json"), 'w', encoding='utf-8') as f:
            json.dump(quality_report, f, indent=2, ensure_ascii=False)
        
        print(f"相位差标定结果已保存到 {output_folder}")
        return quality_report
    
    def evaluate_calibration_quality(self) -> Dict:
        """评估标定质量"""
        if not self.calibration_results:
            return {"error": "没有标定结果"}
        
        quality_report = {
            "method": "phase_difference",
            "overall_quality": "Unknown",
            "projector_reports": {},
            "recommendations": []
        }
        
        all_r2_scores = []
        all_rmse_values = []
        all_correlations = []
        
        for proj_id, result in self.calibration_results.items():
            model_info = result['model_info']
            r2 = model_info['r2_score']
            rmse = model_info['rmse']
            correlation = model_info['correlation']
            
            all_r2_scores.append(r2)
            all_rmse_values.append(rmse)
            all_correlations.append(abs(correlation))
            
            # 质量评估
            if r2 > 0.95 and rmse < 0.5 and abs(correlation) > 0.95:
                proj_quality = "Excellent"
            elif r2 > 0.90 and rmse < 1.0 and abs(correlation) > 0.90:
                proj_quality = "Good"
            elif r2 > 0.80 and rmse < 2.0 and abs(correlation) > 0.80:
                proj_quality = "Fair"
            else:
                proj_quality = "Poor"
            
            quality_report["projector_reports"][proj_id] = {
                "quality": proj_quality,
                "r2_score": r2,
                "rmse": rmse,
                "correlation": correlation,
                "model_type": model_info['model_type'],
                "sample_count": model_info['n_samples']
            }
        
        # 整体质量
        avg_r2 = np.mean(all_r2_scores)
        avg_rmse = np.mean(all_rmse_values)
        avg_corr = np.mean(all_correlations)
        
        if avg_r2 > 0.95 and avg_rmse < 0.5:
            overall_quality = "Excellent"
        elif avg_r2 > 0.90 and avg_rmse < 1.0:
            overall_quality = "Good"
        elif avg_r2 > 0.80 and avg_rmse < 2.0:
            overall_quality = "Fair"
        else:
            overall_quality = "Poor"
        
        quality_report["overall_quality"] = overall_quality
        quality_report["average_r2"] = avg_r2
        quality_report["average_rmse"] = avg_rmse
        quality_report["average_correlation"] = avg_corr
        quality_report["num_calibrated_projectors"] = len(self.calibration_results)
        
        # 生成建议
        recommendations = []
        if avg_r2 > 0.95:
            recommendations.append("标定质量优秀，可以用于高精度测量")
        elif avg_r2 > 0.90:
            recommendations.append("标定质量良好，适合一般精度测量")
        else:
            recommendations.append("标定质量需要改进，建议检查数据质量")
        
        if avg_rmse < 0.5:
            recommendations.append("测量精度优秀 (RMSE < 0.5mm)")
        elif avg_rmse < 1.0:
            recommendations.append("测量精度良好 (RMSE < 1.0mm)")
        
        quality_report["recommendations"] = recommendations
        
        return quality_report


def test_phase_difference_calibration():
    """测试相位差标定方法"""
    print("=== 测试相位差标定方法 ===")
    
    calibrator = PhaseDifferenceCalibration()
    
    # 测试单个投影仪
    result = calibrator.calibrate_projector_with_phase_difference(0, frequency=1080)
    
    if result:
        print("相位差标定测试成功!")
        
        # 保存结果
        calibrator.calibration_results[0] = result
        quality_report = calibrator.save_calibration_results("phase_diff_test_results")
        
        # 打印质量报告
        print("\n质量报告:")
        print(f"整体质量: {quality_report['overall_quality']}")
        print(f"平均R²: {quality_report['average_r2']:.6f}")
        print(f"平均RMSE: {quality_report['average_rmse']:.4f} mm")
        print(f"平均相关系数: {quality_report['average_correlation']:.6f}")
        
    else:
        print("相位差标定测试失败")


if __name__ == "__main__":
    test_phase_difference_calibration()
