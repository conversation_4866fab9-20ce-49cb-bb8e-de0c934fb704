#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版最终标定结果融合程序 - 严格的单光机异常值检测
基于fusion_with_final_calibration.py v2.0，集成多层次异常值检测和物理合理性检查
"""

import os
import numpy as np
import cv2
import tifffile
import pickle
from scipy import stats, ndimage
from tqdm import tqdm
import argparse
from typing import Dict, List, Tuple, Optional
import json

# 导入四频解包算法
from four_freq_phase_unwrap import five_step_phase, unwrap_multifrequency

class EnhancedFinalCalibrationFusion:
    """增强版最终标定结果融合器"""
    
    def __init__(self, enhanced_mode: bool = True):
        # 投影仪配置
        self.projector_configs = {
            0: {"name": "DLP-0", "freq_start": 1, "freq_end": 20},
            1: {"name": "DLP-1", "freq_start": 21, "freq_end": 40},
            2: {"name": "DLP-2", "freq_start": 41, "freq_end": 60},
            3: {"name": "DLP-3", "freq_start": 61, "freq_end": 80}
        }
        
        # 四频解包参数
        self.periods = [1080, 512, 64, 8]
        self.steps = 5
        
        # CCD参数 - 与generate_point_cloud.py保持一致
        self.CCD_DX = 0.0154  # mm/pixel (相机像素的物理尺寸)
        
        # 映射表存储
        self.mapping_tables = {}
        
        # 增强模式控制
        self.enhanced_mode = enhanced_mode
        
        if enhanced_mode:
            # 增强版质量评估参数
            self.min_valid_ratio = 0.25         # 提高到25%有效像素
            self.min_valid_pixels = 3000        # 提高最小有效像素数
            self.z_score_threshold = 2.5        # 降低Z-score阈值
            self.max_std_threshold = 4.0        # 降低标准差阈值到4mm
            self.gradient_threshold = 3.0       # 深度梯度异常阈值
            self.local_window_size = 15         # 局部异常检测窗口
            self.depth_range_limit = 60.0       # 合理深度范围限制
            self.enhanced_score_threshold = 0.45  # 增强评分阈值
        else:
            # 标准模式参数（向后兼容）
            self.min_valid_ratio = 0.1
            self.min_valid_pixels = 100
            self.z_score_threshold = 3.0
            self.max_std_threshold = 10.0
            self.enhanced_score_threshold = 0.3
        
        print(f"初始化融合器: {'增强模式' if enhanced_mode else '标准模式'}")
        if enhanced_mode:
            print(f"  严格参数: 有效像素≥{self.min_valid_ratio:.0%}, 标准差≤{self.max_std_threshold}mm")
    
    def load_mapping_tables(self, calibration_folder: str) -> bool:
        """加载TIFF映射表"""
        print("加载TIFF映射表...")
        mapping_folder = os.path.join(calibration_folder, "mapping_tables")
        
        if not os.path.exists(mapping_folder):
            print(f"错误: 映射表文件夹不存在: {mapping_folder}")
            return False
        
        try:
            for projector_id in range(4):
                self.mapping_tables[projector_id] = {}
                
                # 加载多项式系数 (常数项, 线性项, 二次项)
                for coeff_idx in range(3):
                    tiff_file = f"DLP-{projector_id}-{coeff_idx}.tiff"
                    tiff_path = os.path.join(mapping_folder, tiff_file)
                    
                    if os.path.exists(tiff_path):
                        coeff_map = tifffile.imread(tiff_path)
                        self.mapping_tables[projector_id][coeff_idx] = coeff_map
                        print(f"  已加载 {tiff_file}, 形状: {coeff_map.shape}")
                    else:
                        print(f"  警告: {tiff_file} 不存在")
            
            print(f"映射表加载完成，支持 {len(self.mapping_tables)} 个投影仪")
            return True
            
        except Exception as e:
            print(f"映射表加载失败: {e}")
            return False
    
    def load_phase_shift_images(self, test_folder: str, projector_id: int) -> Optional[Dict]:
        """加载相移图像"""
        freq_ranges = self.projector_configs[projector_id]
        images_by_frequency = {}
        
        try:
            for freq_idx, period in enumerate(self.periods):
                images_by_frequency[freq_idx] = []
                
                for step in range(self.steps):
                    img_num = freq_ranges["freq_start"] + freq_idx * self.steps + step
                    img_path = os.path.join(test_folder, f"{img_num}.bmp")
                    
                    if not os.path.exists(img_path):
                        print(f"    错误: 图像文件不存在: {img_path}")
                        return None
                    
                    img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
                    if img is None:
                        print(f"    错误: 无法读取图像: {img_path}")
                        return None
                    
                    images_by_frequency[freq_idx].append(img.astype(np.float64))
            
            return images_by_frequency
            
        except Exception as e:
            print(f"    图像加载失败: {e}")
            return None
    
    def phase_unwrapping_four_frequency(self, images_by_frequency: Dict) -> Tuple[Optional[np.ndarray], Dict]:
        """四频相位解包"""
        try:
            # 对每个频率进行五步相移
            wrapped_phases = []
            quality_maps = []
            
            for freq_idx in range(len(self.periods)):
                if freq_idx not in images_by_frequency:
                    print(f"    错误: 频率 {freq_idx} 的图像缺失")
                    return None, {"error": f"频率 {freq_idx} 图像缺失"}
                
                images = images_by_frequency[freq_idx]
                if len(images) != self.steps:
                    print(f"    错误: 频率 {freq_idx} 图像数量不正确: {len(images)} != {self.steps}")
                    return None, {"error": f"频率 {freq_idx} 图像数量错误"}
                
                # 五步相移计算
                # 将图像列表转换为numpy数组格式
                images_array = np.stack(images, axis=-1)
                wrapped_phase = five_step_phase(images_array)
                
                # 计算调制度作为质量指标
                I0, I1, I2, I3, I4 = images
                A = 2.0 * (I1 - I3)
                B = I0 - 2.0 * I2 + I4
                modulation = np.sqrt(A**2 + B**2) / (I0 + I1 + I2 + I3 + I4 + 1e-10)
                
                wrapped_phases.append(wrapped_phase)
                quality_maps.append(modulation)
            
            # 多频相位解包
            unwrapped_phases = unwrap_multifrequency(wrapped_phases, self.periods)
            # 取最高频的展开相位
            unwrapped_phase = unwrapped_phases[-1]
            
            # 生成质量掩码
            combined_quality = np.ones_like(unwrapped_phase)
            for quality_map in quality_maps:
                combined_quality = np.minimum(combined_quality, quality_map)
            
            # 应用质量阈值
            quality_threshold = 0.3
            low_quality_mask = combined_quality < quality_threshold
            unwrapped_phase[low_quality_mask] = np.nan
            
            phase_info = {
                "periods": self.periods,
                "quality_threshold": quality_threshold,
                "mean_quality": np.nanmean(combined_quality),
                "valid_ratio": np.sum(np.isfinite(unwrapped_phase)) / unwrapped_phase.size
            }
            
            return unwrapped_phase, phase_info
            
        except Exception as e:
            print(f"    相位解包失败: {e}")
            return None, {"error": str(e)}
    
    def phase_to_height_polynomial(self, phase: np.ndarray, projector_id: int) -> np.ndarray:
        """使用多项式映射将相位转换为高度"""
        if projector_id not in self.mapping_tables:
            print(f"错误: 投影仪 {projector_id} 的映射表未加载")
            return np.full_like(phase, np.nan)
        
        mapping_table = self.mapping_tables[projector_id]
        if not mapping_table:
            print(f"错误: 投影仪 {projector_id} 的映射表为空")
            return np.full_like(phase, np.nan)
        
        # 获取参考形状
        ref_shape = list(mapping_table.values())[0].shape
        
        # 调整相位图尺寸
        if phase.shape != ref_shape:
            phase_resized = cv2.resize(phase, (ref_shape[1], ref_shape[0]), 
                                     interpolation=cv2.INTER_LINEAR)
        else:
            phase_resized = phase.copy()
        
        # 计算多项式：h = c0 + c1*φ + c2*φ²
        height_map = np.zeros_like(phase_resized)
        
        # 常数项
        if 0 in mapping_table:
            height_map += mapping_table[0]
        
        # 一次项
        if 1 in mapping_table:
            height_map += mapping_table[1] * phase_resized
        
        # 二次项
        if 2 in mapping_table:
            height_map += mapping_table[2] * (phase_resized ** 2)
        
        # 处理无效值
        valid_phase = np.isfinite(phase_resized)
        height_map[~valid_phase] = np.nan
        
        return height_map
    
    def enhanced_depth_quality_analysis(self, depth_map: np.ndarray, projector_id: int) -> Dict:
        """增强版深度图质量分析"""
        if not self.enhanced_mode:
            return self.analyze_depth_quality_standard(depth_map, projector_id)
        
        total_pixels = depth_map.size
        valid_mask = np.isfinite(depth_map)
        valid_count = np.sum(valid_mask)
        valid_ratio = valid_count / total_pixels
        
        quality_info = {
            "projector_id": projector_id,
            "total_pixels": total_pixels,
            "valid_pixels": valid_count,
            "valid_ratio": valid_ratio,
            "enhanced_mode": True
        }
        
        # 严格的基础检查
        is_valid_basic = (valid_ratio >= self.min_valid_ratio and 
                         valid_count >= self.min_valid_pixels)
        
        if not is_valid_basic:
            quality_info.update({
                "is_valid": False,
                "rejection_reason": f"基础检查失败: 有效像素{valid_ratio:.1%} < {self.min_valid_ratio:.1%} 或 {valid_count} < {self.min_valid_pixels}",
                "quality_score": 0.0,
                "enhanced_score": 0.0
            })
            return quality_info
        
        valid_depths = depth_map[valid_mask]
        
        # 1. 增强统计分析
        depth_stats = self._compute_enhanced_statistics(valid_depths)
        
        # 2. 多层次异常值检测
        outlier_analysis = self._multi_level_outlier_detection(depth_map, valid_mask, valid_depths)
        
        # 3. 空间连续性检查
        spatial_analysis = self._spatial_continuity_check(depth_map)
        
        # 4. 物理合理性验证
        physical_analysis = self._physical_reasonableness_validation(valid_depths, depth_stats)
        
        # 5. 综合质量评分
        scores = self._comprehensive_scoring(depth_stats, outlier_analysis, 
                                           spatial_analysis, physical_analysis, valid_ratio)
        
        # 6. 最终有效性判断
        is_valid_final = self._enhanced_validity_decision(scores, outlier_analysis, physical_analysis)
        
        quality_info.update({
            "is_valid": is_valid_final,
            "depth_stats": depth_stats,
            "outlier_analysis": outlier_analysis,
            "spatial_analysis": spatial_analysis,
            "physical_analysis": physical_analysis,
            "quality_score": scores["standard_score"],    # 兼容性
            "enhanced_score": scores["enhanced_score"],   # 新评分
            "quality_breakdown": scores["breakdown"],
            "rejection_reason": scores.get("rejection_reason") if not is_valid_final else None
        })
        
        return quality_info
    
    def analyze_depth_quality_standard(self, depth_map: np.ndarray, projector_id: int) -> Dict:
        """标准版深度图质量分析（向后兼容）"""
        total_pixels = depth_map.size
        valid_mask = np.isfinite(depth_map)
        valid_count = np.sum(valid_mask)
        valid_ratio = valid_count / total_pixels
        
        quality_info = {
            "projector_id": projector_id,
            "total_pixels": total_pixels,
            "valid_pixels": valid_count,
            "valid_ratio": valid_ratio,
            "is_valid": valid_ratio >= self.min_valid_ratio,
            "enhanced_mode": False
        }
        
        if valid_count > self.min_valid_pixels:
            valid_depths = depth_map[valid_mask]
            
            depth_stats = {
                "min": np.min(valid_depths),
                "max": np.max(valid_depths),
                "mean": np.mean(valid_depths),
                "std": np.std(valid_depths),
                "median": np.median(valid_depths),
                "range": np.max(valid_depths) - np.min(valid_depths)
            }
            
            # 标准异常值检测
            if len(valid_depths) > 10:
                z_scores = np.abs(stats.zscore(valid_depths))
                outlier_mask = z_scores > self.z_score_threshold
                outlier_ratio = np.sum(outlier_mask) / len(valid_depths)
            else:
                outlier_ratio = 0
            
            # 标准质量评分
            quality_score = self.calculate_standard_quality_score(depth_stats, outlier_ratio, valid_ratio)
            
            quality_info.update({
                "depth_stats": depth_stats,
                "outlier_ratio": outlier_ratio,
                "quality_score": quality_score
            })
        else:
            quality_info.update({
                "depth_stats": None,
                "outlier_ratio": 1.0,
                "quality_score": 0.0
            })
        
        return quality_info
    
    def _compute_enhanced_statistics(self, valid_depths: np.ndarray) -> Dict:
        """计算增强统计信息"""
        return {
            "min": np.min(valid_depths),
            "max": np.max(valid_depths),
            "mean": np.mean(valid_depths),
            "std": np.std(valid_depths),
            "median": np.median(valid_depths),
            "q25": np.percentile(valid_depths, 25),
            "q75": np.percentile(valid_depths, 75),
            "iqr": np.percentile(valid_depths, 75) - np.percentile(valid_depths, 25),
            "range": np.max(valid_depths) - np.min(valid_depths),
            "mad": np.median(np.abs(valid_depths - np.median(valid_depths))),
            "skewness": stats.skew(valid_depths),
            "kurtosis": stats.kurtosis(valid_depths)
        }
    
    def _multi_level_outlier_detection(self, depth_map: np.ndarray, valid_mask: np.ndarray, 
                                     valid_depths: np.ndarray) -> Dict:
        """多层次异常值检测"""
        
        # 1. Z-score检测（更严格）
        z_scores = np.abs(stats.zscore(valid_depths))
        z_outliers = z_scores > self.z_score_threshold
        z_outlier_ratio = np.sum(z_outliers) / len(valid_depths)
        
        # 2. IQR检测
        q25, q75 = np.percentile(valid_depths, [25, 75])
        iqr = q75 - q25
        iqr_lower = q25 - 1.5 * iqr
        iqr_upper = q75 + 1.5 * iqr
        iqr_outliers = (valid_depths < iqr_lower) | (valid_depths > iqr_upper)
        iqr_outlier_ratio = np.sum(iqr_outliers) / len(valid_depths)
        
        # 3. MAD检测（中位数绝对偏差）
        median = np.median(valid_depths)
        mad = np.median(np.abs(valid_depths - median))
        mad_threshold = 3 * mad  # 3-MAD规则
        mad_outliers = np.abs(valid_depths - median) > mad_threshold
        mad_outlier_ratio = np.sum(mad_outliers) / len(valid_depths)
        
        # 4. 局部异常检测
        local_outlier_ratio = self._detect_local_outliers(depth_map, valid_mask)
        
        # 综合异常值评估
        combined_outliers = z_outliers | iqr_outliers | mad_outliers
        combined_ratio = np.sum(combined_outliers) / len(valid_depths)
        
        return {
            "z_outlier_ratio": z_outlier_ratio,
            "iqr_outlier_ratio": iqr_outlier_ratio,
            "mad_outlier_ratio": mad_outlier_ratio,
            "local_outlier_ratio": local_outlier_ratio,
            "combined_outlier_ratio": combined_ratio,
            "severe_outliers": combined_ratio > 0.2,   # 20%以上认为严重
            "moderate_outliers": combined_ratio > 0.1,  # 10%以上认为中等
            "outlier_mask": combined_outliers
        }
    
    def _detect_local_outliers(self, depth_map: np.ndarray, valid_mask: np.ndarray) -> float:
        """检测局部异常值"""
        height, width = depth_map.shape
        local_outlier_count = 0
        checked_pixels = 0
        
        half_window = self.local_window_size // 2
        step_size = max(self.local_window_size // 2, 5)  # 采样步长
        
        for r in range(half_window, height - half_window, step_size):
            for c in range(half_window, width - half_window, step_size):
                if valid_mask[r, c]:
                    # 提取局部窗口
                    window = depth_map[r-half_window:r+half_window+1, 
                                     c-half_window:c+half_window+1]
                    window_valid = valid_mask[r-half_window:r+half_window+1,
                                            c-half_window:c+half_window+1]
                    
                    if np.sum(window_valid) > 8:  # 窗口内有足够有效像素
                        window_depths = window[window_valid]
                        window_median = np.median(window_depths)
                        window_std = np.std(window_depths)
                        center_depth = depth_map[r, c]
                        
                        # 检查中心像素是否为局部异常
                        if window_std > 0 and abs(center_depth - window_median) > 2.5 * window_std:
                            local_outlier_count += 1
                    
                    checked_pixels += 1
        
        return local_outlier_count / max(checked_pixels, 1)
    
    def _spatial_continuity_check(self, depth_map: np.ndarray) -> Dict:
        """空间连续性检查"""
        
        # 计算梯度
        grad_y, grad_x = np.gradient(depth_map)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        
        valid_mask = np.isfinite(depth_map)
        if np.sum(valid_mask) == 0:
            return {"valid_data": False}
        
        valid_gradients = gradient_magnitude[valid_mask]
        
        # 梯度统计
        mean_gradient = np.mean(valid_gradients)
        max_gradient = np.max(valid_gradients)
        high_gradient_ratio = np.sum(valid_gradients > self.gradient_threshold) / len(valid_gradients)
        
        # 连续性评分
        continuity_score = max(0, 1 - high_gradient_ratio * 4)
        
        return {
            "valid_data": True,
            "mean_gradient": mean_gradient,
            "max_gradient": max_gradient,
            "high_gradient_ratio": high_gradient_ratio,
            "continuity_score": continuity_score,
            "smooth_surface": high_gradient_ratio < 0.03
        }
    
    def _physical_reasonableness_validation(self, valid_depths: np.ndarray, depth_stats: Dict) -> Dict:
        """物理合理性验证"""
        
        # 深度范围合理性
        range_ok = depth_stats["range"] <= self.depth_range_limit
        
        # 标准差合理性
        std_ok = depth_stats["std"] <= self.max_std_threshold
        
        # 分布形状合理性
        skew_ok = abs(depth_stats["skewness"]) < 2.0
        kurt_ok = abs(depth_stats["kurtosis"]) < 7.0
        
        # 均值中位数一致性
        mean_median_diff = abs(depth_stats["mean"] - depth_stats["median"])
        consistency_ok = mean_median_diff < depth_stats["std"]
        
        # 综合物理合理性评分
        physical_checks = [range_ok, std_ok, skew_ok, kurt_ok, consistency_ok]
        physical_score = sum(physical_checks) / len(physical_checks)
        
        return {
            "range_reasonable": range_ok,
            "std_reasonable": std_ok,
            "distribution_reasonable": skew_ok and kurt_ok,
            "consistency_reasonable": consistency_ok,
            "physical_score": physical_score,
            "all_checks_passed": all(physical_checks)
        }
    
    def _comprehensive_scoring(self, depth_stats: Dict, outlier_analysis: Dict,
                              spatial_analysis: Dict, physical_analysis: Dict,
                              valid_ratio: float) -> Dict:
        """综合质量评分"""
        
        # 标准评分（兼容性）
        standard_score = self.calculate_standard_quality_score(
            depth_stats, outlier_analysis["combined_outlier_ratio"], valid_ratio)
        
        # 增强评分（更全面）
        weights = {
            "validity": 0.25,      # 有效性权重
            "stability": 0.25,     # 稳定性权重
            "outliers": 0.2,       # 异常值权重
            "spatial": 0.15,       # 空间性权重
            "physical": 0.15       # 物理性权重
        }
        
        # 分项评分
        validity_score = min(valid_ratio / 0.8, 1.0)  # 80%为满分
        stability_score = max(0, 1 - depth_stats["std"] / self.max_std_threshold)
        outlier_score = max(0, 1 - outlier_analysis["combined_outlier_ratio"] * 3)
        spatial_score = spatial_analysis.get("continuity_score", 0)
        physical_score = physical_analysis["physical_score"]
        
        # 加权计算增强评分
        enhanced_score = (
            validity_score * weights["validity"] +
            stability_score * weights["stability"] +
            outlier_score * weights["outliers"] +
            spatial_score * weights["spatial"] +
            physical_score * weights["physical"]
        )
        
        # 质量等级
        if enhanced_score >= 0.8:
            grade = "优秀"
        elif enhanced_score >= 0.65:
            grade = "良好"
        elif enhanced_score >= 0.5:
            grade = "一般"
        else:
            grade = "较差"
        
        return {
            "standard_score": standard_score,
            "enhanced_score": enhanced_score,
            "grade": grade,
            "breakdown": {
                "有效性": f"{validity_score:.3f} ({weights['validity']:.0%})",
                "稳定性": f"{stability_score:.3f} ({weights['stability']:.0%})",
                "异常值": f"{outlier_score:.3f} ({weights['outliers']:.0%})",
                "空间性": f"{spatial_score:.3f} ({weights['spatial']:.0%})",
                "物理性": f"{physical_score:.3f} ({weights['physical']:.0%})"
            }
        }
    
    def _enhanced_validity_decision(self, scores: Dict, outlier_analysis: Dict, 
                                   physical_analysis: Dict) -> bool:
        """增强版有效性决策"""
        
        # 增强评分阈值检查
        score_pass = scores["enhanced_score"] >= self.enhanced_score_threshold
        
        # 异常值检查
        outlier_pass = not outlier_analysis["severe_outliers"]
        
        # 物理合理性检查
        physical_pass = physical_analysis["physical_score"] >= 0.6
        
        # 综合决策
        final_decision = score_pass and outlier_pass and physical_pass
        
        if not final_decision:
            if not score_pass:
                scores["rejection_reason"] = f"评分过低: {scores['enhanced_score']:.3f} < {self.enhanced_score_threshold}"
            elif not outlier_pass:
                scores["rejection_reason"] = f"异常值过多: {outlier_analysis['combined_outlier_ratio']:.1%} > 20%"
            elif not physical_pass:
                scores["rejection_reason"] = f"物理不合理: 综合评分{physical_analysis['physical_score']:.3f} < 0.6"
        
        return final_decision
    
    def calculate_standard_quality_score(self, depth_stats: Dict, outlier_ratio: float, valid_ratio: float) -> float:
        """计算标准质量评分（兼容性）"""
        score = valid_ratio * 0.4
        
        if depth_stats["std"] > 0:
            stability_score = max(0, 1 - depth_stats["std"] / self.max_std_threshold)
            score += stability_score * 0.3
        
        outlier_score = max(0, 1 - outlier_ratio * 2)
        score += outlier_score * 0.3
        
        return min(score, 1.0)
    
    def reconstruct_single_projector(self, test_folder: str, projector_id: int) -> Tuple[np.ndarray, Dict]:
        """重建单个投影仪的深度图"""
        config = self.projector_configs[projector_id]
        print(f"\n重建投影仪 {projector_id} ({config['name']})...")
        
        try:
            # 1. 加载图像
            images_by_frequency = self.load_phase_shift_images(test_folder, projector_id)
            if images_by_frequency is None:
                return None, {"projector_id": projector_id, "error": "图像加载失败", "is_valid": False}
            
            # 2. 相位展开
            unwrapped_phase, phase_info = self.phase_unwrapping_four_frequency(images_by_frequency)
            if unwrapped_phase is None:
                return None, {"projector_id": projector_id, "error": "相位展开失败", "is_valid": False}
            
            # 3. 相位转高度
            height_map = self.phase_to_height_polynomial(unwrapped_phase, projector_id)
            
            # 4. 质量分析
            quality_info = self.enhanced_depth_quality_analysis(height_map, projector_id)
            quality_info["phase_info"] = phase_info
            
            # 显示结果
            if self.enhanced_mode and quality_info.get('is_valid', False):
                grade = quality_info.get('quality_breakdown', {}).get('grade', '未知')
                print(f"  完成重建: 有效比例 {quality_info['valid_ratio']:.1%}, "
                      f"增强评分 {quality_info['enhanced_score']:.3f} ({grade})")
            elif self.enhanced_mode:
                print(f"  重建被拒绝: {quality_info.get('rejection_reason', '未知原因')}")
            else:
                print(f"  完成重建: 有效比例 {quality_info['valid_ratio']:.1%}, "
                      f"质量分数 {quality_info['quality_score']:.3f}")
            
            return height_map, quality_info
            
        except Exception as e:
            print(f"  重建失败: {e}")
            return None, {"projector_id": projector_id, "error": str(e), "is_valid": False}
    
    def adaptive_fusion(self, depth_maps: List[np.ndarray], 
                       quality_infos: List[Dict],
                       use_20um_detection: bool = True) -> Tuple[np.ndarray, Dict]:
        """自适应融合算法"""
        print(f"\n执行自适应融合{'（增强模式）' if self.enhanced_mode else '（标准模式）'}...")
        if use_20um_detection:
            print("  使用20微米精细异常检测")
        
        # 筛选有效的投影仪数据
        valid_projectors = []
        valid_depth_maps = []
        
        for i, (depth_map, quality_info) in enumerate(zip(depth_maps, quality_infos)):
            if depth_map is not None and quality_info.get("is_valid", False):
                valid_projectors.append(i)
                valid_depth_maps.append(depth_map)
        
        fusion_info = {
            "total_projectors": len(depth_maps),
            "valid_projectors": valid_projectors,
            "valid_count": len(valid_projectors),
            "enhanced_mode": self.enhanced_mode,
            "use_20um_detection": use_20um_detection
        }
        
        print(f"  有效投影仪: {valid_projectors} ({len(valid_projectors)}/{len(depth_maps)})")
        
        if len(valid_projectors) == 0:
            print("  错误: 没有有效的投影仪数据")
            fusion_info["fusion_strategy"] = "failed"
            return np.full((2848, 2848), np.nan), fusion_info
        
        elif len(valid_projectors) == 1:
            print(f"  策略: 使用单投影仪数据 (投影仪 {valid_projectors[0]})")
            fusion_info["fusion_strategy"] = "single_projector"
            fused_depth = valid_depth_maps[0].copy()
        
        elif len(valid_projectors) == 2:
            print(f"  策略: 双投影仪融合")
            fusion_info["fusion_strategy"] = "dual_projector"
            if use_20um_detection:
                fused_depth = self.fuse_two_depths_20um_threshold(valid_depth_maps[0], valid_depth_maps[1])
            else:
                fused_depth = self.fuse_two_depths_basic(valid_depth_maps[0], valid_depth_maps[1])
        
        else:
            print(f"  策略: 多投影仪融合")
            fusion_info["fusion_strategy"] = "multi_projector"
            if use_20um_detection:
                fused_depth = self.fuse_multiple_depths_20um_threshold(valid_depth_maps)
            else:
                fused_depth = self.fuse_multiple_depths_basic(valid_depth_maps)
        
        # 统计融合结果
        valid_mask = np.isfinite(fused_depth)
        fusion_info.update({
            "final_valid_pixels": np.sum(valid_mask),
            "final_coverage": np.sum(valid_mask) / fused_depth.size
        })
        
        if np.sum(valid_mask) > 0:
            valid_depths = fused_depth[valid_mask]
            fusion_info["final_depth_stats"] = {
                "min": np.min(valid_depths),
                "max": np.max(valid_depths),
                "mean": np.mean(valid_depths),
                "std": np.std(valid_depths),
                "range": np.max(valid_depths) - np.min(valid_depths)
            }
        
        return fused_depth, fusion_info
    
    def fuse_two_depths_20um_threshold(self, depth1: np.ndarray, depth2: np.ndarray, 
                                      threshold: float = 0.02) -> np.ndarray:
        """双投影仪融合 - 20微米阈值"""
        print(f"    执行双投影仪20微米精细融合...")
        
        valid1 = np.isfinite(depth1)
        valid2 = np.isfinite(depth2)
        fused = np.full_like(depth1, np.nan)
        
        # 处理重叠区域
        both_valid = valid1 & valid2
        if np.sum(both_valid) > 0:
            depth_diff = np.abs(depth1[both_valid] - depth2[both_valid])
            consistent_mask = depth_diff <= threshold
            
            # 一致区域：直接平均
            consistent_indices = np.where(both_valid)[0][consistent_mask], np.where(both_valid)[1][consistent_mask]
            if len(consistent_indices[0]) > 0:
                fused[consistent_indices] = (depth1[consistent_indices] + depth2[consistent_indices]) / 2
            
            # 不一致区域：选择更接近局部中位数的值
            inconsistent_mask = ~consistent_mask
            if np.sum(inconsistent_mask) > 0:
                inconsistent_indices = np.where(both_valid)[0][inconsistent_mask], np.where(both_valid)[1][inconsistent_mask]
                
                # 使用已融合的一致区域计算局部参考
                if len(consistent_indices[0]) > 100:
                    local_ref = np.median(fused[consistent_indices])
                else:
                    local_ref = np.median(np.concatenate([depth1[both_valid], depth2[both_valid]]))
                
                depth1_vals = depth1[inconsistent_indices]
                depth2_vals = depth2[inconsistent_indices]
                diff1 = np.abs(depth1_vals - local_ref)
                diff2 = np.abs(depth2_vals - local_ref)
                
                fused[inconsistent_indices] = np.where(diff1 <= diff2, depth1_vals, depth2_vals)
        
        # 处理单独有效区域
        only1_valid = valid1 & (~valid2)
        only2_valid = valid2 & (~valid1)
        fused[only1_valid] = depth1[only1_valid]
        fused[only2_valid] = depth2[only2_valid]
        
        return fused
    
    def fuse_multiple_depths_20um_threshold(self, depth_maps: List[np.ndarray], 
                                          threshold: float = 0.02) -> np.ndarray:
        """多投影仪融合 - 20微米阈值"""
        print(f"    执行多投影仪20微米精细融合...")
        
        if len(depth_maps) < 2:
            return depth_maps[0] if depth_maps else np.full((2848, 2848), np.nan)
        
        height, width = depth_maps[0].shape
        fused = np.full((height, width), np.nan)
        
        processed_pixels = 0
        consistent_pixels = 0
        outlier_removed = 0
        
        for r in range(height):
            for c in range(width):
                # 收集所有有效深度值
                valid_depths = []
                for depth_map in depth_maps:
                    if np.isfinite(depth_map[r, c]):
                        valid_depths.append(depth_map[r, c])
                
                if len(valid_depths) == 0:
                    continue
                elif len(valid_depths) == 1:
                    fused[r, c] = valid_depths[0]
                else:
                    # 多值融合：使用20微米阈值检测异常值
                    valid_depths = np.array(valid_depths)
                    median_depth = np.median(valid_depths)
                    
                    # 检查一致性
                    depth_diffs = np.abs(valid_depths - median_depth)
                    normal_mask = depth_diffs <= threshold
                    normal_depths = valid_depths[normal_mask]
                    
                    if len(normal_depths) > 0:
                        fused[r, c] = np.mean(normal_depths)
                        if len(normal_depths) == len(valid_depths):
                            consistent_pixels += 1
                        else:
                            outlier_removed += 1
                    else:
                        # 所有值都被认为是异常，使用中位数
                        fused[r, c] = median_depth
                        outlier_removed += 1
                
                processed_pixels += 1
        
        print(f"      处理像素: {processed_pixels:,}")
        print(f"      20微米内一致: {consistent_pixels:,} 位置")
        print(f"      移除异常值: {outlier_removed:,} 位置")
        
        return fused
    
    def fuse_two_depths_basic(self, depth1: np.ndarray, depth2: np.ndarray) -> np.ndarray:
        """基础双投影仪融合"""
        valid1 = np.isfinite(depth1)
        valid2 = np.isfinite(depth2)
        fused = np.full_like(depth1, np.nan)
        
        both_valid = valid1 & valid2
        fused[both_valid] = (depth1[both_valid] + depth2[both_valid]) / 2
        
        only1_valid = valid1 & (~valid2)
        only2_valid = valid2 & (~valid1)
        fused[only1_valid] = depth1[only1_valid]
        fused[only2_valid] = depth2[only2_valid]
        
        return fused
    
    def fuse_multiple_depths_basic(self, depth_maps: List[np.ndarray]) -> np.ndarray:
        """基础多投影仪融合"""
        if len(depth_maps) < 2:
            return depth_maps[0] if depth_maps else np.full((2848, 2848), np.nan)
        
        # 简单平均融合
        fused = np.zeros_like(depth_maps[0])
        count_map = np.zeros_like(depth_maps[0])
        
        for depth_map in depth_maps:
            valid_mask = np.isfinite(depth_map)
            fused[valid_mask] += depth_map[valid_mask]
            count_map[valid_mask] += 1
        
        valid_result = count_map > 0
        fused[valid_result] /= count_map[valid_result]
        fused[~valid_result] = np.nan
        
        return fused
    
    def save_point_cloud(self, depth_map: np.ndarray, output_path: str):
        """保存点云文件"""
        height, width = depth_map.shape
        
        # 生成坐标网格
        x_coords, y_coords = np.meshgrid(np.arange(width), np.arange(height))
        
        # 转换为物理坐标 (mm)
        x_coords = x_coords * self.CCD_DX
        y_coords = y_coords * self.CCD_DX
        z_coords = depth_map
        
        # 过滤有效点
        valid_mask = np.isfinite(depth_map)
        valid_points = np.column_stack([
            x_coords[valid_mask],
            y_coords[valid_mask], 
            z_coords[valid_mask]
        ])
        
        # 生成颜色（基于深度）
        if len(valid_points) > 0:
            z_min, z_max = np.min(valid_points[:, 2]), np.max(valid_points[:, 2])
            z_range = z_max - z_min if z_max > z_min else 1
            z_normalized = (valid_points[:, 2] - z_min) / z_range
            
            # 色彩映射：蓝色(低) -> 绿色(中) -> 红色(高)
            colors = np.zeros((len(valid_points), 3), dtype=np.uint8)
            colors[:, 0] = (z_normalized * 255).astype(np.uint8)  # Red
            colors[:, 1] = ((1 - np.abs(z_normalized - 0.5) * 2) * 255).astype(np.uint8)  # Green  
            colors[:, 2] = ((1 - z_normalized) * 255).astype(np.uint8)  # Blue
        else:
            colors = np.array([]).reshape(0, 3)
        
        # 保存PLY文件
        with open(output_path, 'w') as f:
            f.write("ply\n")
            f.write("format ascii 1.0\n")
            f.write(f"element vertex {len(valid_points)}\n")
            f.write("property float x\n")
            f.write("property float y\n")
            f.write("property float z\n")
            f.write("property uchar red\n")
            f.write("property uchar green\n")
            f.write("property uchar blue\n")
            f.write("end_header\n")
            
            for i in range(len(valid_points)):
                f.write(f"{valid_points[i, 0]:.6f} {valid_points[i, 1]:.6f} {valid_points[i, 2]:.6f} "
                       f"{colors[i, 0]} {colors[i, 1]} {colors[i, 2]}\n")
    
    def process_test_data(self, test_folder: str, calibration_folder: str, 
                         output_folder: str, use_20um_detection: bool = True):
        """处理测试数据"""
        print("=" * 60)
        print("增强版最终标定结果融合程序")
        print("=" * 60)
        
        # 1. 加载标定数据
        if not self.load_mapping_tables(calibration_folder):
            print("标定数据加载失败")
            return False
        
        # 2. 创建输出目录
        os.makedirs(output_folder, exist_ok=True)
        
        print(f"\n开始从测试数据重建深度图: {test_folder}")
        
        # 3. 重建各投影仪深度图
        depth_maps = []
        quality_infos = []
        
        for projector_id in range(4):
            depth_map, quality_info = self.reconstruct_single_projector(test_folder, projector_id)
            depth_maps.append(depth_map)
            quality_infos.append(quality_info)
        
        # 4. 自适应融合
        fused_depth, fusion_info = self.adaptive_fusion(depth_maps, quality_infos, use_20um_detection)
        
        # 5. 保存结果
        # 保存深度图
        depth_path = os.path.join(output_folder, "fused_depth.npy")
        np.save(depth_path, fused_depth)
        print(f"融合深度图已保存: {depth_path}")
        
        # 保存点云
        point_cloud_path = os.path.join(output_folder, "fused_point_cloud.ply")
        self.save_point_cloud(fused_depth, point_cloud_path)
        print(f"点云已保存: {point_cloud_path}")
        
        # 保存融合信息
        info_path = os.path.join(output_folder, "fusion_info.txt")
        self.save_fusion_info(quality_infos, fusion_info, info_path)
        print(f"融合信息已保存: {info_path}")
        
        # 6. 显示总结
        print("\n" + "=" * 60)
        print("融合完成！")
        print("=" * 60)
        print(f"融合策略: {fusion_info.get('fusion_strategy', 'unknown')}")
        print(f"有效投影仪: {fusion_info.get('valid_projectors', [])}")
        print(f"最终覆盖率: {fusion_info.get('final_coverage', 0):.2%}")
        
        if 'final_depth_stats' in fusion_info:
            stats = fusion_info['final_depth_stats']
            print(f"深度范围: {stats['min']:.2f} ~ {stats['max']:.2f} mm")
            print(f"深度标准差: {stats['std']:.3f} mm")
        
        point_count = np.sum(np.isfinite(fused_depth))
        print(f"点云点数: {point_count:,}")
        print(f"结果保存在: {output_folder}")
        
        return True
    
    def save_fusion_info(self, quality_infos: List[Dict], fusion_info: Dict, output_path: str):
        """保存融合信息"""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("增强版最终标定结果融合报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"融合模式: {'增强模式' if self.enhanced_mode else '标准模式'}\n")
            f.write(f"融合策略: {fusion_info.get('fusion_strategy', 'unknown')}\n")
            f.write(f"有效投影仪: {fusion_info.get('valid_projectors', [])} ")
            f.write(f"({fusion_info.get('valid_count', 0)}/{fusion_info.get('total_projectors', 0)})\n")
            
            if fusion_info.get('use_20um_detection'):
                f.write(f"精细检测: 20微米阈值异常检测\n")
            
            f.write("\n各投影仪质量分析:\n")
            for quality_info in quality_infos:
                if quality_info and quality_info.get('projector_id') is not None:
                    proj_id = quality_info['projector_id']
                    valid_ratio = quality_info.get('valid_ratio', 0)
                    
                    if self.enhanced_mode and 'enhanced_score' in quality_info:
                        enhanced_score = quality_info['enhanced_score']
                        grade = quality_info.get('quality_breakdown', {}).get('grade', 'Unknown')
                        f.write(f"  投影仪 {proj_id}: 有效比例 {valid_ratio:.1%}, ")
                        f.write(f"增强评分 {enhanced_score:.3f} ({grade})")
                        
                        if not quality_info.get('is_valid', False):
                            f.write(f" - 拒绝: {quality_info.get('rejection_reason', '未知原因')}")
                        f.write("\n")
                    else:
                        quality_score = quality_info.get('quality_score', 0)
                        f.write(f"  投影仪 {proj_id}: 有效比例 {valid_ratio:.1%}, ")
                        f.write(f"质量分数 {quality_score:.3f}\n")
            
            f.write("\n融合结果:\n")
            f.write(f"  最终有效像素: {fusion_info.get('final_valid_pixels', 0):,}\n")
            f.write(f"  最终覆盖率: {fusion_info.get('final_coverage', 0):.2%}\n")
            
            if 'final_depth_stats' in fusion_info:
                stats = fusion_info['final_depth_stats']
                f.write(f"\n深度统计 (mm):\n")
                f.write(f"  范围: {stats['min']:.2f} ~ {stats['max']:.2f}\n")
                f.write(f"  均值: {stats['mean']:.2f}\n")
                f.write(f"  标准差: {stats['std']:.3f}\n")
                f.write(f"  深度范围: {stats['range']:.2f}\n")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="增强版最终标定结果融合程序")
    parser.add_argument("--test_folder", required=True, help="测试数据文件夹路径")
    parser.add_argument("--calibration_folder", required=True, help="标定结果文件夹路径") 
    parser.add_argument("--output_folder", required=True, help="输出结果文件夹路径")
    parser.add_argument("--enhanced_mode", action="store_true", default=True, 
                       help="启用增强模式（默认启用）")
    parser.add_argument("--standard_mode", action="store_true", 
                       help="使用标准模式（向后兼容）")
    parser.add_argument("--use_20um_detection", action="store_true", default=True,
                       help="使用20微米精细异常检测（默认启用）")
    
    args = parser.parse_args()
    
    # 确定运行模式
    enhanced_mode = args.enhanced_mode and not args.standard_mode
    
    # 创建融合器
    fusion = EnhancedFinalCalibrationFusion(enhanced_mode=enhanced_mode)
    
    # 处理数据
    success = fusion.process_test_data(
        args.test_folder,
        args.calibration_folder, 
        args.output_folder,
        args.use_20um_detection
    )
    
    if not success:
        exit(1)


if __name__ == "__main__":
    main() 