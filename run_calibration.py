#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行相位高度标定的主程序
"""

import os
import time
from phase_height_calibration import PhaseHeightCalibration


def main():
    """主函数"""
    print("=== 相位高度标定程序 ===")
    print("数据结构:")
    print("- 11个位置 (Test-0 到 Test-10)，每次移动1mm")
    print("- 4个投影仪 (0-3号)")
    print("- 4个频率 (8, 64, 512, 1080像素周期)")
    print("- 每个频率5步相移")
    print()
    
    # 创建标定器
    calibrator = PhaseHeightCalibration(calibration_data_folder="标定数据")
    
    # 检查数据文件夹是否存在
    if not os.path.exists("标定数据"):
        print("错误: 标定数据文件夹不存在")
        return
    
    # 检查数据完整性
    print("检查数据完整性...")
    missing_folders = []
    for i in range(11):
        folder_path = os.path.join("标定数据", f"Test-{i}")
        if not os.path.exists(folder_path):
            missing_folders.append(f"Test-{i}")
    
    if missing_folders:
        print(f"警告: 缺少以下文件夹: {missing_folders}")
    else:
        print("数据文件夹完整")
    
    # 开始标定
    print("\n开始标定过程...")
    start_time = time.time()
    
    try:
        # 标定所有投影仪
        calibration_results = calibrator.calibrate_all_projectors(
            model_type='polynomial',  # 使用多项式模型
            degree=2  # 二次多项式
        )
        
        if not calibration_results:
            print("标定失败: 没有成功标定任何投影仪")
            return
        
        # 保存标定结果
        print("\n保存标定结果...")
        calibrator.save_calibration_results("calibration_results")
        
        # 生成可视化结果
        print("生成可视化结果...")
        calibrator.visualize_calibration_results("calibration_results")
        
        # 检查标定质量
        print("\n检查标定质量...")
        quality_report = calibrator.check_calibration_quality()
        
        # 打印质量报告
        print_quality_report(quality_report)
        
        # 保存质量报告
        save_quality_report(quality_report, "calibration_results")
        
        end_time = time.time()
        print(f"\n标定完成! 总耗时: {end_time - start_time:.1f} 秒")
        print("结果文件保存在 'calibration_results' 文件夹中")
        
    except Exception as e:
        print(f"标定过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def print_quality_report(quality_report):
    """打印质量报告"""
    print("\n" + "="*50)
    print("标定质量报告")
    print("="*50)
    
    if "error" in quality_report:
        print(f"错误: {quality_report['error']}")
        return
    
    # 整体质量
    print(f"整体质量: {quality_report['overall_quality']}")
    print(f"平均R²分数: {quality_report['average_r2']:.6f}")
    print(f"平均RMSE: {quality_report['average_rmse']:.4f} mm")
    
    # 各投影仪详情
    print("\n各投影仪详情:")
    print("-" * 60)
    print(f"{'投影仪':<8} {'质量':<12} {'R²分数':<12} {'RMSE(mm)':<12} {'样本数':<10}")
    print("-" * 60)
    
    for proj_id, report in quality_report["projector_reports"].items():
        print(f"{proj_id:<8} {report['quality']:<12} {report['r2_score']:<12.6f} "
              f"{report['rmse']:<12.4f} {report['sample_count']:<10,}")
    
    # 建议
    print("\n建议:")
    for i, recommendation in enumerate(quality_report["recommendations"], 1):
        print(f"{i}. {recommendation}")
    
    print("="*50)


def save_quality_report(quality_report, output_folder):
    """保存质量报告到文件"""
    import json
    
    # 保存为JSON格式
    report_file = os.path.join(output_folder, "quality_report.json")
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(quality_report, f, indent=2, ensure_ascii=False)
    
    # 保存为文本格式
    report_text_file = os.path.join(output_folder, "quality_report.txt")
    with open(report_text_file, 'w', encoding='utf-8') as f:
        f.write("相位高度标定质量报告\n")
        f.write("="*50 + "\n\n")
        
        if "error" in quality_report:
            f.write(f"错误: {quality_report['error']}\n")
            return
        
        f.write(f"整体质量: {quality_report['overall_quality']}\n")
        f.write(f"平均R²分数: {quality_report['average_r2']:.6f}\n")
        f.write(f"平均RMSE: {quality_report['average_rmse']:.4f} mm\n\n")
        
        f.write("各投影仪详情:\n")
        f.write("-" * 60 + "\n")
        f.write(f"{'投影仪':<8} {'质量':<12} {'R²分数':<12} {'RMSE(mm)':<12} {'样本数':<10}\n")
        f.write("-" * 60 + "\n")
        
        for proj_id, report in quality_report["projector_reports"].items():
            f.write(f"{proj_id:<8} {report['quality']:<12} {report['r2_score']:<12.6f} "
                   f"{report['rmse']:<12.4f} {report['sample_count']:<10,}\n")
        
        f.write("\n建议:\n")
        for i, recommendation in enumerate(quality_report["recommendations"], 1):
            f.write(f"{i}. {recommendation}\n")
    
    print(f"质量报告已保存到: {report_file} 和 {report_text_file}")


def test_calibration_model():
    """测试标定模型的使用"""
    print("\n=== 测试标定模型 ===")
    
    # 加载标定结果
    calibrator = PhaseHeightCalibration()
    calibrator.load_calibration_results("calibration_results/calibration_results.pkl")
    
    if not calibrator.calibration_results:
        print("没有找到标定结果，请先运行标定")
        return
    
    # 使用第一个标定位置的数据进行测试
    test_folder = os.path.join("标定数据", "Test-5")  # 使用中间位置测试
    expected_height = 5.0  # mm
    
    print(f"使用 {test_folder} 进行测试 (期望高度: {expected_height} mm)")
    
    for proj_id in calibrator.calibration_results.keys():
        print(f"\n测试投影仪 {proj_id}:")
        
        # 处理相位展开
        unwrapped_phase = calibrator.process_single_position(test_folder, proj_id)
        
        if unwrapped_phase is not None:
            # 预测高度
            height_map = calibrator.predict_height(unwrapped_phase, proj_id)
            
            # 计算统计信息
            valid_heights = height_map[~np.isnan(height_map)]
            if len(valid_heights) > 0:
                mean_height = np.mean(valid_heights)
                std_height = np.std(valid_heights)
                error = abs(mean_height - expected_height)
                
                print(f"  预测高度: {mean_height:.3f} ± {std_height:.3f} mm")
                print(f"  期望高度: {expected_height:.3f} mm")
                print(f"  误差: {error:.3f} mm")
                print(f"  有效像素: {len(valid_heights):,}")
            else:
                print("  没有有效的高度预测")
        else:
            print("  相位展开失败")


def quick_test():
    """快速测试单个投影仪"""
    print("=== 快速测试 (仅投影仪0) ===")
    
    calibrator = PhaseHeightCalibration(calibration_data_folder="标定数据")
    
    # 仅标定投影仪0
    result = calibrator.calibrate_projector(0, model_type='polynomial', degree=2)
    
    if result:
        print("快速测试成功!")
        
        # 保存结果
        calibrator.calibration_results[0] = result
        calibrator.save_calibration_results("quick_test_results")
        calibrator.visualize_calibration_results("quick_test_results")
        
        # 质量检查
        quality_report = calibrator.check_calibration_quality()
        print_quality_report(quality_report)
    else:
        print("快速测试失败")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "quick":
            quick_test()
        elif sys.argv[1] == "test":
            test_calibration_model()
        else:
            print("用法: python run_calibration.py [quick|test]")
    else:
        main()
