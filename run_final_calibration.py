#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行最终的相位差标定程序
"""

import os
import time
import numpy as np
from final_phase_difference_calibration import PhaseDifferenceCalibration


def print_quality_report(quality_report):
    """打印质量报告"""
    print("\n" + "="*70)
    print("相位差标定质量报告")
    print("="*70)
    
    if "error" in quality_report:
        print(f"错误: {quality_report['error']}")
        return
    
    # 整体质量
    print(f"标定方法: {quality_report['method']}")
    print(f"整体质量: {quality_report['overall_quality']}")
    print(f"已标定投影仪数量: {quality_report['num_calibrated_projectors']}")
    print(f"平均R²分数: {quality_report['average_r2']:.6f}")
    print(f"平均RMSE: {quality_report['average_rmse']:.4f} mm")
    print(f"平均相关系数: {quality_report['average_correlation']:.6f}")
    
    # 各投影仪详情
    print("\n各投影仪详情:")
    print("-" * 90)
    print(f"{'投影仪':<8} {'质量':<12} {'R²分数':<12} {'RMSE(mm)':<12} {'相关系数':<12} {'模型类型':<15}")
    print("-" * 90)
    
    for proj_id, report in quality_report["projector_reports"].items():
        print(f"{proj_id:<8} {report['quality']:<12} {report['r2_score']:<12.6f} "
              f"{report['rmse']:<12.4f} {report['correlation']:<12.6f} {report['model_type']:<15}")
    
    # 建议
    print("\n建议:")
    for i, recommendation in enumerate(quality_report["recommendations"], 1):
        print(f"{i}. {recommendation}")
    
    print("="*70)


def check_data_integrity():
    """检查数据完整性"""
    print("检查数据完整性...")
    
    calibration_folder = "标定数据"
    if not os.path.exists(calibration_folder):
        print(f"错误: 标定数据文件夹不存在: {calibration_folder}")
        return False
    
    missing_folders = []
    incomplete_folders = []
    
    for i in range(11):
        folder_path = os.path.join(calibration_folder, f"Test-{i}")
        if not os.path.exists(folder_path):
            missing_folders.append(f"Test-{i}")
        else:
            # 检查图像文件数量
            image_count = len([f for f in os.listdir(folder_path) if f.endswith('.bmp')])
            if image_count < 80:
                incomplete_folders.append(f"Test-{i} ({image_count} images)")
    
    if missing_folders:
        print(f"缺少文件夹: {missing_folders}")
        return False
    
    if incomplete_folders:
        print(f"图像不完整的文件夹: {incomplete_folders}")
        print("警告: 某些文件夹的图像数量不足，可能影响标定质量")
    
    print("数据完整性检查通过")
    return True


def run_complete_calibration():
    """运行完整的相位差标定"""
    print("=== 相位差标定程序 ===")
    print("标定方法: 相位差法")
    print("数据结构:")
    print("- 11个高度位置 (Test-0 到 Test-10)，每次移动1mm")
    print("- 4个投影仪 (0-3号)")
    print("- 使用1080像素周期（最稳定的频率）")
    print("- 以Test-0为参考位置")
    print()
    
    # 检查数据完整性
    if not check_data_integrity():
        print("数据完整性检查失败，请检查数据文件")
        return
    
    # 创建标定器
    calibrator = PhaseDifferenceCalibration(calibration_data_folder="标定数据")
    
    # 开始标定
    print("\n开始相位差标定过程...")
    start_time = time.time()
    
    try:
        # 标定所有投影仪
        calibration_results = calibrator.calibrate_all_projectors_phase_difference(frequency=1080)
        
        if not calibration_results:
            print("标定失败: 没有成功标定任何投影仪")
            return
        
        # 保存标定结果
        print("\n保存标定结果...")
        quality_report = calibrator.save_calibration_results("final_phase_difference_results")
        
        # 打印质量报告
        print_quality_report(quality_report)
        
        # 生成详细报告
        save_detailed_report(calibration_results, quality_report, "final_phase_difference_results")
        
        # 生成可视化结果
        print("\n生成可视化结果...")
        create_visualization(calibration_results, "final_phase_difference_results")
        
        end_time = time.time()
        print(f"\n相位差标定完成! 总耗时: {end_time - start_time:.1f} 秒")
        print("结果文件保存在 'final_phase_difference_results' 文件夹中")
        
        # 性能总结
        print_performance_summary(quality_report)
        
    except Exception as e:
        print(f"标定过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def save_detailed_report(calibration_results, quality_report, output_folder):
    """保存详细报告"""
    report_file = os.path.join(output_folder, "detailed_calibration_report.txt")
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("相位差标定详细报告\n")
        f.write("="*70 + "\n\n")
        
        # 标定概述
        f.write("标定概述:\n")
        f.write(f"- 标定时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"- 标定方法: 相位差法\n")
        f.write(f"- 成功标定投影仪数量: {len(calibration_results)}\n")
        f.write(f"- 使用频率: 1080像素周期\n")
        f.write(f"- 参考位置: Test-0\n")
        f.write(f"- 高度范围: 0-10mm (步长1mm)\n\n")
        
        # 方法说明
        f.write("方法说明:\n")
        f.write("相位差法是通过计算各高度位置与参考位置的相位差来建立\n")
        f.write("相位差与高度的映射关系。这种方法避免了相位展开的复杂性，\n")
        f.write("直接利用包裹相位的差值进行标定，具有更高的稳定性和精度。\n\n")
        
        # 整体质量评估
        f.write("整体质量评估:\n")
        f.write(f"- 整体质量: {quality_report['overall_quality']}\n")
        f.write(f"- 平均R²分数: {quality_report['average_r2']:.6f}\n")
        f.write(f"- 平均RMSE: {quality_report['average_rmse']:.4f} mm\n")
        f.write(f"- 平均相关系数: {quality_report['average_correlation']:.6f}\n\n")
        
        # 各投影仪详细信息
        f.write("各投影仪详细信息:\n")
        f.write("-" * 70 + "\n")
        
        for proj_id, result in calibration_results.items():
            model_info = result['model_info']
            f.write(f"\n投影仪 {proj_id} ({result['projector_name']}):\n")
            f.write(f"  频率: {result['frequency']}像素周期\n")
            f.write(f"  参考位置: Test-{result['reference_position']}\n")
            f.write(f"  模型类型: {model_info['model_type']}\n")
            f.write(f"  性能指标:\n")
            f.write(f"    R² 分数: {model_info['r2_score']:.6f}\n")
            f.write(f"    RMSE: {model_info['rmse']:.4f} mm\n")
            f.write(f"    相关系数: {model_info['correlation']:.6f}\n")
            f.write(f"    残差标准差: {model_info['residual_std']:.4f} mm\n")
            f.write(f"  数据统计:\n")
            f.write(f"    样本数量: {model_info['n_samples']:,}\n")
            f.write(f"    相位差范围: [{model_info['phase_diff_range'][0]:.3f}, {model_info['phase_diff_range'][1]:.3f}] rad\n")
            f.write(f"    高度范围: [{model_info['height_range'][0]:.1f}, {model_info['height_range'][1]:.1f}] mm\n")
            f.write(f"  质量评估: {quality_report['projector_reports'][proj_id]['quality']}\n")
        
        # 使用说明
        f.write(f"\n使用说明:\n")
        f.write(f"1. 标定模型已保存在 calibration_models.pkl 文件中\n")
        f.write(f"2. 使用时需要提供当前相位图和参考相位图\n")
        f.write(f"3. 调用 predict_height_from_phase_difference() 方法进行高度预测\n")
        f.write(f"4. 参考相位图应该是Test-0位置的相位图\n")
        f.write(f"5. 确保输入相位差的范围在标定范围内\n\n")
        
        # 建议
        f.write(f"建议:\n")
        for i, recommendation in enumerate(quality_report["recommendations"], 1):
            f.write(f"{i}. {recommendation}\n")
    
    print(f"详细报告已保存到: {report_file}")


def create_visualization(calibration_results, output_folder):
    """创建可视化结果"""
    import matplotlib.pyplot as plt
    
    # 创建总结图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Phase Difference Calibration Results', fontsize=16)
    
    # 收集数据
    proj_ids = []
    r2_scores = []
    rmse_values = []
    correlations = []
    
    for proj_id, result in calibration_results.items():
        model_info = result['model_info']
        proj_ids.append(f'Proj {proj_id}')
        r2_scores.append(model_info['r2_score'])
        rmse_values.append(model_info['rmse'])
        correlations.append(abs(model_info['correlation']))
    
    # 1. R²分数对比
    ax1 = axes[0, 0]
    bars1 = ax1.bar(proj_ids, r2_scores, color='skyblue', edgecolor='navy')
    ax1.set_ylabel('R² Score')
    ax1.set_title('R² Score Comparison')
    ax1.set_ylim(0, 1)
    ax1.grid(True, alpha=0.3)
    
    for bar, score in zip(bars1, r2_scores):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{score:.3f}', ha='center', va='bottom')
    
    # 2. RMSE对比
    ax2 = axes[0, 1]
    bars2 = ax2.bar(proj_ids, rmse_values, color='lightcoral', edgecolor='darkred')
    ax2.set_ylabel('RMSE (mm)')
    ax2.set_title('RMSE Comparison')
    ax2.grid(True, alpha=0.3)
    
    for bar, rmse in zip(bars2, rmse_values):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(rmse_values)*0.01,
                f'{rmse:.3f}', ha='center', va='bottom')
    
    # 3. 相关系数对比
    ax3 = axes[1, 0]
    bars3 = ax3.bar(proj_ids, correlations, color='lightgreen', edgecolor='darkgreen')
    ax3.set_ylabel('|Correlation|')
    ax3.set_title('Phase Difference - Height Correlation')
    ax3.set_ylim(0, 1)
    ax3.grid(True, alpha=0.3)
    
    for bar, corr in zip(bars3, correlations):
        ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{corr:.3f}', ha='center', va='bottom')
    
    # 4. 总体统计
    ax4 = axes[1, 1]
    ax4.axis('off')
    
    avg_r2 = np.mean(r2_scores)
    avg_rmse = np.mean(rmse_values)
    avg_corr = np.mean(correlations)
    
    summary_text = f"""
    Phase Difference Calibration Summary:
    
    Method: Phase Difference
    Frequency: 1080 pixels period
    Reference: Test-0 position
    
    Average Performance:
    R² Score: {avg_r2:.6f}
    RMSE: {avg_rmse:.4f} mm
    |Correlation|: {avg_corr:.6f}
    
    Quality Assessment:
    {'Excellent' if avg_r2 > 0.95 and avg_rmse < 0.5 else 
     'Good' if avg_r2 > 0.90 and avg_rmse < 1.0 else 
     'Fair' if avg_r2 > 0.80 and avg_rmse < 2.0 else 'Poor'}
    
    Calibrated Projectors: {len(calibration_results)}
    """
    
    ax4.text(0.05, 0.95, summary_text, transform=ax4.transAxes, fontsize=11,
            verticalalignment='top', fontfamily='monospace',
            bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_folder, 'phase_difference_calibration_summary.png'), 
               dpi=150, bbox_inches='tight')
    plt.close()
    
    print("可视化结果已保存")


def print_performance_summary(quality_report):
    """打印性能总结"""
    print("\n" + "="*50)
    print("性能总结")
    print("="*50)
    
    avg_r2 = quality_report['average_r2']
    avg_rmse = quality_report['average_rmse']
    avg_corr = quality_report['average_correlation']
    
    print(f"相位差标定法性能:")
    print(f"- 平均R²分数: {avg_r2:.6f}")
    print(f"- 平均RMSE: {avg_rmse:.4f} mm")
    print(f"- 平均相关系数: {avg_corr:.6f}")
    
    if avg_r2 > 0.95 and avg_rmse < 0.5:
        print("✓ 标定质量: 优秀")
        print("✓ 适用于高精度三维测量")
    elif avg_r2 > 0.90 and avg_rmse < 1.0:
        print("✓ 标定质量: 良好")
        print("✓ 适用于一般精度三维测量")
    else:
        print("⚠ 标定质量: 需要改进")
        print("⚠ 建议检查数据质量或重新标定")
    
    print("\n相位差法的优势:")
    print("1. 避免了复杂的相位展开过程")
    print("2. 直接利用包裹相位差值，稳定性高")
    print("3. 线性关系明显，标定精度高")
    print("4. 对噪声和干扰具有较好的鲁棒性")
    
    print("="*50)


def test_single_projector():
    """测试单个投影仪"""
    print("=== 测试单个投影仪相位差标定 ===")
    
    if not check_data_integrity():
        return
    
    calibrator = PhaseDifferenceCalibration()
    
    # 测试投影仪0
    result = calibrator.calibrate_projector_with_phase_difference(0, frequency=1080)
    
    if result:
        print("单投影仪相位差标定测试成功!")
        
        # 保存结果
        calibrator.calibration_results[0] = result
        quality_report = calibrator.save_calibration_results("single_projector_phase_diff_test")
        
        print_quality_report(quality_report)
    else:
        print("单投影仪相位差标定测试失败")


def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "test":
            test_single_projector()
        elif sys.argv[1] == "full":
            run_complete_calibration()
        else:
            print("用法: python run_final_calibration.py [test|full]")
            print("  test: 测试单个投影仪相位差标定")
            print("  full: 运行完整的相位差标定流程")
    else:
        run_complete_calibration()


if __name__ == "__main__":
    main()
