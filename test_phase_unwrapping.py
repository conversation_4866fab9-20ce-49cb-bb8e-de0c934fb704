#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
四频五步相移相位展开测试脚本
"""

import numpy as np
import matplotlib.pyplot as plt
import os
from phase_unwrapping_four_frequency import FourFrequencyPhaseUnwrapping
from generate_test_images import generate_test_images, generate_realistic_test_images


def test_phase_unwrapping():
    """测试相位展开算法"""
    print("=== 四频五步相移相位展开测试 ===\n")
    
    # 1. 生成测试图像
    print("1. 生成测试图像...")
    test_folder = "test_input_images"
    generate_test_images(test_folder, width=640, height=480, noise_level=0.02)
    
    # 2. 创建相位展开对象
    print("\n2. 初始化相位展开器...")
    pixel_periods = [1080, 512, 64, 8]  # 从低频到高频
    unwrapper = FourFrequencyPhaseUnwrapping(pixel_periods=pixel_periods)
    
    # 3. 执行相位展开
    print("\n3. 执行相位展开...")
    results = unwrapper.process_phase_unwrapping(
        image_folder=test_folder,
        output_folder="test_results",
        min_modulation=0.1
    )
    
    # 4. 分析结果
    print("\n4. 分析结果...")
    analyze_results(results, pixel_periods)
    
    return results


def analyze_results(results: dict, pixel_periods: list):
    """分析相位展开结果"""
    print("\n=== 结果分析 ===")
    
    # 分析包裹相位
    wrapped_phases = results['wrapped_phases']
    modulations = results['modulations']
    unwrapped_phase = results['unwrapped_phase_masked']
    quality_mask = results['quality_mask']
    
    print(f"图像尺寸: {wrapped_phases[0].shape}")
    print(f"有效像素比例: {np.sum(quality_mask) / quality_mask.size * 100:.1f}%")
    
    # 各频率的统计信息
    for i, period in enumerate(pixel_periods):
        phase = wrapped_phases[i]
        mod = modulations[i]
        
        print(f"\n频率 {i+1} (周期 {period} 像素):")
        print(f"  相位范围: [{np.min(phase):.3f}, {np.max(phase):.3f}] rad")
        print(f"  平均调制度: {np.mean(mod):.3f}")
        print(f"  调制度标准差: {np.std(mod):.3f}")
    
    # 展开相位统计
    valid_unwrapped = unwrapped_phase[~np.isnan(unwrapped_phase)]
    if len(valid_unwrapped) > 0:
        print(f"\n展开相位:")
        print(f"  范围: [{np.min(valid_unwrapped):.3f}, {np.max(valid_unwrapped):.3f}] rad")
        print(f"  平均值: {np.mean(valid_unwrapped):.3f} rad")
        print(f"  标准差: {np.std(valid_unwrapped):.3f} rad")


def create_comparison_plot(results: dict, pixel_periods: list):
    """创建对比图"""
    wrapped_phases = results['wrapped_phases']
    unwrapped_phase = results['unwrapped_phase_masked']
    quality_mask = results['quality_mask']
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 显示各频率的包裹相位
    for i in range(min(4, len(wrapped_phases))):
        row = i // 2
        col = i % 2
        if row < 2 and col < 2:
            im = axes[row, col].imshow(wrapped_phases[i], cmap='hsv', vmin=-np.pi, vmax=np.pi)
            axes[row, col].set_title(f'Wrapped Phase\nPeriod {pixel_periods[i]} pixels')
            axes[row, col].axis('off')
            plt.colorbar(im, ax=axes[row, col], fraction=0.046, pad=0.04)
    
    # 显示展开相位
    im = axes[0, 2].imshow(unwrapped_phase, cmap='jet')
    axes[0, 2].set_title('Unwrapped Phase')
    axes[0, 2].axis('off')
    plt.colorbar(im, ax=axes[0, 2], fraction=0.046, pad=0.04)
    
    # 显示质量掩码
    axes[1, 2].imshow(quality_mask, cmap='gray')
    axes[1, 2].set_title('Quality Mask')
    axes[1, 2].axis('off')
    
    plt.tight_layout()
    plt.savefig('phase_unwrapping_comparison.png', dpi=150, bbox_inches='tight')
    plt.show()


def test_with_realistic_data():
    """使用更真实的数据进行测试"""
    print("\n=== 真实数据测试 ===")
    
    # 生成真实测试图像
    realistic_folder = "realistic_test_images"
    generate_realistic_test_images(realistic_folder)
    
    # 创建相位展开对象
    unwrapper = FourFrequencyPhaseUnwrapping(pixel_periods=[1080, 512, 64, 8])
    
    # 执行相位展开
    results = unwrapper.process_phase_unwrapping(
        image_folder=realistic_folder,
        output_folder="realistic_results",
        min_modulation=0.05  # 降低阈值以适应更真实的数据
    )
    
    return results


def profile_performance():
    """性能分析"""
    import time
    
    print("\n=== 性能分析 ===")
    
    # 测试不同图像尺寸的处理时间
    sizes = [(320, 240), (640, 480), (1280, 960)]
    
    for width, height in sizes:
        print(f"\n测试图像尺寸: {width}x{height}")
        
        # 生成测试图像
        test_folder = f"perf_test_{width}x{height}"
        start_time = time.time()
        generate_test_images(test_folder, width=width, height=height, noise_level=0.01)
        gen_time = time.time() - start_time
        
        # 执行相位展开
        unwrapper = FourFrequencyPhaseUnwrapping()
        start_time = time.time()
        results = unwrapper.process_phase_unwrapping(
            image_folder=test_folder,
            output_folder=f"perf_results_{width}x{height}"
        )
        process_time = time.time() - start_time
        
        print(f"  图像生成时间: {gen_time:.2f} 秒")
        print(f"  相位展开时间: {process_time:.2f} 秒")
        print(f"  总处理时间: {gen_time + process_time:.2f} 秒")
        
        # 清理临时文件
        import shutil
        if os.path.exists(test_folder):
            shutil.rmtree(test_folder)


def main():
    """主函数"""
    print("四频五步相移相位展开测试程序")
    print("=" * 50)
    
    try:
        # 基础测试
        results = test_phase_unwrapping()
        
        # 创建对比图
        create_comparison_plot(results, [1080, 512, 64, 8])
        
        # 真实数据测试
        realistic_results = test_with_realistic_data()
        
        # 性能分析
        profile_performance()
        
        print("\n" + "=" * 50)
        print("所有测试完成！")
        print("请查看生成的结果文件夹和图像文件。")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
