# 双频相位测量系统

基于512和8像素周期的双频五步相移3D测量系统

## 📋 系统概述

### 🎯 设计目标
- **效率提升**: 相比四频系统减少50%的图像数量（每投影仪10张 vs 20张）
- **精度保持**: 使用8像素周期保持高测量精度
- **稳定性**: 512像素周期提供稳定的相位展开
- **实用性**: 适合实时和快速测量应用

### 🔧 技术特点
- **双频配置**: 512像素周期（低频） + 8像素周期（高频）
- **周期比**: 64:1，提供充足的展开能力
- **相位展开**: 使用512像素周期展开8像素周期相位
- **多项式标定**: 3次多项式相位-高度映射
- **自适应融合**: 基于质量的多投影仪数据融合

## 📁 文件结构

```
dual_freq_system/
├── dual_freq_phase_unwrap.py      # 双频相位展开算法
├── dual_freq_calibration.py       # 双频标定系统
├── dual_freq_fusion.py           # 双频融合重建系统
├── run_dual_freq_system.py       # 运行脚本
└── README_双频系统.md             # 本文档
```

## 🚀 快速开始

### 1. 环境要求
```bash
# Python 依赖
numpy
opencv-python
scipy
tqdm
pathlib
```

### 2. 数据准备

#### 标定数据结构
```
标定数据/
├── Test-0/          # 0mm高度
├── Test-1/          # 1mm高度
├── ...
└── Test-10/         # 10mm高度

每个Test-X文件夹包含80张BMP图像：
- 01-20.bmp: 投影仪0的图像
- 21-40.bmp: 投影仪1的图像  
- 41-60.bmp: 投影仪2的图像
- 61-80.bmp: 投影仪3的图像

每个投影仪的图像分布：
- 01-05: 8像素周期 (5步相移)
- 11-15: 512像素周期 (5步相移)
- 其他: 不使用
```

#### 测试数据结构
```
pcb/
├── test1/           # 测试样本1
├── test2/           # 测试样本2
└── ...

每个测试文件夹包含80张BMP图像（格式同标定数据）
```

### 3. 运行系统

#### 完整流程
```bash
cd dual_freq_system
python run_dual_freq_system.py --mode all
```

#### 分步执行

**步骤1: 测试相位展开**
```bash
python run_dual_freq_system.py --mode test
```

**步骤2: 执行标定**
```bash
python run_dual_freq_system.py --mode calibrate \
    --calibration_data ../标定数据 \
    --calibration_output dual_freq_calibration_results
```

**步骤3: 融合重建**
```bash
python run_dual_freq_system.py --mode fusion \
    --test_data ../pcb \
    --calibration_output dual_freq_calibration_results \
    --fusion_output dual_freq_fusion_results
```

**步骤4: 性能对比**
```bash
python run_dual_freq_system.py --mode compare
```

## 📊 输出结果

### 标定结果
```
dual_freq_calibration_results/
├── mapping_tables/              # 映射表文件
│   ├── DLP-0-0.tiff            # 投影仪0常数项
│   ├── DLP-0-1.tiff            # 投影仪0一次项
│   ├── DLP-0-2.tiff            # 投影仪0二次项
│   └── ...
├── calibration_results.pkl     # 完整标定数据
├── quality_report.json         # 质量报告
└── detailed_calibration_report.txt  # 详细报告
```

### 融合结果
```
dual_freq_fusion_results/
├── test1_depth.tiff            # 深度图
├── test1_pointcloud.ply        # 点云文件
├── test1_info.json            # 处理信息
└── ...
```

## 🔬 算法原理

### 双频相位展开
1. **包裹相位计算**: 使用五步相移算法计算512和8像素周期的包裹相位
2. **调制度评估**: 计算条纹对比度，过滤低质量区域
3. **相位展开**: 使用512像素周期的相位展开8像素周期的相位
4. **质量控制**: 基于调制度阈值进行质量筛选

### 标定算法
1. **数据收集**: 收集11个高度位置的展开相位数据
2. **逐像素拟合**: 使用3次多项式拟合相位-高度关系
3. **映射表生成**: 保存多项式系数为TIFF格式映射表
4. **质量评估**: 计算RMSE、平均误差等质量指标

### 融合算法
1. **单投影仪重建**: 使用双频展开和标定结果重建深度图
2. **质量评估**: 基于有效像素比例和深度一致性评分
3. **自适应融合**: 根据质量权重进行多投影仪数据融合
4. **异常值检测**: 使用Z-score检测和去除异常像素

## 📈 性能对比

| 指标 | 双频系统 | 四频系统 | 改进 |
|------|----------|----------|------|
| 图像数量 | 10张/投影仪 | 20张/投影仪 | **-50%** |
| 采集时间 | 减少50% | 基准 | **+100%效率** |
| 处理速度 | 更快 | 较慢 | **+50%速度** |
| 存储需求 | 减少50% | 基准 | **-50%存储** |
| 测量精度 | 高精度 | 最高精度 | 略微降低 |
| 稳定性 | 良好 | 最佳 | 略微降低 |

## 🎯 适用场景

### ✅ 推荐使用双频系统
- **实时测量**: 需要快速获得结果的应用
- **批量检测**: 大量样本的快速筛选
- **移动设备**: 存储和计算资源有限的场合
- **在线检测**: 生产线上的质量控制
- **精度要求适中**: RMSE < 1mm的测量需求

### ⚠️ 建议使用四频系统
- **超高精度**: RMSE < 0.3mm的严格要求
- **复杂表面**: 高反射、低对比度表面
- **研究开发**: 算法验证和性能评估
- **标准测量**: 作为参考标准的测量

## 🔧 参数调整

### 相位展开参数
```python
# dual_freq_phase_unwrap.py
DualFrequencyUnwrapper(
    period_low=512.0,           # 低频周期
    period_high=8.0,            # 高频周期  
    modulation_threshold=0.1    # 调制度阈值
)
```

### 标定参数
```python
# dual_freq_calibration.py
fit_degree = 3                  # 多项式拟合度数
height_step = 1.0              # 高度步长(mm)
num_positions = 11             # 标定位置数
```

### 融合参数
```python
# dual_freq_fusion.py
outlier_threshold = 3.0        # 异常值检测阈值(σ)
min_valid_ratio = 0.1          # 最小有效像素比例
```

## 🐛 故障排除

### 常见问题

**1. 相位展开失败**
- 检查图像质量和对比度
- 调整调制度阈值
- 确认图像文件完整性

**2. 标定精度不佳**
- 增加标定位置数量
- 检查机械精度
- 优化光照条件

**3. 融合结果异常**
- 检查标定结果质量
- 调整异常值检测阈值
- 验证投影仪同步性

### 性能优化

**1. 提高处理速度**
- 使用多线程处理
- 优化图像读取
- 减少内存拷贝

**2. 改善测量精度**
- 优化相移步长精度
- 改善图像质量
- 增加标定数据量

## 📞 技术支持

如有问题或建议，请参考：
1. 检查输入数据格式和完整性
2. 查看详细错误日志
3. 对比四频系统结果验证
4. 调整算法参数重新测试

---

**版本**: 1.0  
**日期**: 2025-07-29  
**作者**: AI Assistant  
**基于**: 最终系统_四频分析
