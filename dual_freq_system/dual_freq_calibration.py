"""
dual_freq_calibration.py
========================
基于512和8像素周期的双频相位-高度标定系统

参考最终系统_四频分析中的phase_height_calibration_final.py
使用双频相位展开进行标定

标定流程：
1. 读取标定数据文件夹中的512和8周期图像
2. 使用双频相位展开获得绝对相位
3. 建立相位到高度的多项式映射关系
4. 保存标定结果

作者：AI Assistant
日期：2025-07-29
"""

import numpy as np
import cv2
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import pickle
import json
from datetime import datetime
import os
from tqdm import tqdm

# 导入双频相位展开
from dual_freq_phase_unwrap import DualFrequencyUnwrapper


class DualFrequencyCalibrator:
    """双频相位-高度标定器"""
    
    def __init__(self, calibration_data_folder: str = "标定数据"):
        """
        初始化标定器
        Args:
            calibration_data_folder: 标定数据文件夹路径
        """
        self.calibration_data_folder = calibration_data_folder
        
        # 投影仪配置：每个投影仪对应的图像编号范围
        self.projector_configs = {
            0: {"name": "Projector_0", "image_range": (1, 20)},
            1: {"name": "Projector_1", "image_range": (21, 40)},
            2: {"name": "Projector_2", "image_range": (41, 60)},
            3: {"name": "Projector_3", "image_range": (61, 80)}
        }
        
        # 双频配置：只使用512和8像素周期
        self.frequency_configs = {
            8: {"image_indices": [1, 2, 3, 4, 5], "name": "8px_period"},      
            512: {"image_indices": [11, 12, 13, 14, 15], "name": "512px_period"} 
        }
        
        # 标定参数
        self.height_step = 1.0  # mm，每次移动1mm
        self.num_positions = 11  # Test-0 到 Test-10
        self.fit_degree = 3  # 多项式拟合度数
        
        # 创建双频相位展开器
        self.dual_unwrapper = DualFrequencyUnwrapper(
            period_low=512.0,
            period_high=8.0,
            modulation_threshold=0.1
        )
        
        # 结果存储
        self.calibration_results = {}
        self.mapping_tables = {}
        
        print(f"双频标定器初始化完成")
        print(f"标定数据文件夹: {calibration_data_folder}")
        print(f"使用频率: 512像素周期 + 8像素周期")
        
    def load_dual_frequency_images(self, test_folder: str, projector_id: int) -> Dict[int, List[np.ndarray]]:
        """
        加载指定投影仪的双频相移图像
        Args:
            test_folder: 测试文件夹路径
            projector_id: 投影仪ID (0-3)
        Returns:
            images_by_frequency: 按频率组织的图像字典
        """
        start_idx, _ = self.projector_configs[projector_id]["image_range"]
        images_by_frequency = {}
        
        for freq in [512, 8]:  # 只加载这两个频率
            freq_config = self.frequency_configs[freq]
            freq_images = []
            
            for img_idx in freq_config["image_indices"]:
                # 计算实际文件编号
                actual_idx = start_idx + img_idx - 1
                img_path = Path(test_folder) / f"{actual_idx:02d}.bmp"
                
                if not img_path.exists():
                    print(f"    警告: 图像文件不存在 {img_path}")
                    return None
                
                # 读取图像
                img = cv2.imread(str(img_path), cv2.IMREAD_GRAYSCALE)
                if img is None:
                    print(f"    错误: 无法读取图像 {img_path}")
                    return None
                
                freq_images.append(img.astype(np.float32))
            
            images_by_frequency[freq] = freq_images
            print(f"    加载频率 {freq}: {len(freq_images)} 张图像")
        
        return images_by_frequency
    
    def process_single_position_unwrapping(self, test_folder: str, projector_id: int) -> Optional[np.ndarray]:
        """
        处理单个位置的双频相位展开
        Args:
            test_folder: 测试文件夹路径
            projector_id: 投影仪ID
        Returns:
            unwrapped_phase: 展开的绝对相位图
        """
        try:
            print(f"    使用双频相位展开处理...")
            
            # 加载图像
            images_by_frequency = self.load_dual_frequency_images(test_folder, projector_id)
            
            if images_by_frequency is None:
                print(f"    图像加载失败")
                return None
            
            # 准备双频图像组
            frames_512 = np.stack(images_by_frequency[512], axis=-1)  # (H, W, 5)
            frames_8 = np.stack(images_by_frequency[8], axis=-1)      # (H, W, 5)
            
            # 执行双频相位展开
            unwrapped_phase, info = self.dual_unwrapper.unwrap_dual_frequency(frames_512, frames_8)
            
            # 检查结果质量
            valid_ratio = info['valid_ratio_combined']
            if valid_ratio < 0.5:  # 如果有效像素少于50%
                print(f"    警告: 有效像素比例过低 ({valid_ratio:.1%})")
            
            return unwrapped_phase
            
        except Exception as e:
            print(f"    双频相位展开失败: {e}")
            return None
    
    def collect_unwrapped_phases(self, projector_id: int) -> Tuple[List[np.ndarray], List[float]]:
        """
        收集所有位置的展开相位数据
        Args:
            projector_id: 投影仪ID
        Returns:
            unwrapped_phases: 展开相位图列表
            heights: 对应的高度列表
        """
        print(f"正在收集投影仪 {projector_id} 的双频展开相位数据...")
        
        unwrapped_phases = []
        heights = []
        
        for position in range(self.num_positions):
            test_folder = Path(self.calibration_data_folder) / f"Test-{position}"
            height = position * self.height_step
            
            print(f"  处理位置 {position} (高度 {height}mm): {test_folder}")
            
            if not test_folder.exists():
                print(f"    跳过: 文件夹不存在")
                continue
            
            # 处理相位展开
            unwrapped_phase = self.process_single_position_unwrapping(str(test_folder), projector_id)
            
            if unwrapped_phase is not None:
                unwrapped_phases.append(unwrapped_phase)
                heights.append(height)
                print(f"    成功: 相位范围 [{np.nanmin(unwrapped_phase):.3f}, {np.nanmax(unwrapped_phase):.3f}]")
            else:
                print(f"    失败: 跳过此位置")
        
        print(f"投影仪 {projector_id} 收集完成: {len(unwrapped_phases)} 个有效位置")
        return unwrapped_phases, heights
    
    def create_mapping_table(self, unwrapped_phases: List[np.ndarray], 
                           heights: List[float], projector_id: int) -> List[np.ndarray]:
        """
        创建相位到高度的映射表
        Args:
            unwrapped_phases: 展开相位图列表
            heights: 对应高度列表
            projector_id: 投影仪ID
        Returns:
            mapping_table: 多项式系数映射表列表
        """
        print(f"创建投影仪 {projector_id} 的映射表...")
        
        if len(unwrapped_phases) == 0:
            print("没有有效的相位数据")
            return []
        
        # 获取图像尺寸
        height, width = unwrapped_phases[0].shape
        print(f"图像尺寸: {height} x {width}")
        
        # 初始化映射表（多项式系数）
        mapping_table = []
        for degree in range(self.fit_degree + 1):
            mapping_table.append(np.full((height, width), np.nan, dtype=np.float32))
        
        # 准备数据
        num_positions = len(unwrapped_phases)
        phase_data = np.stack(unwrapped_phases, axis=2)  # (H, W, N)
        height_array = np.array(heights)
        
        print(f"开始逐像素拟合 ({height * width:,} 像素)...")
        
        # 逐像素拟合
        valid_count = 0
        total_pixels = height * width
        
        with tqdm(total=total_pixels, desc="拟合进度") as pbar:
            for i in range(height):
                for j in range(width):
                    # 获取该像素在所有位置的相位值
                    pixel_phases = phase_data[i, j, :]
                    
                    # 检查有效数据
                    valid_mask = np.isfinite(pixel_phases)
                    valid_count_pixel = np.sum(valid_mask)
                    
                    if valid_count_pixel >= self.fit_degree + 2:  # 至少需要比拟合度数多2个点
                        try:
                            # 多项式拟合
                            valid_phases = pixel_phases[valid_mask]
                            valid_heights = height_array[valid_mask]
                            
                            coeffs = np.polyfit(valid_phases, valid_heights, self.fit_degree)
                            
                            # 存储系数（注意numpy的polyfit返回的系数是从高次到低次）
                            for degree in range(self.fit_degree + 1):
                                mapping_table[degree][i, j] = coeffs[self.fit_degree - degree]
                            
                            valid_count += 1
                            
                        except Exception as e:
                            # 拟合失败，保持NaN
                            pass
                    
                    pbar.update(1)
        
        print(f"拟合完成: {valid_count:,}/{total_pixels:,} 像素有效 ({valid_count/total_pixels:.1%})")
        
        return mapping_table

    def save_mapping_table(self, mapping_table: List[np.ndarray], projector_id: int, output_dir: str):
        """
        保存映射表到TIFF文件
        Args:
            mapping_table: 映射表
            projector_id: 投影仪ID
            output_dir: 输出目录
        """
        mapping_dir = Path(output_dir) / "mapping_tables"
        mapping_dir.mkdir(parents=True, exist_ok=True)

        for degree, coeff_map in enumerate(mapping_table):
            filename = f"DLP-{projector_id}-{degree}.tiff"
            filepath = mapping_dir / filename

            # 保存为32位浮点TIFF
            cv2.imwrite(str(filepath), coeff_map.astype(np.float32))
            print(f"  保存系数图 {degree}: {filepath}")

    def evaluate_calibration_quality(self, unwrapped_phases: List[np.ndarray],
                                   heights: List[float],
                                   mapping_table: List[np.ndarray]) -> Dict:
        """
        评估标定质量
        Args:
            unwrapped_phases: 展开相位图列表
            heights: 高度列表
            mapping_table: 映射表
        Returns:
            quality_metrics: 质量指标字典
        """
        print("评估标定质量...")

        if len(mapping_table) == 0:
            return {"error": "映射表为空"}

        # 预测高度并计算误差
        all_errors = []

        for i, (phase_map, true_height) in enumerate(zip(unwrapped_phases, heights)):
            # 使用映射表预测高度
            predicted_height = np.zeros_like(phase_map, dtype=np.float64)

            for degree, coeff_map in enumerate(mapping_table):
                predicted_height += coeff_map * np.power(phase_map, degree)

            # 计算误差
            error = np.abs(predicted_height - true_height)
            valid_mask = np.isfinite(error)

            if np.sum(valid_mask) > 0:
                all_errors.extend(error[valid_mask])

        if len(all_errors) == 0:
            return {"error": "没有有效的误差数据"}

        all_errors = np.array(all_errors)

        # 计算质量指标
        rmse = np.sqrt(np.mean(all_errors**2))
        mean_error = np.mean(all_errors)
        std_error = np.std(all_errors)
        max_error = np.max(all_errors)

        # 质量等级评估
        if rmse < 0.5:
            quality_grade = "Excellent"
        elif rmse < 1.0:
            quality_grade = "Good"
        elif rmse < 2.0:
            quality_grade = "Fair"
        else:
            quality_grade = "Poor"

        quality_metrics = {
            "rmse": rmse,
            "mean_error": mean_error,
            "std_error": std_error,
            "max_error": max_error,
            "quality_grade": quality_grade,
            "valid_pixels": len(all_errors)
        }

        print(f"  RMSE: {rmse:.4f} mm")
        print(f"  平均误差: {mean_error:.4f} mm")
        print(f"  标准差: {std_error:.4f} mm")
        print(f"  最大误差: {max_error:.4f} mm")
        print(f"  质量等级: {quality_grade}")

        return quality_metrics

    def calibrate_single_projector(self, projector_id: int) -> Dict:
        """
        标定单个投影仪
        Args:
            projector_id: 投影仪ID
        Returns:
            calibration_result: 标定结果字典
        """
        print(f"\n=== 开始标定投影仪 {projector_id} ===")
        print(f"使用双频相位展开算法 (512 + 8 像素周期)")

        # 收集展开相位数据
        unwrapped_phases, heights = self.collect_unwrapped_phases(projector_id)

        if len(unwrapped_phases) == 0:
            print(f"投影仪 {projector_id} 没有有效数据，跳过标定")
            return None

        # 创建映射表
        mapping_table = self.create_mapping_table(unwrapped_phases, heights, projector_id)

        if len(mapping_table) == 0:
            print(f"投影仪 {projector_id} 映射表创建失败")
            return None

        # 评估质量
        quality_metrics = self.evaluate_calibration_quality(unwrapped_phases, heights, mapping_table)

        # 存储结果
        calibration_result = {
            "projector_id": projector_id,
            "projector_name": self.projector_configs[projector_id]["name"],
            "method": "dual_frequency",
            "frequencies": [512, 8],
            "fit_degree": self.fit_degree,
            "num_positions": len(unwrapped_phases),
            "height_range": [min(heights), max(heights)] if heights else [0, 0],
            "quality_metrics": quality_metrics,
            "calibration_time": datetime.now().isoformat()
        }

        self.calibration_results[projector_id] = calibration_result
        self.mapping_tables[projector_id] = mapping_table

        print(f"投影仪 {projector_id} 标定完成")
        return calibration_result

    def calibrate_all_projectors(self, output_dir: str = "dual_freq_calibration_results"):
        """
        标定所有投影仪
        Args:
            output_dir: 输出目录
        """
        print("=== 开始双频标定系统 ===")
        print(f"输出目录: {output_dir}")

        # 创建输出目录
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        # 标定所有投影仪
        successful_calibrations = 0

        for projector_id in range(4):
            result = self.calibrate_single_projector(projector_id)

            if result is not None:
                # 保存映射表
                self.save_mapping_table(self.mapping_tables[projector_id], projector_id, output_dir)
                successful_calibrations += 1

        # 保存完整结果
        self.save_calibration_results(output_dir)

        print(f"\n=== 标定完成 ===")
        print(f"成功标定投影仪数量: {successful_calibrations}/4")
        print(f"结果保存在: {output_dir}")

    def save_calibration_results(self, output_dir: str):
        """保存标定结果"""
        output_path = Path(output_dir)

        # 保存pickle格式的完整结果
        with open(output_path / "calibration_results.pkl", "wb") as f:
            pickle.dump({
                "calibration_results": self.calibration_results,
                "mapping_tables": self.mapping_tables,
                "calibration_params": {
                    "method": "dual_frequency",
                    "frequencies": [512, 8],
                    "fit_degree": self.fit_degree,
                    "height_step": self.height_step,
                    "num_positions": self.num_positions
                }
            }, f)

        # 保存JSON格式的质量报告
        quality_report = {
            "calibration_time": datetime.now().isoformat(),
            "method": "dual_frequency",
            "frequencies": [512, 8],
            "projectors": {}
        }

        total_rmse = 0
        total_mean_error = 0
        valid_projectors = 0

        for proj_id, result in self.calibration_results.items():
            quality_report["projectors"][proj_id] = result

            if "quality_metrics" in result and "rmse" in result["quality_metrics"]:
                total_rmse += result["quality_metrics"]["rmse"]
                total_mean_error += result["quality_metrics"]["mean_error"]
                valid_projectors += 1

        if valid_projectors > 0:
            quality_report["overall"] = {
                "average_rmse": total_rmse / valid_projectors,
                "average_mean_error": total_mean_error / valid_projectors,
                "successful_projectors": valid_projectors
            }

        with open(output_path / "quality_report.json", "w", encoding="utf-8") as f:
            json.dump(quality_report, f, indent=2, ensure_ascii=False)

        # 保存详细报告
        self.save_detailed_report(output_dir)

        print(f"标定结果已保存:")
        print(f"  - calibration_results.pkl: 完整标定数据")
        print(f"  - quality_report.json: 质量报告")
        print(f"  - detailed_report.txt: 详细报告")
        print(f"  - mapping_tables/: 映射表文件")

    def save_detailed_report(self, output_dir: str):
        """保存详细报告"""
        report_path = Path(output_dir) / "detailed_calibration_report.txt"

        with open(report_path, "w", encoding="utf-8") as f:
            f.write("双频相位-高度标定详细报告\n")
            f.write("=" * 70 + "\n\n")

            f.write("标定概述:\n")
            f.write(f"- 标定时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"- 相位展开方法: 双频相位展开 (512 + 8 像素周期)\n")
            f.write(f"- 标定方法: 多项式映射\n")
            f.write(f"- 成功标定投影仪数量: {len(self.calibration_results)}\n")
            f.write(f"- 拟合度数: {self.fit_degree}\n")
            f.write(f"- 高度范围: 0-{(self.num_positions-1)*self.height_step}mm (步长{self.height_step}mm)\n\n")

            # 整体质量评估
            if self.calibration_results:
                total_rmse = sum(r["quality_metrics"]["rmse"] for r in self.calibration_results.values()
                               if "quality_metrics" in r and "rmse" in r["quality_metrics"])
                total_mean_error = sum(r["quality_metrics"]["mean_error"] for r in self.calibration_results.values()
                                     if "quality_metrics" in r and "mean_error" in r["quality_metrics"])
                valid_count = len([r for r in self.calibration_results.values()
                                 if "quality_metrics" in r and "rmse" in r["quality_metrics"]])

                if valid_count > 0:
                    avg_rmse = total_rmse / valid_count
                    avg_mean_error = total_mean_error / valid_count

                    f.write("整体质量评估:\n")
                    if avg_rmse < 0.5:
                        f.write("- 整体质量: Excellent\n")
                    elif avg_rmse < 1.0:
                        f.write("- 整体质量: Good\n")
                    else:
                        f.write("- 整体质量: Fair\n")
                    f.write(f"- 平均RMSE: {avg_rmse:.4f} mm\n")
                    f.write(f"- 平均误差: {avg_mean_error:.4f} mm\n\n")

            f.write("各投影仪详细信息:\n")
            f.write("-" * 70 + "\n\n")

            for proj_id, result in self.calibration_results.items():
                f.write(f"投影仪 {proj_id} ({result['projector_name']}):\n")
                f.write(f"  相位展开方法: 双频 (512 + 8 像素周期)\n")
                f.write(f"  拟合度数: {result['fit_degree']}\n")
                f.write(f"  标定位置数: {result['num_positions']}\n")

                if "quality_metrics" in result:
                    metrics = result["quality_metrics"]
                    f.write(f"  性能指标:\n")
                    f.write(f"    RMSE: {metrics['rmse']:.4f} mm\n")
                    f.write(f"    平均误差: {metrics['mean_error']:.4f} mm\n")
                    f.write(f"    标准差: {metrics['std_error']:.4f} mm\n")
                    f.write(f"    最大误差: {metrics['max_error']:.4f} mm\n")
                    f.write(f"  数据统计:\n")
                    f.write(f"    有效像素总数: {metrics['valid_pixels']:,}\n")
                    f.write(f"  质量评估: {metrics['quality_grade']}\n")

                f.write("\n")

            f.write("使用说明:\n")
            f.write("1. 映射表已保存在 mapping_tables/ 文件夹中\n")
            f.write("2. 每个投影仪有 3 个系数图（DLP-{projector_id}-{degree}.tiff）\n")
            f.write("3. 使用多项式公式进行高度预测: h = c0 + c1*φ + c2*φ² + c3*φ³\n")
            f.write("4. 双频展开使用512像素周期展开8像素周期相位\n")
            f.write("5. 相比四频系统，图像数量减少50%（每个投影仪10张图像）\n\n")

            f.write("建议:\n")
            avg_rmse = sum(r["quality_metrics"]["rmse"] for r in self.calibration_results.values()
                          if "quality_metrics" in r and "rmse" in r["quality_metrics"]) / len(self.calibration_results)
            if avg_rmse < 0.5:
                f.write("1. 标定质量优秀，可以用于高精度测量\n")
                f.write("2. 双频系统在保持精度的同时提高了效率\n")
            elif avg_rmse < 1.0:
                f.write("1. 标定质量良好，适合一般精度要求的测量\n")
                f.write("2. 可考虑优化图像质量或调制度阈值\n")
            else:
                f.write("1. 标定质量需要改进\n")
                f.write("2. 建议检查图像质量、光照条件或相位展开参数\n")


if __name__ == "__main__":
    # 创建标定器
    calibrator = DualFrequencyCalibrator("标定数据")

    # 执行标定
    calibrator.calibrate_all_projectors("dual_freq_calibration_results")
