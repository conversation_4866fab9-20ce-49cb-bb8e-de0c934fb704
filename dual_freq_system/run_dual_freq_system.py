"""
run_dual_freq_system.py
========================
双频系统运行脚本

功能：
1. 测试双频相位展开算法
2. 执行双频标定
3. 执行双频融合重建
4. 性能对比分析

作者：AI Assistant
日期：2025-07-29
"""

import sys
import os
from pathlib import Path
import argparse
import time

# 添加父目录到路径，以便导入四频系统的模块
sys.path.append(str(Path(__file__).parent.parent))

from dual_freq_phase_unwrap import DualFrequencyUnwrapper
from dual_freq_calibration import DualFrequencyCalibrator
from dual_freq_fusion import DualFrequencyFusion


def test_phase_unwrapping():
    """测试双频相位展开算法"""
    print("=== 测试双频相位展开算法 ===")
    
    # 创建展开器
    unwrapper = DualFrequencyUnwrapper(
        period_low=512.0,
        period_high=8.0,
        modulation_threshold=0.1
    )
    
    # 运行内置测试
    print("运行内置测试...")
    try:
        import numpy as np
        
        # 创建测试数据
        x = np.linspace(0, 1000, 1001)
        y = np.linspace(0, 1000, 1001)
        X, Y = np.meshgrid(x, y)
        
        # 生成测试图像
        frames_512 = np.stack([
            np.cos(2.0 * np.pi * X / 512.0 + s * 2.0 * np.pi / 5.0) + 0.1 * np.random.randn(*X.shape)
            for s in range(5)
        ], axis=-1)
        
        frames_8 = np.stack([
            np.cos(2.0 * np.pi * X / 8.0 + s * 2.0 * np.pi / 5.0) + 0.1 * np.random.randn(*X.shape)
            for s in range(5)
        ], axis=-1)
        
        # 执行相位展开
        start_time = time.time()
        unwrapped_phase, info = unwrapper.unwrap_dual_frequency(frames_512, frames_8)
        end_time = time.time()
        
        # 打印结果
        unwrapper.print_info(info)
        print(f"处理时间: {end_time - start_time:.2f} 秒")
        print("双频相位展开测试完成\n")
        
        return True
        
    except Exception as e:
        print(f"双频相位展开测试失败: {e}")
        return False


def run_calibration(calibration_data_dir: str = "标定数据", 
                   output_dir: str = "dual_freq_calibration_results"):
    """执行双频标定"""
    print("=== 执行双频标定 ===")
    
    # 检查标定数据目录
    if not Path(calibration_data_dir).exists():
        print(f"错误: 标定数据目录不存在: {calibration_data_dir}")
        return False
    
    try:
        # 创建标定器
        calibrator = DualFrequencyCalibrator(calibration_data_dir)
        
        # 执行标定
        start_time = time.time()
        calibrator.calibrate_all_projectors(output_dir)
        end_time = time.time()
        
        print(f"标定完成，用时: {end_time - start_time:.2f} 秒")
        print(f"结果保存在: {output_dir}\n")
        
        return True
        
    except Exception as e:
        print(f"双频标定失败: {e}")
        return False


def run_fusion(test_data_dir: str = "pcb", 
               calibration_dir: str = "dual_freq_calibration_results",
               output_dir: str = "dual_freq_fusion_results"):
    """执行双频融合重建"""
    print("=== 执行双频融合重建 ===")
    
    # 检查输入目录
    if not Path(test_data_dir).exists():
        print(f"错误: 测试数据目录不存在: {test_data_dir}")
        return False
    
    if not Path(calibration_dir).exists():
        print(f"错误: 标定结果目录不存在: {calibration_dir}")
        return False
    
    try:
        # 创建融合器
        fusion_engine = DualFrequencyFusion(
            calibration_results_dir=calibration_dir,
            outlier_threshold=3.0,
            min_valid_ratio=0.1
        )
        
        # 执行融合
        start_time = time.time()
        fusion_engine.process_all_test_folders(test_data_dir, output_dir)
        end_time = time.time()
        
        print(f"融合重建完成，用时: {end_time - start_time:.2f} 秒")
        print(f"结果保存在: {output_dir}\n")
        
        return True
        
    except Exception as e:
        print(f"双频融合重建失败: {e}")
        return False


def compare_with_four_freq():
    """与四频系统性能对比"""
    print("=== 双频与四频系统对比 ===")
    
    print("理论对比:")
    print("双频系统 (512 + 8 像素周期):")
    print("  - 图像数量: 10张/投影仪 (50%减少)")
    print("  - 采集时间: 减少50%")
    print("  - 处理复杂度: 降低")
    print("  - 精度: 保持高精度 (8像素周期)")
    print("  - 稳定性: 良好 (512像素周期提供稳定展开)")
    
    print("\n四频系统 (1080 + 512 + 64 + 8 像素周期):")
    print("  - 图像数量: 20张/投影仪")
    print("  - 采集时间: 基准")
    print("  - 处理复杂度: 高")
    print("  - 精度: 最高精度")
    print("  - 稳定性: 最高稳定性")
    
    print("\n推荐使用场景:")
    print("双频系统:")
    print("  - 实时或快速测量需求")
    print("  - 精度要求适中的应用")
    print("  - 存储空间有限的场合")
    
    print("四频系统:")
    print("  - 最高精度要求的应用")
    print("  - 复杂表面测量")
    print("  - 研究和开发用途")


def main():
    parser = argparse.ArgumentParser(description="双频系统运行脚本")
    parser.add_argument("--mode", type=str, default="all", 
                       choices=["test", "calibrate", "fusion", "compare", "all"],
                       help="运行模式")
    parser.add_argument("--calibration_data", type=str, default="标定数据", 
                       help="标定数据目录")
    parser.add_argument("--test_data", type=str, default="pcb", 
                       help="测试数据目录")
    parser.add_argument("--calibration_output", type=str, default="dual_freq_calibration_results",
                       help="标定结果输出目录")
    parser.add_argument("--fusion_output", type=str, default="dual_freq_fusion_results",
                       help="融合结果输出目录")
    
    args = parser.parse_args()
    
    print("双频相位测量系统")
    print("=" * 50)
    print(f"运行模式: {args.mode}")
    print(f"标定数据: {args.calibration_data}")
    print(f"测试数据: {args.test_data}")
    print("=" * 50)
    
    success_count = 0
    total_count = 0
    
    if args.mode in ["test", "all"]:
        print("\n1. 测试相位展开算法")
        total_count += 1
        if test_phase_unwrapping():
            success_count += 1
    
    if args.mode in ["calibrate", "all"]:
        print("\n2. 执行标定")
        total_count += 1
        if run_calibration(args.calibration_data, args.calibration_output):
            success_count += 1
    
    if args.mode in ["fusion", "all"]:
        print("\n3. 执行融合重建")
        total_count += 1
        if run_fusion(args.test_data, args.calibration_output, args.fusion_output):
            success_count += 1
    
    if args.mode in ["compare", "all"]:
        print("\n4. 性能对比")
        compare_with_four_freq()
    
    print("\n" + "=" * 50)
    print("双频系统运行完成")
    if total_count > 0:
        print(f"成功: {success_count}/{total_count}")
        if success_count == total_count:
            print("所有任务执行成功！")
        else:
            print("部分任务执行失败，请检查错误信息")
    print("=" * 50)


if __name__ == "__main__":
    main()
