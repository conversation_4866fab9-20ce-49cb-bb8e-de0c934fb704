"""
dual_freq_phase_unwrap.py
==========================
基于512和8像素周期的双频五步相移相位展开算法

采用512像素周期作为低频（粗展开）
采用8像素周期作为高频（精细测量）

算法流程：
1. 使用五步相移算法计算两个频率的包裹相位
2. 使用512像素周期的相位展开8像素周期的相位
3. 返回展开后的8像素周期绝对相位

作者：AI Assistant
日期：2025-07-29
"""

from __future__ import annotations

import numpy as np
from typing import List, Tuple, Dict, Optional
import cv2


def five_step_phase(frames: np.ndarray) -> np.ndarray:
    """根据五步相移算法计算包裹相位。
    
    参数
    ----
    frames : ndarray
        ndarray[..., 5]，最后一维为 5 个相移帧 (0, 72°, 144°, 216°, 288°)。
    
    返回
    ----
    ndarray
        与 frames[...,0] 同形状的包裹相位，取值范围 [-π, π)。
    """
    if frames.shape[-1] != 5:
        raise ValueError("five_step_phase: 输入的最后一维必须为 5")
    
    # 依次取出五帧
    I0, I1, I2, I3, I4 = [frames[..., i] for i in range(5)]
    
    # 五步相移解析公式 (等相位步长情况)
    A = 2.0 * (I1 - I3)
    B = I0 - 2.0 * I2 + I4
    
    wrapped_phase = np.arctan2(A, B)
    return wrapped_phase


def calculate_modulation(frames: np.ndarray) -> np.ndarray:
    """计算调制度（条纹对比度）
    
    参数
    ----
    frames : ndarray
        ndarray[..., 5]，最后一维为 5 个相移帧
    
    返回
    ----
    ndarray
        调制度图像
    """
    if frames.shape[-1] != 5:
        raise ValueError("calculate_modulation: 输入的最后一维必须为 5")
    
    # 依次取出五帧
    I0, I1, I2, I3, I4 = [frames[..., i] for i in range(5)]
    
    # 计算调制度
    A = 2.0 * (I1 - I3)
    B = I0 - 2.0 * I2 + I4
    DC = (I0 + I1 + I2 + I3 + I4) / 5.0
    
    modulation = np.sqrt(A**2 + B**2) / (2.0 * DC + 1e-10)
    
    return modulation


def dual_frequency_unwrap(phase_low: np.ndarray, 
                         phase_high: np.ndarray,
                         period_low: float = 512.0,
                         period_high: float = 8.0) -> np.ndarray:
    """双频相位展开
    
    参数
    ----
    phase_low : ndarray
        低频包裹相位 (512像素周期)
    phase_high : ndarray  
        高频包裹相位 (8像素周期)
    period_low : float
        低频周期，默认512
    period_high : float
        高频周期，默认8
    
    返回
    ----
    ndarray
        展开后的高频绝对相位
    """
    # 计算周期比
    ratio = period_low / period_high  # 512/8 = 64
    
    # 预测高频相位值
    phi_pred = phase_low * ratio
    
    # 计算需要添加的 2π 整数倍，使其与预测值最接近
    delta = phi_pred - phase_high
    m = np.round(delta / (2.0 * np.pi))
    
    # 展开高频相位
    unwrapped_phase = phase_high + 2.0 * np.pi * m
    
    return unwrapped_phase


class DualFrequencyUnwrapper:
    """双频相位展开器"""
    
    def __init__(self, 
                 period_low: float = 512.0,
                 period_high: float = 8.0,
                 modulation_threshold: float = 0.1):
        """
        初始化双频展开器
        
        参数:
            period_low: 低频周期（像素）
            period_high: 高频周期（像素）
            modulation_threshold: 调制度阈值
        """
        self.period_low = period_low
        self.period_high = period_high
        self.modulation_threshold = modulation_threshold
        
        print(f"双频展开器初始化:")
        print(f"  低频周期: {period_low} 像素")
        print(f"  高频周期: {period_high} 像素")
        print(f"  周期比: {period_low/period_high:.1f}")
        print(f"  调制度阈值: {modulation_threshold}")
    
    def unwrap_dual_frequency(self, 
                             frames_low: np.ndarray,
                             frames_high: np.ndarray) -> Tuple[np.ndarray, Dict]:
        """
        双频相位展开主函数
        
        参数:
            frames_low: 低频图像组 (H, W, 5)
            frames_high: 高频图像组 (H, W, 5)
            
        返回:
            unwrapped_phase: 展开的绝对相位
            info: 处理信息
        """
        print("开始双频相位展开...")
        
        # 1. 计算包裹相位
        print("1. 计算包裹相位...")
        phase_low = five_step_phase(frames_low)
        phase_high = five_step_phase(frames_high)
        
        # 2. 计算调制度
        print("2. 计算调制度...")
        mod_low = calculate_modulation(frames_low)
        mod_high = calculate_modulation(frames_high)
        
        # 3. 质量评估
        print("3. 质量评估...")
        valid_mask_low = mod_low > self.modulation_threshold
        valid_mask_high = mod_high > self.modulation_threshold
        combined_mask = valid_mask_low & valid_mask_high
        
        valid_ratio_low = np.sum(valid_mask_low) / valid_mask_low.size
        valid_ratio_high = np.sum(valid_mask_high) / valid_mask_high.size
        valid_ratio_combined = np.sum(combined_mask) / combined_mask.size
        
        print(f"   低频有效像素比例: {valid_ratio_low:.1%}")
        print(f"   高频有效像素比例: {valid_ratio_high:.1%}")
        print(f"   组合有效像素比例: {valid_ratio_combined:.1%}")
        
        # 4. 相位展开
        print("4. 执行相位展开...")
        unwrapped_phase = dual_frequency_unwrap(
            phase_low, phase_high, 
            self.period_low, self.period_high
        )
        
        # 5. 应用质量掩码
        unwrapped_phase[~combined_mask] = np.nan
        
        # 6. 统计信息
        phase_range = [np.nanmin(unwrapped_phase), np.nanmax(unwrapped_phase)]
        
        info = {
            "method": "dual_frequency",
            "periods": [self.period_low, self.period_high],
            "phase_range": phase_range,
            "valid_ratio_low": valid_ratio_low,
            "valid_ratio_high": valid_ratio_high,
            "valid_ratio_combined": valid_ratio_combined,
            "modulation_threshold": self.modulation_threshold,
            "unwrapped_pixels": np.sum(np.isfinite(unwrapped_phase))
        }
        
        print(f"5. 展开完成:")
        print(f"   相位范围: [{phase_range[0]:.3f}, {phase_range[1]:.3f}] 弧度")
        print(f"   有效像素: {info['unwrapped_pixels']:,}")
        
        return unwrapped_phase, info
    
    def print_info(self, info: Dict):
        """打印处理信息"""
        print("\n=== 双频相位展开信息 ===")
        print(f"方法: {info['method']}")
        print(f"周期: {info['periods']}")
        print(f"相位范围: [{info['phase_range'][0]:.3f}, {info['phase_range'][1]:.3f}] 弧度")
        print(f"低频有效比例: {info['valid_ratio_low']:.1%}")
        print(f"高频有效比例: {info['valid_ratio_high']:.1%}")
        print(f"组合有效比例: {info['valid_ratio_combined']:.1%}")
        print(f"调制度阈值: {info['modulation_threshold']}")
        print(f"有效像素数: {info['unwrapped_pixels']:,}")
        print("========================\n")


# 测试函数
if __name__ == "__main__":
    print("双频相位展开算法测试")
    
    # 创建测试数据
    x = np.linspace(0, 2000, 2001)
    y = np.linspace(0, 2000, 2001)
    X, Y = np.meshgrid(x, y)
    
    # 真实相位（基于8像素周期）
    ground_truth = 2.0 * np.pi * X / 8.0
    
    # 生成测试图像
    frames_512 = np.stack([
        np.cos(2.0 * np.pi * X / 512.0 + s * 2.0 * np.pi / 5.0)
        for s in range(5)
    ], axis=-1)
    
    frames_8 = np.stack([
        np.cos(2.0 * np.pi * X / 8.0 + s * 2.0 * np.pi / 5.0)
        for s in range(5)
    ], axis=-1)
    
    # 创建展开器并测试
    unwrapper = DualFrequencyUnwrapper()
    unwrapped_phase, info = unwrapper.unwrap_dual_frequency(frames_512, frames_8)
    
    # 计算误差
    error = np.abs(unwrapped_phase - ground_truth)
    valid_mask = np.isfinite(error)
    
    if np.sum(valid_mask) > 0:
        mean_error = np.mean(error[valid_mask])
        max_error = np.max(error[valid_mask])
        print(f"\n测试结果:")
        print(f"平均误差: {mean_error:.6f} 弧度")
        print(f"最大误差: {max_error:.6f} 弧度")
        print(f"有效像素比例: {np.sum(valid_mask)/valid_mask.size:.1%}")
    else:
        print("测试失败：没有有效像素")
