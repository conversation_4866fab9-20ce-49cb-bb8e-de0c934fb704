"""
dual_freq_fusion.py
===================
基于双频标定结果的重建融合系统

参考最终系统_四频分析中的fusion_with_final_calibration.py
使用双频相位展开和标定结果进行重建融合

功能：
1. 读取测试文件夹中的512和8周期数据
2. 使用双频相位展开获得绝对相位
3. 应用标定结果进行高度重建
4. 多投影仪数据融合
5. 输出点云文件

作者：AI Assistant
日期：2025-07-29
"""

import numpy as np
import cv2
from pathlib import Path
from typing import List, Tuple, Dict, Optional
import pickle
import argparse
from scipy import stats

# 导入双频相位展开
from dual_freq_phase_unwrap import DualFrequencyUnwrapper


class DualFrequencyFusion:
    """基于双频标定结果的融合重建器"""
    
    def __init__(self, 
                 calibration_results_dir: str = "dual_freq_calibration_results",
                 outlier_threshold: float = 3.0,
                 min_valid_ratio: float = 0.1):
        """
        初始化融合器
        
        参数:
            calibration_results_dir: 标定结果目录
            outlier_threshold: 异常值检测阈值（标准差倍数）
            min_valid_ratio: 最小有效数据比例
        """
        self.calibration_results_dir = Path(calibration_results_dir)
        self.outlier_threshold = outlier_threshold
        self.min_valid_ratio = min_valid_ratio
        
        # 投影仪配置
        self.projector_configs = {
            0: {"name": "Projector_0", "image_range": (1, 20)},
            1: {"name": "Projector_1", "image_range": (21, 40)},
            2: {"name": "Projector_2", "image_range": (41, 60)},
            3: {"name": "Projector_3", "image_range": (61, 80)}
        }
        
        # 双频配置
        self.frequency_configs = {
            8: {"image_indices": [1, 2, 3, 4, 5], "name": "8px_period"},      
            512: {"image_indices": [11, 12, 13, 14, 15], "name": "512px_period"} 
        }
        
        # 创建双频相位展开器
        self.dual_unwrapper = DualFrequencyUnwrapper(
            period_low=512.0,
            period_high=8.0,
            modulation_threshold=0.1
        )
        
        # 加载标定结果
        self.load_calibration_results()
        
        print(f"双频融合器初始化完成")
        print(f"标定结果目录: {calibration_results_dir}")
        print(f"异常值阈值: {outlier_threshold}σ")
        print(f"最小有效比例: {min_valid_ratio}")
    
    def load_calibration_results(self):
        """加载标定结果"""
        print(f"加载标定结果从: {self.calibration_results_dir}")
        
        # 加载映射表
        self.mapping_tables = {}
        mapping_dir = self.calibration_results_dir / "mapping_tables"
        
        for projector_id in range(4):
            # 加载多项式系数映射表
            c0_file = mapping_dir / f"DLP-{projector_id}-0.tiff"  # 常数项
            c1_file = mapping_dir / f"DLP-{projector_id}-1.tiff"  # 一次项
            c2_file = mapping_dir / f"DLP-{projector_id}-2.tiff"  # 二次项
            c3_file = mapping_dir / f"DLP-{projector_id}-3.tiff"  # 三次项
            
            if not all([c0_file.exists(), c1_file.exists(), c2_file.exists()]):
                print(f"  警告: 投影仪 {projector_id} 的映射表文件不完整，跳过")
                continue
            
            # 读取映射表
            c0 = cv2.imread(str(c0_file), cv2.IMREAD_ANYDEPTH | cv2.IMREAD_GRAYSCALE).astype(np.float32)
            c1 = cv2.imread(str(c1_file), cv2.IMREAD_ANYDEPTH | cv2.IMREAD_GRAYSCALE).astype(np.float32)
            c2 = cv2.imread(str(c2_file), cv2.IMREAD_ANYDEPTH | cv2.IMREAD_GRAYSCALE).astype(np.float32)
            
            # 检查是否有三次项
            c3 = None
            if c3_file.exists():
                c3 = cv2.imread(str(c3_file), cv2.IMREAD_ANYDEPTH | cv2.IMREAD_GRAYSCALE).astype(np.float32)
            
            self.mapping_tables[projector_id] = {
                'c0': c0,
                'c1': c1, 
                'c2': c2,
                'c3': c3
            }
            
            print(f"  投影仪 {projector_id}: 映射表加载完成 ({c0.shape})")
        
        print(f"成功加载 {len(self.mapping_tables)} 个投影仪的标定结果")
    
    def load_dual_frequency_images(self, test_folder: str, projector_id: int) -> Dict[int, List[np.ndarray]]:
        """
        加载指定投影仪的双频相移图像
        Args:
            test_folder: 测试文件夹路径
            projector_id: 投影仪ID (0-3)
        Returns:
            images_by_frequency: 按频率组织的图像字典
        """
        start_idx, _ = self.projector_configs[projector_id]["image_range"]
        images_by_frequency = {}
        
        for freq in [512, 8]:  # 只加载这两个频率
            freq_config = self.frequency_configs[freq]
            freq_images = []
            
            for img_idx in freq_config["image_indices"]:
                # 计算实际文件编号
                actual_idx = start_idx + img_idx - 1
                img_path = Path(test_folder) / f"{actual_idx:02d}.bmp"
                
                if not img_path.exists():
                    print(f"    警告: 图像文件不存在 {img_path}")
                    return None
                
                # 读取图像
                img = cv2.imread(str(img_path), cv2.IMREAD_GRAYSCALE)
                if img is None:
                    print(f"    错误: 无法读取图像 {img_path}")
                    return None
                
                freq_images.append(img.astype(np.float32))
            
            images_by_frequency[freq] = freq_images
        
        return images_by_frequency
    
    def phase_to_height(self, phase: np.ndarray, projector_id: int) -> np.ndarray:
        """使用多项式映射将相位转换为高度"""
        if projector_id not in self.mapping_tables:
            raise ValueError(f"投影仪 {projector_id} 的映射表未加载")
        
        # 获取映射系数
        coeffs = self.mapping_tables[projector_id]
        c0, c1, c2, c3 = coeffs['c0'], coeffs['c1'], coeffs['c2'], coeffs['c3']
        
        # 确保尺寸匹配
        if phase.shape != c0.shape:
            c0 = cv2.resize(c0, (phase.shape[1], phase.shape[0]), interpolation=cv2.INTER_LINEAR)
            c1 = cv2.resize(c1, (phase.shape[1], phase.shape[0]), interpolation=cv2.INTER_LINEAR)
            c2 = cv2.resize(c2, (phase.shape[1], phase.shape[0]), interpolation=cv2.INTER_LINEAR)
            if c3 is not None:
                c3 = cv2.resize(c3, (phase.shape[1], phase.shape[0]), interpolation=cv2.INTER_LINEAR)
        
        # 使用多项式计算高度: h = c0 + c1*φ + c2*φ² + c3*φ³
        height = c0 + c1 * phase + c2 * (phase ** 2)
        if c3 is not None:
            height += c3 * (phase ** 3)
        
        # 处理无效值
        valid_phase = np.isfinite(phase)
        valid_c0 = np.isfinite(c0)
        valid_c1 = np.isfinite(c1)
        valid_c2 = np.isfinite(c2)
        
        valid_mask = valid_phase & valid_c0 & valid_c1 & valid_c2
        if c3 is not None:
            valid_c3 = np.isfinite(c3)
            valid_mask = valid_mask & valid_c3
        
        # 在无效区域设为NaN
        height[~valid_mask] = np.nan
        
        return height
    
    def reconstruct_single_projector(self, test_dir: str, projector_id: int) -> Tuple[np.ndarray, Dict]:
        """重建单个投影仪的深度图"""
        print(f"重建投影仪 {projector_id}...")
        
        try:
            test_path = Path(test_dir)
            
            # 1. 加载图像数据
            images_by_frequency = self.load_dual_frequency_images(str(test_path), projector_id)
            
            if images_by_frequency is None:
                print(f"  投影仪 {projector_id}: 图像加载失败")
                return None, {"projector_id": projector_id, "error": "图像加载失败", "is_valid": False}
            
            # 2. 双频相位展开
            frames_512 = np.stack(images_by_frequency[512], axis=-1)  # (H, W, 5)
            frames_8 = np.stack(images_by_frequency[8], axis=-1)      # (H, W, 5)
            
            phase, phase_info = self.dual_unwrapper.unwrap_dual_frequency(frames_512, frames_8)
            
            # 3. 相位转高度
            depth_map = self.phase_to_height(phase, projector_id)
            
            # 4. 分析质量
            quality_info = self.analyze_depth_quality(depth_map, projector_id)
            quality_info["phase_info"] = phase_info
            
            print(f"  投影仪 {projector_id}: 有效比例 {quality_info['valid_ratio']:.1%}, "
                  f"质量分数 {quality_info['quality_score']:.3f}")
            
            return depth_map, quality_info
            
        except Exception as e:
            print(f"  投影仪 {projector_id} 重建失败: {e}")
            return None, {"projector_id": projector_id, "error": str(e), "is_valid": False}
    
    def analyze_depth_quality(self, depth_map: np.ndarray, projector_id: int) -> Dict:
        """分析深度图质量"""
        if depth_map is None:
            return {"projector_id": projector_id, "is_valid": False, "error": "深度图为空"}
        
        # 统计有效像素
        valid_mask = np.isfinite(depth_map)
        valid_ratio = np.sum(valid_mask) / valid_mask.size
        
        if valid_ratio < self.min_valid_ratio:
            return {
                "projector_id": projector_id,
                "is_valid": False,
                "valid_ratio": valid_ratio,
                "error": f"有效像素比例过低 ({valid_ratio:.1%})"
            }
        
        # 计算统计信息
        valid_depths = depth_map[valid_mask]
        depth_mean = np.mean(valid_depths)
        depth_std = np.std(valid_depths)
        depth_range = [np.min(valid_depths), np.max(valid_depths)]
        
        # 质量分数（基于有效比例和深度一致性）
        consistency_score = 1.0 / (1.0 + depth_std)  # 标准差越小，一致性越好
        quality_score = valid_ratio * consistency_score
        
        return {
            "projector_id": projector_id,
            "is_valid": True,
            "valid_ratio": valid_ratio,
            "depth_mean": depth_mean,
            "depth_std": depth_std,
            "depth_range": depth_range,
            "quality_score": quality_score,
            "valid_pixels": np.sum(valid_mask)
        }

    def adaptive_fusion(self, depth_maps: List[np.ndarray], quality_infos: List[Dict]) -> Tuple[np.ndarray, Dict]:
        """自适应融合多个投影仪的深度图"""
        print("开始自适应融合...")

        # 过滤有效的深度图
        valid_data = []
        for depth_map, quality_info in zip(depth_maps, quality_infos):
            if depth_map is not None and quality_info.get("is_valid", False):
                valid_data.append((depth_map, quality_info))

        if len(valid_data) == 0:
            print("  没有有效的深度图数据")
            return None, {"error": "没有有效数据"}

        print(f"  有效投影仪数量: {len(valid_data)}")

        if len(valid_data) == 1:
            # 只有一个有效投影仪
            depth_map, quality_info = valid_data[0]
            print(f"  使用单个投影仪 {quality_info['projector_id']} 的数据")

            fusion_info = {
                "method": "single_projector",
                "used_projectors": [quality_info['projector_id']],
                "fusion_ratio": quality_info['valid_ratio'],
                "quality_score": quality_info['quality_score']
            }

            return depth_map.copy(), fusion_info

        # 多投影仪融合
        return self.multi_projector_fusion(valid_data)

    def multi_projector_fusion(self, valid_data: List[Tuple[np.ndarray, Dict]]) -> Tuple[np.ndarray, Dict]:
        """多投影仪数据融合"""
        print(f"  执行多投影仪融合 ({len(valid_data)} 个投影仪)")

        # 获取图像尺寸
        height, width = valid_data[0][0].shape

        # 初始化融合结果
        fused_depth = np.full((height, width), np.nan)
        fusion_weights = np.zeros((height, width))

        # 收集所有有效深度值
        all_depths = []
        all_weights = []
        all_masks = []

        for depth_map, quality_info in valid_data:
            valid_mask = np.isfinite(depth_map)
            weight = quality_info['quality_score']

            all_depths.append(depth_map)
            all_weights.append(weight)
            all_masks.append(valid_mask)

            print(f"    投影仪 {quality_info['projector_id']}: 权重 {weight:.3f}, "
                  f"有效像素 {np.sum(valid_mask):,}")

        # 逐像素融合
        used_projectors = []
        outlier_count = 0

        for i in range(height):
            for j in range(width):
                # 收集该像素位置的所有有效深度值
                pixel_depths = []
                pixel_weights = []
                pixel_projectors = []

                for k, (depth_map, quality_info) in enumerate(valid_data):
                    if all_masks[k][i, j]:  # 该投影仪在此像素有效
                        pixel_depths.append(depth_map[i, j])
                        pixel_weights.append(all_weights[k])
                        pixel_projectors.append(quality_info['projector_id'])

                if len(pixel_depths) == 0:
                    continue
                elif len(pixel_depths) == 1:
                    # 只有一个有效值
                    fused_depth[i, j] = pixel_depths[0]
                    fusion_weights[i, j] = pixel_weights[0]
                    if pixel_projectors[0] not in used_projectors:
                        used_projectors.append(pixel_projectors[0])
                else:
                    # 多个有效值，需要融合
                    pixel_depths = np.array(pixel_depths)
                    pixel_weights = np.array(pixel_weights)

                    # 异常值检测
                    if len(pixel_depths) >= 3:
                        z_scores = np.abs(stats.zscore(pixel_depths))
                        outlier_mask = z_scores > self.outlier_threshold

                        if np.sum(~outlier_mask) >= 2:  # 至少保留2个值
                            pixel_depths = pixel_depths[~outlier_mask]
                            pixel_weights = pixel_weights[~outlier_mask]
                            pixel_projectors = [p for p, is_outlier in zip(pixel_projectors, outlier_mask) if not is_outlier]
                            outlier_count += np.sum(outlier_mask)

                    # 加权平均融合
                    if len(pixel_depths) > 0:
                        weighted_depth = np.average(pixel_depths, weights=pixel_weights)
                        fused_depth[i, j] = weighted_depth
                        fusion_weights[i, j] = np.sum(pixel_weights)

                        for proj_id in pixel_projectors:
                            if proj_id not in used_projectors:
                                used_projectors.append(proj_id)

        # 统计融合结果
        valid_fused = np.isfinite(fused_depth)
        fusion_ratio = np.sum(valid_fused) / valid_fused.size

        fusion_info = {
            "method": "multi_projector_weighted",
            "used_projectors": sorted(used_projectors),
            "fusion_ratio": fusion_ratio,
            "outlier_pixels": outlier_count,
            "fused_pixels": np.sum(valid_fused),
            "average_weight": np.mean(fusion_weights[valid_fused]) if np.sum(valid_fused) > 0 else 0
        }

        print(f"    融合完成: 有效像素 {np.sum(valid_fused):,} ({fusion_ratio:.1%})")
        print(f"    使用投影仪: {used_projectors}")
        print(f"    异常像素: {outlier_count:,}")

        return fused_depth, fusion_info

    def save_point_cloud(self, depth_map: np.ndarray, output_path: str,
                        pixel_size: float = 0.01):  # 10微米像素
        """保存点云到PLY文件"""
        print(f"保存点云到: {output_path}")

        valid_mask = np.isfinite(depth_map)
        valid_count = np.sum(valid_mask)

        if valid_count == 0:
            print("  错误: 没有有效的深度数据")
            return

        # 生成坐标
        height, width = depth_map.shape
        y_coords, x_coords = np.mgrid[0:height, 0:width]

        # 转换为物理坐标（毫米）
        x_coords = x_coords * pixel_size  # mm
        y_coords = y_coords * pixel_size  # mm
        z_coords = depth_map  # mm

        # 提取有效点
        valid_x = x_coords[valid_mask]
        valid_y = y_coords[valid_mask]
        valid_z = z_coords[valid_mask]

        # 生成颜色（基于高度）
        z_min, z_max = np.min(valid_z), np.max(valid_z)
        if z_max > z_min:
            normalized_z = (valid_z - z_min) / (z_max - z_min)
        else:
            normalized_z = np.zeros_like(valid_z)

        # 彩虹色映射
        colors = np.zeros((valid_count, 3), dtype=np.uint8)
        colors[:, 0] = (255 * (1 - normalized_z)).astype(np.uint8)  # R
        colors[:, 1] = (255 * np.abs(2 * normalized_z - 1)).astype(np.uint8)  # G
        colors[:, 2] = (255 * normalized_z).astype(np.uint8)  # B

        # 写入PLY文件
        with open(output_path, 'w') as f:
            f.write("ply\n")
            f.write("format ascii 1.0\n")
            f.write(f"element vertex {valid_count}\n")
            f.write("property float x\n")
            f.write("property float y\n")
            f.write("property float z\n")
            f.write("property uchar red\n")
            f.write("property uchar green\n")
            f.write("property uchar blue\n")
            f.write("end_header\n")

            for i in range(valid_count):
                f.write(f"{valid_x[i]:.6f} {valid_y[i]:.6f} {valid_z[i]:.6f} "
                       f"{colors[i, 0]} {colors[i, 1]} {colors[i, 2]}\n")

        print(f"  点云保存完成: {valid_count:,} 个点")
        print(f"  高度范围: [{z_min:.3f}, {z_max:.3f}] mm")

    def process_test_folder(self, test_dir: str, output_dir: str):
        """处理单个测试文件夹"""
        test_path = Path(test_dir)
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        print(f"\n=== 处理测试文件夹: {test_path.name} ===")

        # 重建所有投影仪
        depth_maps = []
        quality_infos = []

        for projector_id in range(4):
            if projector_id in self.mapping_tables:
                depth_map, quality_info = self.reconstruct_single_projector(str(test_path), projector_id)
                depth_maps.append(depth_map)
                quality_infos.append(quality_info)
            else:
                print(f"  跳过投影仪 {projector_id}: 无标定数据")
                depth_maps.append(None)
                quality_infos.append({"projector_id": projector_id, "is_valid": False, "error": "无标定数据"})

        # 自适应融合
        fused_depth, fusion_info = self.adaptive_fusion(depth_maps, quality_infos)

        if fused_depth is not None:
            # 保存深度图
            depth_file = output_path / f"{test_path.name}_depth.tiff"
            cv2.imwrite(str(depth_file), fused_depth.astype(np.float32))

            # 保存点云
            ply_file = output_path / f"{test_path.name}_pointcloud.ply"
            self.save_point_cloud(fused_depth, str(ply_file))

            # 保存融合信息
            info_file = output_path / f"{test_path.name}_info.json"
            import json
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "test_folder": test_path.name,
                    "fusion_info": fusion_info,
                    "quality_infos": quality_infos
                }, f, indent=2, ensure_ascii=False)

            print(f"结果保存到: {output_path}")
            print(f"  深度图: {depth_file}")
            print(f"  点云: {ply_file}")
            print(f"  信息: {info_file}")
        else:
            print("融合失败，无法生成结果")

    def process_all_test_folders(self, test_base_dir: str, output_dir: str):
        """处理所有测试文件夹"""
        test_base_path = Path(test_base_dir)

        print(f"=== 双频融合重建系统 ===")
        print(f"测试数据目录: {test_base_path}")
        print(f"输出目录: {output_dir}")

        # 查找所有测试文件夹
        test_folders = []
        for item in test_base_path.iterdir():
            if item.is_dir() and item.name.startswith(('test', 'Test', 'pcb', 'PCB')):
                test_folders.append(item)

        test_folders.sort()
        print(f"找到 {len(test_folders)} 个测试文件夹")

        # 处理每个测试文件夹
        for test_folder in test_folders:
            try:
                self.process_test_folder(str(test_folder), output_dir)
            except Exception as e:
                print(f"处理 {test_folder.name} 时出错: {e}")
                continue

        print(f"\n=== 所有测试文件夹处理完成 ===")


def main():
    parser = argparse.ArgumentParser(description="双频融合重建系统")
    parser.add_argument("--test_dir", type=str, default="pcb", help="测试数据目录")
    parser.add_argument("--output_dir", type=str, default="dual_freq_fusion_results", help="输出目录")
    parser.add_argument("--calibration_dir", type=str, default="dual_freq_calibration_results", help="标定结果目录")
    parser.add_argument("--outlier_threshold", type=float, default=3.0, help="异常值检测阈值")
    parser.add_argument("--min_valid_ratio", type=float, default=0.1, help="最小有效数据比例")
    args = parser.parse_args()

    # 创建融合器
    fusion_engine = DualFrequencyFusion(
        calibration_results_dir=args.calibration_dir,
        outlier_threshold=args.outlier_threshold,
        min_valid_ratio=args.min_valid_ratio
    )

    # 处理测试数据
    fusion_engine.process_all_test_folders(args.test_dir, args.output_dir)


if __name__ == "__main__":
    main()
