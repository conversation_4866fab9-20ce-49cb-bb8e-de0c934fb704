#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不同的标定方法
"""

import numpy as np
import cv2
import os
import matplotlib.pyplot as plt
from phase_height_mapping_calibration import PhaseHeightMappingCalibration


def test_different_frequencies():
    """测试不同频率的标定效果"""
    print("=== 测试不同频率的标定效果 ===")
    
    calibrator = PhaseHeightMappingCalibration()
    frequencies = [1080, 512, 64, 8]  # 从低频到高频
    
    results = {}
    
    for freq in frequencies:
        print(f"\n测试频率: {freq}像素周期")
        
        try:
            result = calibrator.calibrate_single_projector(0, frequency=freq)
            if result:
                model_info = result['model_info']
                results[freq] = {
                    'r2_score': model_info['r2_score'],
                    'rmse': model_info['rmse'],
                    'correlation': model_info['correlation'],
                    'n_samples': model_info['n_samples']
                }
                print(f"  R² = {model_info['r2_score']:.6f}")
                print(f"  RMSE = {model_info['rmse']:.4f} mm")
                print(f"  相关系数 = {model_info['correlation']:.6f}")
            else:
                print(f"  频率 {freq} 标定失败")
                
        except Exception as e:
            print(f"  频率 {freq} 出错: {e}")
    
    # 总结结果
    print(f"\n频率对比总结:")
    print(f"{'频率':<8} {'R²分数':<12} {'RMSE(mm)':<12} {'相关系数':<12}")
    print("-" * 50)
    
    for freq, result in results.items():
        print(f"{freq:<8} {result['r2_score']:<12.6f} {result['rmse']:<12.4f} {result['correlation']:<12.6f}")
    
    # 找出最佳频率
    if results:
        best_freq = max(results.keys(), key=lambda x: results[x]['r2_score'])
        print(f"\n最佳频率: {best_freq}像素周期 (R² = {results[best_freq]['r2_score']:.6f})")


def test_phase_difference_method():
    """测试相位差方法"""
    print("\n=== 测试相位差方法 ===")
    
    calibrator = PhaseHeightMappingCalibration()
    
    # 使用1080像素周期（最低频）
    frequency = 1080
    projector_id = 0
    reference_position = 0  # 使用Test-0作为参考
    
    print(f"使用频率: {frequency}像素周期")
    print(f"参考位置: Test-{reference_position}")
    
    # 收集相位差数据
    phase_diffs = []
    heights = []
    
    # 加载参考位置的相位
    ref_folder = os.path.join("标定数据", f"Test-{reference_position}")
    ref_images = calibrator.load_phase_shift_images(ref_folder, projector_id, frequency)
    
    if ref_images is None:
        print("无法加载参考图像")
        return
    
    ref_phase, ref_mod = calibrator.calculate_wrapped_phase(ref_images)
    
    print("计算各位置与参考位置的相位差...")
    
    for position in range(11):
        if position == reference_position:
            # 参考位置的相位差为0
            phase_diff = np.zeros_like(ref_phase)
        else:
            test_folder = os.path.join("标定数据", f"Test-{position}")
            
            if os.path.exists(test_folder):
                images = calibrator.load_phase_shift_images(test_folder, projector_id, frequency)
                
                if images is not None:
                    current_phase, current_mod = calibrator.calculate_wrapped_phase(images)
                    
                    # 计算相位差
                    phase_diff = current_phase - ref_phase
                    
                    # 处理相位跳跃
                    phase_diff = np.angle(np.exp(1j * phase_diff))
                    
                    # 应用质量掩码
                    quality_mask = (current_mod > 0.1) & (ref_mod > 0.1)
                    phase_diff[~quality_mask] = np.nan
                else:
                    print(f"  位置 {position}: 图像加载失败")
                    continue
            else:
                print(f"  位置 {position}: 文件夹不存在")
                continue
        
        phase_diffs.append(phase_diff)
        heights.append(position * 1.0)
        
        # 计算统计信息
        valid_diff = phase_diff[~np.isnan(phase_diff)]
        if len(valid_diff) > 0:
            print(f"  位置 {position} (高度 {position}mm): 相位差均值 = {np.mean(valid_diff):.6f} rad")
        else:
            print(f"  位置 {position} (高度 {position}mm): 无有效相位差")
    
    # 提取相位差-高度对
    all_phase_diffs = []
    all_heights = []
    
    for phase_diff, height in zip(phase_diffs, heights):
        valid_mask = ~np.isnan(phase_diff)
        valid_diffs = phase_diff[valid_mask]
        
        if len(valid_diffs) > 0:
            # 随机采样
            n_samples = min(1000, len(valid_diffs))
            if len(valid_diffs) > n_samples:
                indices = np.random.choice(len(valid_diffs), n_samples, replace=False)
                sampled_diffs = valid_diffs[indices]
            else:
                sampled_diffs = valid_diffs
            
            all_phase_diffs.extend(sampled_diffs)
            all_heights.extend([height] * len(sampled_diffs))
    
    if len(all_phase_diffs) > 0:
        phase_diff_array = np.array(all_phase_diffs)
        height_array = np.array(all_heights)
        
        # 计算相关系数
        correlation = np.corrcoef(phase_diff_array, height_array)[0, 1]
        print(f"\n相位差-高度相关系数: {correlation:.6f}")
        
        # 可视化相位差-高度关系
        plt.figure(figsize=(10, 6))
        plt.scatter(phase_diff_array, height_array, alpha=0.1, s=1)
        plt.xlabel('Phase Difference (rad)')
        plt.ylabel('Height (mm)')
        plt.title(f'Phase Difference vs Height (Correlation: {correlation:.6f})')
        plt.grid(True, alpha=0.3)
        plt.savefig('phase_difference_vs_height.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        # 如果相关性好，尝试拟合模型
        if abs(correlation) > 0.5:
            print("相关性较好，尝试拟合线性模型...")
            
            from sklearn.linear_model import LinearRegression
            from sklearn.metrics import r2_score, mean_squared_error
            
            model = LinearRegression()
            X = phase_diff_array.reshape(-1, 1)
            model.fit(X, height_array)
            
            y_pred = model.predict(X)
            r2 = r2_score(height_array, y_pred)
            rmse = np.sqrt(mean_squared_error(height_array, y_pred))
            
            print(f"线性模型结果:")
            print(f"  R² = {r2:.6f}")
            print(f"  RMSE = {rmse:.4f} mm")
            print(f"  斜率 = {model.coef_[0]:.4f} mm/rad")
            print(f"  截距 = {model.intercept_:.4f} mm")
        else:
            print("相关性较低，不适合拟合模型")
    else:
        print("没有有效的相位差数据")


def test_intensity_based_calibration():
    """测试基于图像强度的标定（作为对照）"""
    print("\n=== 测试基于图像强度的标定 ===")
    
    calibrator = PhaseHeightMappingCalibration()
    
    intensities = []
    heights = []
    
    print("收集各位置的图像强度...")
    
    for position in range(11):
        test_folder = os.path.join("标定数据", f"Test-{position}")
        
        if os.path.exists(test_folder):
            # 使用第一张图像的平均强度
            img_path = os.path.join(test_folder, "1.bmp")
            
            if os.path.exists(img_path):
                img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
                if img is not None:
                    mean_intensity = np.mean(img)
                    intensities.append(mean_intensity)
                    heights.append(position * 1.0)
                    print(f"  位置 {position} (高度 {position}mm): 强度 = {mean_intensity:.1f}")
    
    if len(intensities) > 1:
        intensity_array = np.array(intensities)
        height_array = np.array(heights)
        
        # 计算相关系数
        correlation = np.corrcoef(intensity_array, height_array)[0, 1]
        print(f"\n强度-高度相关系数: {correlation:.6f}")
        
        # 拟合线性模型
        from sklearn.linear_model import LinearRegression
        from sklearn.metrics import r2_score, mean_squared_error
        
        model = LinearRegression()
        X = intensity_array.reshape(-1, 1)
        model.fit(X, height_array)
        
        y_pred = model.predict(X)
        r2 = r2_score(height_array, y_pred)
        rmse = np.sqrt(mean_squared_error(height_array, y_pred))
        
        print(f"强度-高度线性模型:")
        print(f"  R² = {r2:.6f}")
        print(f"  RMSE = {rmse:.4f} mm")
        print(f"  斜率 = {model.coef_[0]:.4f} mm/intensity")
        print(f"  截距 = {model.intercept_:.4f} mm")
        
        # 可视化
        plt.figure(figsize=(10, 6))
        plt.scatter(intensity_array, height_array, c='blue', s=50, label='Data points')
        plt.plot(intensity_array, y_pred, 'r-', linewidth=2, label='Linear fit')
        plt.xlabel('Image Intensity')
        plt.ylabel('Height (mm)')
        plt.title(f'Intensity vs Height (R² = {r2:.6f})')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.savefig('intensity_vs_height.png', dpi=150, bbox_inches='tight')
        plt.show()


def main():
    """主函数"""
    print("测试不同的标定方法")
    print("="*50)
    
    # 1. 测试不同频率
    test_different_frequencies()
    
    # 2. 测试相位差方法
    test_phase_difference_method()
    
    # 3. 测试强度方法（作为对照）
    test_intensity_based_calibration()
    
    print("\n测试完成！")
    print("建议:")
    print("1. 如果强度-高度相关性很好，说明数据本身是有效的")
    print("2. 如果相位差方法效果更好，可以考虑使用相位差进行标定")
    print("3. 如果所有相位方法都效果不好，可能需要检查相位计算算法")


if __name__ == "__main__":
    main()
