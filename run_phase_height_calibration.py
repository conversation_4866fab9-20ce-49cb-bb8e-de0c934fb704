#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行相位高度映射标定的主程序
"""

import os
import time
import json
from phase_height_mapping_calibration import PhaseHeightMappingCalibration


def print_quality_report(quality_report):
    """打印质量报告"""
    print("\n" + "="*60)
    print("相位高度标定质量报告")
    print("="*60)
    
    if "error" in quality_report:
        print(f"错误: {quality_report['error']}")
        return
    
    # 整体质量
    print(f"整体质量: {quality_report['overall_quality']}")
    print(f"已标定投影仪数量: {quality_report['num_calibrated_projectors']}")
    print(f"平均R²分数: {quality_report['average_r2']:.6f}")
    print(f"平均RMSE: {quality_report['average_rmse']:.4f} mm")
    
    # 各投影仪详情
    print("\n各投影仪详情:")
    print("-" * 80)
    print(f"{'投影仪':<8} {'质量':<12} {'R²分数':<12} {'RMSE(mm)':<12} {'相关系数':<12} {'模型类型':<12}")
    print("-" * 80)
    
    for proj_id, report in quality_report["projector_reports"].items():
        print(f"{proj_id:<8} {report['quality']:<12} {report['r2_score']:<12.6f} "
              f"{report['rmse']:<12.4f} {report['correlation']:<12.6f} {report['model_type']:<12}")
    
    # 建议
    print("\n建议:")
    for i, recommendation in enumerate(quality_report["recommendations"], 1):
        print(f"{i}. {recommendation}")
    
    print("="*60)


def check_data_integrity():
    """检查数据完整性"""
    print("检查数据完整性...")
    
    calibration_folder = "标定数据"
    if not os.path.exists(calibration_folder):
        print(f"错误: 标定数据文件夹不存在: {calibration_folder}")
        return False
    
    missing_folders = []
    incomplete_folders = []
    
    for i in range(11):  # Test-0 到 Test-10
        folder_path = os.path.join(calibration_folder, f"Test-{i}")
        if not os.path.exists(folder_path):
            missing_folders.append(f"Test-{i}")
        else:
            # 检查是否有足够的图像文件
            image_count = len([f for f in os.listdir(folder_path) if f.endswith('.bmp')])
            if image_count < 80:  # 应该有80张有用的图像
                incomplete_folders.append(f"Test-{i} ({image_count} images)")
    
    if missing_folders:
        print(f"缺少文件夹: {missing_folders}")
        return False
    
    if incomplete_folders:
        print(f"图像不完整的文件夹: {incomplete_folders}")
        print("警告: 某些文件夹的图像数量不足，可能影响标定质量")
    
    print("数据完整性检查通过")
    return True


def run_full_calibration():
    """运行完整的标定流程"""
    print("=== 相位高度映射标定程序 ===")
    print("数据结构:")
    print("- 11个高度位置 (Test-0 到 Test-10)，每次移动1mm")
    print("- 4个投影仪 (0-3号)")
    print("- 4个频率 (8, 64, 512, 1080像素周期)")
    print("- 每个频率5步相移")
    print()
    
    # 检查数据完整性
    if not check_data_integrity():
        print("数据完整性检查失败，请检查数据文件")
        return
    
    # 创建标定器
    calibrator = PhaseHeightMappingCalibration(calibration_data_folder="标定数据")
    
    # 开始标定
    print("\n开始标定过程...")
    start_time = time.time()
    
    try:
        # 标定所有投影仪（使用8像素周期，最敏感）
        calibration_results = calibrator.calibrate_all_projectors(frequency=8)
        
        if not calibration_results:
            print("标定失败: 没有成功标定任何投影仪")
            return
        
        # 保存标定结果和生成报告
        print("\n保存标定结果...")
        quality_report = calibrator.save_calibration_results("phase_height_calibration_results")
        
        # 生成可视化结果
        print("生成可视化结果...")
        calibrator.visualize_calibration_results("phase_height_calibration_results")
        
        # 打印质量报告
        print_quality_report(quality_report)
        
        end_time = time.time()
        print(f"\n标定完成! 总耗时: {end_time - start_time:.1f} 秒")
        print("结果文件保存在 'phase_height_calibration_results' 文件夹中")
        
        # 保存详细的文本报告
        save_detailed_report(calibration_results, quality_report, "phase_height_calibration_results")
        
    except Exception as e:
        print(f"标定过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def save_detailed_report(calibration_results, quality_report, output_folder):
    """保存详细的文本报告"""
    report_file = os.path.join(output_folder, "detailed_calibration_report.txt")
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("相位高度映射标定详细报告\n")
        f.write("="*60 + "\n\n")
        
        # 标定概述
        f.write("标定概述:\n")
        f.write(f"- 标定时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"- 成功标定投影仪数量: {len(calibration_results)}\n")
        f.write(f"- 使用频率: 8像素周期\n")
        f.write(f"- 高度范围: 0-10mm (步长1mm)\n\n")
        
        # 整体质量评估
        f.write("整体质量评估:\n")
        f.write(f"- 整体质量: {quality_report['overall_quality']}\n")
        f.write(f"- 平均R²分数: {quality_report['average_r2']:.6f}\n")
        f.write(f"- 平均RMSE: {quality_report['average_rmse']:.4f} mm\n\n")
        
        # 各投影仪详细信息
        f.write("各投影仪详细信息:\n")
        f.write("-" * 60 + "\n")
        
        for proj_id, result in calibration_results.items():
            model_info = result['model_info']
            f.write(f"\n投影仪 {proj_id} ({result['projector_name']}):\n")
            f.write(f"  频率: {result['frequency']}像素周期\n")
            f.write(f"  模型类型: {model_info['model_type']} (度数: {model_info['degree']})\n")
            f.write(f"  性能指标:\n")
            f.write(f"    R² 分数: {model_info['r2_score']:.6f}\n")
            f.write(f"    RMSE: {model_info['rmse']:.4f} mm\n")
            f.write(f"    相关系数: {model_info['correlation']:.6f}\n")
            f.write(f"    残差标准差: {model_info['residual_std']:.4f} mm\n")
            f.write(f"  数据统计:\n")
            f.write(f"    样本数量: {model_info['n_samples']:,}\n")
            f.write(f"    内点比例: {model_info['inlier_ratio']:.3f}\n")
            f.write(f"    相位范围: [{model_info['phase_range'][0]:.3f}, {model_info['phase_range'][1]:.3f}] rad\n")
            f.write(f"    高度范围: [{model_info['height_range'][0]:.1f}, {model_info['height_range'][1]:.1f}] mm\n")
            f.write(f"  质量评估: {quality_report['projector_reports'][proj_id]['quality']}\n")
        
        # 建议
        f.write(f"\n建议:\n")
        for i, recommendation in enumerate(quality_report["recommendations"], 1):
            f.write(f"{i}. {recommendation}\n")
        
        # 使用说明
        f.write(f"\n使用说明:\n")
        f.write(f"1. 标定模型已保存在 calibration_models.pkl 文件中\n")
        f.write(f"2. 使用 PhaseHeightMappingCalibration.predict_height_from_phase() 方法进行高度预测\n")
        f.write(f"3. 输入相位图，输出对应的高度图\n")
        f.write(f"4. 确保输入相位的范围在标定范围内以获得准确结果\n")
    
    print(f"详细报告已保存到: {report_file}")


def test_single_projector():
    """测试单个投影仪的标定"""
    print("=== 测试单个投影仪标定 ===")
    
    if not check_data_integrity():
        return
    
    calibrator = PhaseHeightMappingCalibration()
    
    # 仅标定投影仪0
    result = calibrator.calibrate_single_projector(0, frequency=8)
    
    if result:
        print("单投影仪标定测试成功!")
        
        # 保存结果
        calibrator.calibration_results[0] = result
        quality_report = calibrator.save_calibration_results("single_projector_test")
        calibrator.visualize_calibration_results("single_projector_test")
        
        print_quality_report(quality_report)
    else:
        print("单投影仪标定测试失败")


def test_height_prediction():
    """测试高度预测功能"""
    print("=== 测试高度预测功能 ===")
    
    # 加载已有的标定结果
    calibrator = PhaseHeightMappingCalibration()
    
    results_file = "phase_height_calibration_results/calibration_results.pkl"
    if not os.path.exists(results_file):
        print("没有找到标定结果文件，请先运行标定")
        return
    
    import pickle
    with open(results_file, 'rb') as f:
        calibrator.calibration_results = pickle.load(f)
    
    print(f"加载了 {len(calibrator.calibration_results)} 个投影仪的标定结果")
    
    # 使用Test-5的数据进行测试
    test_folder = "标定数据/Test-5"
    expected_height = 5.0  # mm
    
    if not os.path.exists(test_folder):
        print(f"测试文件夹不存在: {test_folder}")
        return
    
    print(f"使用 {test_folder} 进行测试 (期望高度: {expected_height} mm)")
    
    for proj_id in calibrator.calibration_results.keys():
        print(f"\n测试投影仪 {proj_id}:")
        
        try:
            # 加载测试图像并计算相位
            images = calibrator.load_phase_shift_images(test_folder, proj_id, 8)
            if images is not None:
                wrapped_phase, modulation = calibrator.calculate_wrapped_phase(images)
                
                # 预测高度
                height_map = calibrator.predict_height_from_phase(wrapped_phase, proj_id)
                
                # 计算统计信息
                valid_heights = height_map[~np.isnan(height_map)]
                if len(valid_heights) > 0:
                    mean_height = np.mean(valid_heights)
                    std_height = np.std(valid_heights)
                    error = abs(mean_height - expected_height)
                    
                    print(f"  预测高度: {mean_height:.3f} ± {std_height:.3f} mm")
                    print(f"  期望高度: {expected_height:.3f} mm")
                    print(f"  误差: {error:.3f} mm")
                    print(f"  有效像素: {len(valid_heights):,}")
                    
                    if error < 0.5:
                        print(f"  结果: 优秀 (误差 < 0.5mm)")
                    elif error < 1.0:
                        print(f"  结果: 良好 (误差 < 1.0mm)")
                    else:
                        print(f"  结果: 需要改进 (误差 >= 1.0mm)")
                else:
                    print("  没有有效的高度预测")
            else:
                print("  图像加载失败")
                
        except Exception as e:
            print(f"  测试失败: {e}")


def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "test":
            test_single_projector()
        elif sys.argv[1] == "predict":
            test_height_prediction()
        elif sys.argv[1] == "full":
            run_full_calibration()
        else:
            print("用法: python run_phase_height_calibration.py [test|predict|full]")
            print("  test: 测试单个投影仪标定")
            print("  predict: 测试高度预测功能")
            print("  full: 运行完整标定流程")
    else:
        run_full_calibration()


if __name__ == "__main__":
    main()
