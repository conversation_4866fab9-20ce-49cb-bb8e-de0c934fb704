#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行基于您的相位展开程序的最终标定程序
"""

import os
import time
import numpy as np
from phase_height_calibration_final import PhaseHeightCalibrationFinal


def print_quality_report(quality_report):
    """打印质量报告"""
    print("\n" + "="*70)
    print("相位高度标定质量报告")
    print("="*70)
    
    if "error" in quality_report:
        print(f"错误: {quality_report['error']}")
        return
    
    # 整体质量
    print(f"标定方法: {quality_report['method']}")
    print(f"拟合度数: {quality_report['fit_degree']}")
    print(f"整体质量: {quality_report['overall_quality']}")
    print(f"已标定投影仪数量: {quality_report['num_calibrated_projectors']}")
    print(f"平均RMSE: {quality_report['average_rmse']:.4f} mm")
    print(f"平均误差: {quality_report['average_mean_error']:.4f} mm")
    
    # 各投影仪详情
    print("\n各投影仪详情:")
    print("-" * 90)
    print(f"{'投影仪':<8} {'质量':<12} {'RMSE(mm)':<12} {'平均误差(mm)':<15} {'最大误差(mm)':<15} {'有效像素':<12}")
    print("-" * 90)
    
    for proj_id, report in quality_report["projector_reports"].items():
        print(f"{proj_id:<8} {report['quality']:<12} {report['rmse']:<12.4f} "
              f"{report['mean_error']:<15.4f} {report['max_error']:<15.4f} {report['valid_pixels']:<12,}")
    
    # 建议
    print("\n建议:")
    for i, recommendation in enumerate(quality_report["recommendations"], 1):
        print(f"{i}. {recommendation}")
    
    print("="*70)


def check_data_integrity():
    """检查数据完整性"""
    print("检查数据完整性...")
    
    calibration_folder = "标定数据"
    if not os.path.exists(calibration_folder):
        print(f"错误: 标定数据文件夹不存在: {calibration_folder}")
        return False
    
    missing_folders = []
    incomplete_folders = []
    
    for i in range(11):
        folder_path = os.path.join(calibration_folder, f"Test-{i}")
        if not os.path.exists(folder_path):
            missing_folders.append(f"Test-{i}")
        else:
            # 检查图像文件数量
            image_count = len([f for f in os.listdir(folder_path) if f.endswith('.bmp')])
            if image_count < 80:
                incomplete_folders.append(f"Test-{i} ({image_count} images)")
    
    if missing_folders:
        print(f"缺少文件夹: {missing_folders}")
        return False
    
    if incomplete_folders:
        print(f"图像不完整的文件夹: {incomplete_folders}")
        print("警告: 某些文件夹的图像数量不足，可能影响标定质量")
    
    print("数据完整性检查通过")
    return True


def run_complete_calibration(use_four_freq: bool = True):
    """运行完整的标定流程"""
    print("=== 基于您的相位展开程序的标定系统 ===")
    print(f"相位展开方法: {'四频相位展开' if use_four_freq else '实用相位展开'}")
    print("标定方法: 多项式映射（参考C++程序）")
    print("数据结构:")
    print("- 11个高度位置 (Test-0 到 Test-10)，每次移动1mm")
    print("- 4个投影仪 (0-3号)")
    print("- 4个频率 (8, 64, 512, 1080像素周期)")
    print("- 每个频率5步相移")
    print()
    
    # 检查数据完整性
    if not check_data_integrity():
        print("数据完整性检查失败，请检查数据文件")
        return
    
    # 创建标定器
    calibrator = PhaseHeightCalibrationFinal(calibration_data_folder="标定数据")
    
    # 开始标定
    print(f"\n开始标定过程...")
    start_time = time.time()
    
    try:
        # 标定所有投影仪
        calibration_results = calibrator.calibrate_all_projectors(use_four_freq=use_four_freq)
        
        if not calibration_results:
            print("标定失败: 没有成功标定任何投影仪")
            return
        
        # 保存标定结果
        print("\n保存标定结果...")
        output_folder = f"final_calibration_results_{'four_freq' if use_four_freq else 'practical'}"
        quality_report = calibrator.save_calibration_results(output_folder)
        
        # 打印质量报告
        print_quality_report(quality_report)
        
        # 生成详细报告
        save_detailed_report(calibration_results, quality_report, output_folder, use_four_freq)
        
        end_time = time.time()
        print(f"\n标定完成! 总耗时: {end_time - start_time:.1f} 秒")
        print(f"结果文件保存在 '{output_folder}' 文件夹中")
        
        # 性能总结
        print_performance_summary(quality_report, use_four_freq)
        
    except Exception as e:
        print(f"标定过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def save_detailed_report(calibration_results, quality_report, output_folder, use_four_freq):
    """保存详细报告"""
    report_file = os.path.join(output_folder, "detailed_calibration_report.txt")
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("基于您的相位展开程序的标定详细报告\n")
        f.write("="*70 + "\n\n")
        
        # 标定概述
        f.write("标定概述:\n")
        f.write(f"- 标定时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"- 相位展开方法: {'四频相位展开' if use_four_freq else '实用相位展开'}\n")
        f.write(f"- 标定方法: 多项式映射（参考C++程序）\n")
        f.write(f"- 成功标定投影仪数量: {len(calibration_results)}\n")
        f.write(f"- 拟合度数: {quality_report['fit_degree']}\n")
        f.write(f"- 高度范围: 0-10mm (步长1mm)\n\n")
        
        # 方法说明
        f.write("方法说明:\n")
        f.write("本程序基于您提供的相位展开程序（four_freq_phase_unwrap.py 和 practical_phase_unwrap.py）\n")
        f.write("以及参考C++标定程序（mulPlaneProsCalib.cpp）的多项式映射方法。\n")
        f.write("通过逐像素拟合多项式建立相位到高度的映射关系。\n\n")
        
        # 整体质量评估
        f.write("整体质量评估:\n")
        f.write(f"- 整体质量: {quality_report['overall_quality']}\n")
        f.write(f"- 平均RMSE: {quality_report['average_rmse']:.4f} mm\n")
        f.write(f"- 平均误差: {quality_report['average_mean_error']:.4f} mm\n\n")
        
        # 各投影仪详细信息
        f.write("各投影仪详细信息:\n")
        f.write("-" * 70 + "\n")
        
        for proj_id, result in calibration_results.items():
            metrics = result['quality_metrics']
            f.write(f"\n投影仪 {proj_id} ({result['projector_name']}):\n")
            f.write(f"  相位展开方法: {'四频' if result['use_four_freq'] else '实用'}\n")
            f.write(f"  拟合度数: {result['fit_degree']}\n")
            f.write(f"  标定位置数: {result['num_positions']}\n")
            f.write(f"  性能指标:\n")
            f.write(f"    RMSE: {metrics['overall_rmse']:.4f} mm\n")
            f.write(f"    平均误差: {metrics['overall_mean_error']:.4f} mm\n")
            f.write(f"    标准差: {metrics['overall_std_error']:.4f} mm\n")
            f.write(f"    最大误差: {metrics['overall_max_error']:.4f} mm\n")
            f.write(f"  数据统计:\n")
            f.write(f"    有效像素总数: {metrics['total_valid_pixels']:,}\n")
            f.write(f"  质量评估: {quality_report['projector_reports'][proj_id]['quality']}\n")
        
        # 使用说明
        f.write(f"\n使用说明:\n")
        f.write(f"1. 映射表已保存在 mapping_tables/ 文件夹中\n")
        f.write(f"2. 每个投影仪有 {quality_report['fit_degree']} 个系数图（DLP-{{projector_id}}-{{degree}}.tiff）\n")
        f.write(f"3. 使用 predict_height_from_phase() 方法进行高度预测\n")
        f.write(f"4. 输入展开相位图，输出对应的高度图\n")
        f.write(f"5. 高度计算公式: h = c0 + c1*φ + c2*φ² + c3*φ³ + ...\n\n")
        
        # 建议
        f.write(f"建议:\n")
        for i, recommendation in enumerate(quality_report["recommendations"], 1):
            f.write(f"{i}. {recommendation}\n")
    
    print(f"详细报告已保存到: {report_file}")


def print_performance_summary(quality_report, use_four_freq):
    """打印性能总结"""
    print("\n" + "="*50)
    print("性能总结")
    print("="*50)
    
    avg_rmse = quality_report['average_rmse']
    avg_mean_error = quality_report['average_mean_error']
    
    print(f"基于您的相位展开程序的标定性能:")
    print(f"- 相位展开方法: {'四频相位展开' if use_four_freq else '实用相位展开'}")
    print(f"- 标定方法: 多项式映射")
    print(f"- 平均RMSE: {avg_rmse:.4f} mm")
    print(f"- 平均误差: {avg_mean_error:.4f} mm")
    
    if avg_rmse < 0.5 and avg_mean_error < 0.3:
        print("✓ 标定质量: 优秀")
        print("✓ 适用于高精度三维测量")
    elif avg_rmse < 1.0 and avg_mean_error < 0.5:
        print("✓ 标定质量: 良好")
        print("✓ 适用于一般精度三维测量")
    else:
        print("⚠ 标定质量: 需要改进")
        print("⚠ 建议检查数据质量或调整参数")
    
    print("\n程序特点:")
    print("1. 基于您的专业相位展开算法")
    print("2. 参考C++程序的多项式映射方法")
    print("3. 逐像素拟合，精度高")
    print("4. 支持四频和实用两种展开方法")
    print("5. 完整的质量评估和报告系统")
    
    print("="*50)


def test_single_projector(use_four_freq: bool = True):
    """测试单个投影仪"""
    print(f"=== 测试单个投影仪标定 ({'四频' if use_four_freq else '实用'}相位展开) ===")
    
    if not check_data_integrity():
        return
    
    calibrator = PhaseHeightCalibrationFinal()
    
    # 测试投影仪0
    result = calibrator.calibrate_single_projector(0, use_four_freq=use_four_freq)
    
    if result:
        print("单投影仪标定测试成功!")
        
        # 保存结果
        calibrator.calibration_results[0] = result
        output_folder = f"single_projector_test_{'four_freq' if use_four_freq else 'practical'}"
        quality_report = calibrator.save_calibration_results(output_folder)
        
        print_quality_report(quality_report)
    else:
        print("单投影仪标定测试失败")


def compare_methods():
    """对比四频和实用两种方法"""
    print("=== 对比四频和实用两种相位展开方法 ===")
    
    if not check_data_integrity():
        return
    
    print("\n1. 测试四频相位展开方法...")
    test_single_projector(use_four_freq=True)
    
    print("\n2. 测试实用相位展开方法...")
    test_single_projector(use_four_freq=False)
    
    print("\n对比完成！请查看生成的结果文件夹进行详细对比。")


def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "test_four":
            test_single_projector(use_four_freq=True)
        elif sys.argv[1] == "test_practical":
            test_single_projector(use_four_freq=False)
        elif sys.argv[1] == "compare":
            compare_methods()
        elif sys.argv[1] == "full_four":
            run_complete_calibration(use_four_freq=True)
        elif sys.argv[1] == "full_practical":
            run_complete_calibration(use_four_freq=False)
        else:
            print("用法: python run_final_phase_calibration.py [选项]")
            print("选项:")
            print("  test_four      - 测试单个投影仪（四频展开）")
            print("  test_practical - 测试单个投影仪（实用展开）")
            print("  compare        - 对比两种方法")
            print("  full_four      - 完整标定（四频展开）")
            print("  full_practical - 完整标定（实用展开）")
    else:
        # 默认使用四频展开进行完整标定
        run_complete_calibration(use_four_freq=True)


if __name__ == "__main__":
    main()
