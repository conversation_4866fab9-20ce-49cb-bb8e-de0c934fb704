基于您的相位展开程序的标定详细报告
======================================================================

标定概述:
- 标定时间: 2025-07-25 23:16:36
- 相位展开方法: 四频相位展开
- 标定方法: 多项式映射（参考C++程序）
- 成功标定投影仪数量: 4
- 拟合度数: 3
- 高度范围: 0-10mm (步长1mm)

方法说明:
本程序基于您提供的相位展开程序（four_freq_phase_unwrap.py 和 practical_phase_unwrap.py）
以及参考C++标定程序（mulPlaneProsCalib.cpp）的多项式映射方法。
通过逐像素拟合多项式建立相位到高度的映射关系。

整体质量评估:
- 整体质量: Excellent
- 平均RMSE: 0.4325 mm
- 平均误差: 0.2308 mm

各投影仪详细信息:
----------------------------------------------------------------------

投影仪 0 (Projector_0):
  相位展开方法: 四频
  拟合度数: 3
  标定位置数: 11
  性能指标:
    RMSE: 0.4434 mm
    平均误差: 0.2350 mm
    标准差: 0.3760 mm
    最大误差: 5.0616 mm
  数据统计:
    有效像素总数: 89,222,144
  质量评估: Excellent

投影仪 1 (Projector_1):
  相位展开方法: 四频
  拟合度数: 3
  标定位置数: 11
  性能指标:
    RMSE: 0.4037 mm
    平均误差: 0.2188 mm
    标准差: 0.3392 mm
    最大误差: 5.3901 mm
  数据统计:
    有效像素总数: 89,222,144
  质量评估: Excellent

投影仪 2 (Projector_2):
  相位展开方法: 四频
  拟合度数: 3
  标定位置数: 11
  性能指标:
    RMSE: 0.4756 mm
    平均误差: 0.2491 mm
    标准差: 0.4051 mm
    最大误差: 4.0462 mm
  数据统计:
    有效像素总数: 89,222,144
  质量评估: Excellent

投影仪 3 (Projector_3):
  相位展开方法: 四频
  拟合度数: 3
  标定位置数: 11
  性能指标:
    RMSE: 0.4075 mm
    平均误差: 0.2203 mm
    标准差: 0.3429 mm
    最大误差: 3.5057 mm
  数据统计:
    有效像素总数: 89,222,144
  质量评估: Excellent

使用说明:
1. 映射表已保存在 mapping_tables/ 文件夹中
2. 每个投影仪有 3 个系数图（DLP-{projector_id}-{degree}.tiff）
3. 使用 predict_height_from_phase() 方法进行高度预测
4. 输入展开相位图，输出对应的高度图
5. 高度计算公式: h = c0 + c1*φ + c2*φ² + c3*φ³ + ...

建议:
1. 标定质量优秀，可以用于高精度测量
2. 平均误差优秀 (< 0.3mm)
