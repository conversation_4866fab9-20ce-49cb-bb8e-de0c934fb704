#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成相位展开的标定程序
真正使用四频五步相移展开算法进行标定
"""

import numpy as np
import cv2
import os
import matplotlib.pyplot as plt
import pickle
import json
from typing import List, Tuple, Dict, Optional
from sklearn.linear_model import LinearRegression, RANSACRegressor
from sklearn.preprocessing import PolynomialFeatures
from sklearn.pipeline import Pipeline
from sklearn.metrics import r2_score, mean_squared_error
import warnings
warnings.filterwarnings('ignore')

from phase_unwrapping_four_frequency import FourFrequencyPhaseUnwrapping


class IntegratedPhaseUnwrappingCalibration:
    """集成相位展开的标定类"""
    
    def __init__(self, calibration_data_folder: str = "标定数据"):
        """初始化标定器"""
        self.calibration_data_folder = calibration_data_folder
        
        # 投影仪配置
        self.projector_configs = {
            0: {"name": "Projector_0", "image_range": (1, 20)},
            1: {"name": "Projector_1", "image_range": (21, 40)},
            2: {"name": "Projector_2", "image_range": (41, 60)},
            3: {"name": "Projector_3", "image_range": (61, 80)}
        }
        
        # 频率配置
        self.frequency_configs = {
            8: {"image_indices": [1, 2, 3, 4, 5], "name": "8px_period"},      
            64: {"image_indices": [6, 7, 8, 9, 10], "name": "64px_period"},    
            512: {"image_indices": [11, 12, 13, 14, 15], "name": "512px_period"}, 
            1080: {"image_indices": [16, 17, 18, 19, 20], "name": "1080px_period"} 
        }
        
        # 标定参数
        self.height_step = 1.0  # mm
        self.num_positions = 11
        self.pixel_periods = [1080, 512, 64, 8]  # 从低频到高频
        
        # 创建相位展开器
        self.phase_unwrapper = FourFrequencyPhaseUnwrapping(pixel_periods=self.pixel_periods)
        
        # 结果存储
        self.calibration_results = {}
        
    def load_calibration_images_for_unwrapping(self, test_folder: str, projector_id: int) -> np.ndarray:
        """
        加载用于相位展开的图像
        Returns:
            images: 形状为 (frequencies, steps, height, width) 的图像数组
        """
        start_idx, _ = self.projector_configs[projector_id]["image_range"]
        
        # 按频率加载图像
        images_by_frequency = {}
        for period, config in self.frequency_configs.items():
            frequency_images = []
            
            for img_idx in config["image_indices"]:
                actual_img_idx = start_idx + img_idx - 1
                img_path = os.path.join(test_folder, f"{actual_img_idx}.bmp")
                
                if os.path.exists(img_path):
                    img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
                    if img is not None:
                        frequency_images.append(img.astype(np.float32))
                    else:
                        print(f"警告: 无法读取图像 {img_path}")
                        return None
                else:
                    print(f"警告: 图像文件不存在 {img_path}")
                    return None
            
            if len(frequency_images) != 5:
                print(f"警告: 频率 {period} 的图像数量不足")
                return None
                
            images_by_frequency[period] = frequency_images
        
        # 转换为相位展开器需要的格式 [1080, 512, 64, 8]
        ordered_images = []
        for period in self.pixel_periods:
            if period in images_by_frequency:
                ordered_images.append(images_by_frequency[period])
            else:
                print(f"警告: 缺少频率 {period} 的图像")
                return None
        
        return np.array(ordered_images)
    
    def process_single_position_with_unwrapping(self, test_folder: str, projector_id: int) -> Optional[np.ndarray]:
        """
        使用四频五步相移展开处理单个位置
        Args:
            test_folder: 测试文件夹路径
            projector_id: 投影仪ID
        Returns:
            unwrapped_phase: 展开的绝对相位图
        """
        try:
            print(f"    使用四频相位展开处理...")
            
            # 加载图像
            images = self.load_calibration_images_for_unwrapping(test_folder, projector_id)
            
            if images is None:
                print(f"    图像加载失败")
                return None
            
            # 计算包裹相位
            wrapped_phases, modulations = self.phase_unwrapper.calculate_all_wrapped_phases(images)
            
            # 创建质量掩码
            quality_mask = self.phase_unwrapper.create_quality_mask(modulations, min_modulation=0.05)
            
            # 分层相位展开
            unwrapped_phase = self.phase_unwrapper.phase_unwrapping_hierarchical(wrapped_phases)
            
            # 应用质量掩码
            unwrapped_phase[~quality_mask] = np.nan
            
            # 输出统计信息
            valid_phase = unwrapped_phase[~np.isnan(unwrapped_phase)]
            if len(valid_phase) > 0:
                print(f"    展开相位范围: [{np.min(valid_phase):.3f}, {np.max(valid_phase):.3f}] rad")
                print(f"    展开相位均值: {np.mean(valid_phase):.3f} rad")
                print(f"    有效像素比例: {np.sum(~np.isnan(unwrapped_phase)) / unwrapped_phase.size * 100:.1f}%")
            else:
                print(f"    没有有效的展开相位")
                return None
            
            return unwrapped_phase
            
        except Exception as e:
            print(f"    相位展开处理失败: {e}")
            return None
    
    def collect_unwrapped_phase_data(self, projector_id: int) -> Tuple[List[np.ndarray], List[float]]:
        """
        收集所有位置的展开相位数据
        Args:
            projector_id: 投影仪ID
        Returns:
            unwrapped_phase_maps: 展开相位图列表
            heights: 对应的高度列表
        """
        print(f"正在收集投影仪 {projector_id} 的四频展开相位数据...")
        
        unwrapped_phase_maps = []
        heights = []
        
        for position in range(self.num_positions):
            test_folder = os.path.join(self.calibration_data_folder, f"Test-{position}")
            
            if not os.path.exists(test_folder):
                print(f"警告: 文件夹不存在 {test_folder}")
                continue
            
            print(f"  处理位置 {position} (高度 {position * self.height_step:.1f} mm)...")
            
            # 使用四频相位展开处理
            unwrapped_phase = self.process_single_position_with_unwrapping(test_folder, projector_id)
            
            if unwrapped_phase is not None:
                unwrapped_phase_maps.append(unwrapped_phase)
                heights.append(position * self.height_step)
            else:
                print(f"    跳过位置 {position}（处理失败）")
        
        print(f"成功收集到 {len(unwrapped_phase_maps)} 个位置的展开相位数据")
        return unwrapped_phase_maps, heights
    
    def calculate_absolute_phase_difference(self, unwrapped_phase_maps: List[np.ndarray], 
                                          reference_position: int = 0) -> Tuple[List[np.ndarray], List[float]]:
        """
        计算绝对相位差（基于展开相位）
        Args:
            unwrapped_phase_maps: 展开相位图列表
            reference_position: 参考位置索引
        Returns:
            absolute_phase_diffs: 绝对相位差列表
            heights: 对应的高度列表
        """
        print(f"计算绝对相位差（参考位置: {reference_position}）...")
        
        if reference_position >= len(unwrapped_phase_maps):
            print(f"错误: 参考位置 {reference_position} 超出范围")
            return [], []
        
        reference_phase = unwrapped_phase_maps[reference_position]
        absolute_phase_diffs = []
        heights = []
        
        for i, unwrapped_phase in enumerate(unwrapped_phase_maps):
            if i == reference_position:
                # 参考位置的相位差为0
                phase_diff = np.zeros_like(reference_phase)
            else:
                # 计算绝对相位差（不需要处理相位跳跃，因为已经展开）
                phase_diff = unwrapped_phase - reference_phase
                
                # 只保留两个图像都有效的像素
                valid_mask = (~np.isnan(unwrapped_phase)) & (~np.isnan(reference_phase))
                phase_diff[~valid_mask] = np.nan
            
            absolute_phase_diffs.append(phase_diff)
            heights.append(i * self.height_step)
            
            # 输出统计信息
            valid_diff = phase_diff[~np.isnan(phase_diff)]
            if len(valid_diff) > 0:
                print(f"  位置 {i}: 绝对相位差均值 = {np.mean(valid_diff):.6f} rad")
            else:
                print(f"  位置 {i}: 无有效绝对相位差")
        
        return absolute_phase_diffs, heights
    
    def extract_absolute_phase_height_pairs(self, absolute_phase_diffs: List[np.ndarray], 
                                          heights: List[float],
                                          sample_ratio: float = 0.001) -> Tuple[np.ndarray, np.ndarray]:
        """提取绝对相位差-高度数据对"""
        all_phase_diffs = []
        all_heights = []
        
        for phase_diff, height in zip(absolute_phase_diffs, heights):
            valid_mask = ~np.isnan(phase_diff)
            valid_diffs = phase_diff[valid_mask]
            
            if len(valid_diffs) > 0:
                # 随机采样
                n_samples = max(1000, int(len(valid_diffs) * sample_ratio))
                if len(valid_diffs) > n_samples:
                    indices = np.random.choice(len(valid_diffs), n_samples, replace=False)
                    sampled_diffs = valid_diffs[indices]
                else:
                    sampled_diffs = valid_diffs
                
                all_phase_diffs.extend(sampled_diffs)
                all_heights.extend([height] * len(sampled_diffs))
        
        return np.array(all_phase_diffs), np.array(all_heights)
    
    def fit_absolute_phase_model(self, phase_diffs: np.ndarray, heights: np.ndarray) -> Dict:
        """拟合绝对相位差-高度模型"""
        print(f"拟合绝对相位差-高度模型，数据点数: {len(phase_diffs)}")
        
        # 数据预处理
        phase_std = np.std(phase_diffs)
        phase_mean = np.mean(phase_diffs)
        valid_mask = np.abs(phase_diffs - phase_mean) < 3 * phase_std
        
        clean_phase_diffs = phase_diffs[valid_mask]
        clean_heights = heights[valid_mask]
        
        print(f"数据清理后: {len(clean_phase_diffs)} 个数据点")
        
        if len(clean_phase_diffs) < 100:
            print("警告: 有效数据点太少")
            return None
        
        # 尝试不同模型
        models = [
            ('linear', LinearRegression()),
            ('polynomial_2', Pipeline([
                ('poly', PolynomialFeatures(degree=2)),
                ('linear', LinearRegression())
            ])),
            ('polynomial_3', Pipeline([
                ('poly', PolynomialFeatures(degree=3)),
                ('linear', LinearRegression())
            ])),
            ('robust_linear', RANSACRegressor(
                LinearRegression(),
                min_samples=100,
                residual_threshold=0.5,
                random_state=42
            ))
        ]
        
        best_model = None
        best_score = -np.inf
        best_name = ""
        
        X = clean_phase_diffs.reshape(-1, 1)
        
        for model_name, model in models:
            try:
                model.fit(X, clean_heights)
                y_pred = model.predict(X)
                r2 = r2_score(clean_heights, y_pred)
                rmse = np.sqrt(mean_squared_error(clean_heights, y_pred))
                
                print(f"{model_name} 模型: R² = {r2:.6f}, RMSE = {rmse:.4f} mm")
                
                if r2 > best_score:
                    best_model = model
                    best_score = r2
                    best_name = model_name
                    
            except Exception as e:
                print(f"{model_name} 模型拟合失败: {e}")
        
        if best_model is None:
            print("所有模型拟合都失败")
            return None
        
        # 使用最佳模型计算最终结果
        y_pred = best_model.predict(X)
        r2 = r2_score(clean_heights, y_pred)
        rmse = np.sqrt(mean_squared_error(clean_heights, y_pred))
        correlation = np.corrcoef(clean_phase_diffs, clean_heights)[0, 1]
        
        # 计算残差统计
        residuals = clean_heights - y_pred
        residual_std = np.std(residuals)
        
        model_info = {
            'model': best_model,
            'model_type': best_name,
            'r2_score': r2,
            'rmse': rmse,
            'residual_std': residual_std,
            'correlation': correlation,
            'n_samples': len(clean_phase_diffs),
            'absolute_phase_diff_range': (np.min(clean_phase_diffs), np.max(clean_phase_diffs)),
            'height_range': (np.min(clean_heights), np.max(clean_heights))
        }
        
        print(f"最佳模型: {best_name}")
        print(f"  相关系数: {correlation:.6f}")
        print(f"  R² 分数: {r2:.6f}")
        print(f"  RMSE: {rmse:.4f} mm")
        print(f"  残差标准差: {residual_std:.4f} mm")
        
        return model_info
    
    def calibrate_projector_with_unwrapping(self, projector_id: int, reference_position: int = 0) -> Dict:
        """使用四频相位展开进行标定"""
        print(f"\n=== 使用四频相位展开标定投影仪 {projector_id} ===")
        print(f"像素周期: {self.pixel_periods}")
        
        # 收集展开相位数据
        unwrapped_phase_maps, heights = self.collect_unwrapped_phase_data(projector_id)
        
        if len(unwrapped_phase_maps) == 0:
            print(f"投影仪 {projector_id} 没有有效数据，跳过标定")
            return None
        
        # 计算绝对相位差
        absolute_phase_diffs, heights = self.calculate_absolute_phase_difference(
            unwrapped_phase_maps, reference_position)
        
        if len(absolute_phase_diffs) == 0:
            print(f"投影仪 {projector_id} 绝对相位差计算失败，跳过标定")
            return None
        
        # 提取绝对相位差-高度对
        phase_diffs, height_values = self.extract_absolute_phase_height_pairs(
            absolute_phase_diffs, heights)
        
        if len(phase_diffs) == 0:
            print(f"投影仪 {projector_id} 没有有效的绝对相位差-高度对，跳过标定")
            return None
        
        print(f"提取到 {len(phase_diffs)} 个有效的绝对相位差-高度对")
        
        # 拟合模型
        model_info = self.fit_absolute_phase_model(phase_diffs, height_values)
        
        if model_info is None:
            print(f"投影仪 {projector_id} 模型拟合失败")
            return None
        
        # 保存标定结果
        calibration_result = {
            'projector_id': projector_id,
            'projector_name': self.projector_configs[projector_id]["name"],
            'pixel_periods': self.pixel_periods,
            'reference_position': reference_position,
            'model_info': model_info,
            'unwrapped_phase_maps': unwrapped_phase_maps,
            'absolute_phase_diffs': absolute_phase_diffs,
            'heights': heights,
            'calibration_phase_diffs': phase_diffs,
            'calibration_heights': height_values,
            'method': 'four_frequency_unwrapping'
        }
        
        print(f"\n投影仪 {projector_id} 四频相位展开标定完成!")
        return calibration_result
    
    def calibrate_all_projectors_with_unwrapping(self) -> Dict:
        """使用四频相位展开标定所有投影仪"""
        print("=== 使用四频相位展开标定所有投影仪 ===")
        
        all_results = {}
        
        for projector_id in range(4):
            result = self.calibrate_projector_with_unwrapping(projector_id)
            if result is not None:
                all_results[projector_id] = result
                self.calibration_results[projector_id] = result
        
        print(f"\n四频相位展开标定完成，成功标定 {len(all_results)} 个投影仪")
        return all_results
    
    def predict_height_from_unwrapped_phase(self, current_unwrapped_phase: np.ndarray, 
                                          reference_unwrapped_phase: np.ndarray,
                                          projector_id: int) -> np.ndarray:
        """
        使用展开相位预测高度
        Args:
            current_unwrapped_phase: 当前展开相位图
            reference_unwrapped_phase: 参考展开相位图
            projector_id: 投影仪ID
        Returns:
            height_map: 高度图
        """
        if projector_id not in self.calibration_results:
            raise ValueError(f"投影仪 {projector_id} 未标定")
        
        # 计算绝对相位差
        absolute_phase_diff = current_unwrapped_phase - reference_unwrapped_phase
        
        # 使用标定模型预测高度
        model_info = self.calibration_results[projector_id]['model_info']
        model = model_info['model']
        
        height_map = np.full_like(absolute_phase_diff, np.nan)
        valid_mask = (~np.isnan(current_unwrapped_phase)) & (~np.isnan(reference_unwrapped_phase))
        
        if np.any(valid_mask):
            valid_phase_diffs = absolute_phase_diff[valid_mask]
            X = valid_phase_diffs.reshape(-1, 1)
            predicted_heights = model.predict(X)
            
            flat_heights = np.full_like(absolute_phase_diff.flatten(), np.nan)
            flat_heights[valid_mask.flatten()] = predicted_heights
            height_map = flat_heights.reshape(absolute_phase_diff.shape)
        
        return height_map
    
    def save_calibration_results(self, output_folder: str = "integrated_unwrapping_calibration_results"):
        """保存标定结果"""
        os.makedirs(output_folder, exist_ok=True)
        
        # 保存完整结果
        with open(os.path.join(output_folder, "calibration_results.pkl"), 'wb') as f:
            pickle.dump(self.calibration_results, f)
        
        # 保存简化模型
        simplified_results = {}
        for proj_id, result in self.calibration_results.items():
            model_info = result['model_info']
            simplified_results[proj_id] = {
                'model': model_info['model'],
                'model_type': model_info['model_type'],
                'absolute_phase_diff_range': model_info['absolute_phase_diff_range'],
                'height_range': model_info['height_range'],
                'projector_name': result['projector_name'],
                'pixel_periods': result['pixel_periods'],
                'reference_position': result['reference_position'],
                'method': 'four_frequency_unwrapping'
            }
        
        with open(os.path.join(output_folder, "calibration_models.pkl"), 'wb') as f:
            pickle.dump(simplified_results, f)
        
        # 生成质量报告
        quality_report = self.evaluate_calibration_quality()
        with open(os.path.join(output_folder, "quality_report.json"), 'w', encoding='utf-8') as f:
            json.dump(quality_report, f, indent=2, ensure_ascii=False)
        
        print(f"四频相位展开标定结果已保存到 {output_folder}")
        return quality_report
    
    def evaluate_calibration_quality(self) -> Dict:
        """评估标定质量"""
        if not self.calibration_results:
            return {"error": "没有标定结果"}
        
        quality_report = {
            "method": "four_frequency_unwrapping",
            "pixel_periods": self.pixel_periods,
            "overall_quality": "Unknown",
            "projector_reports": {},
            "recommendations": []
        }
        
        all_r2_scores = []
        all_rmse_values = []
        all_correlations = []
        
        for proj_id, result in self.calibration_results.items():
            model_info = result['model_info']
            r2 = model_info['r2_score']
            rmse = model_info['rmse']
            correlation = model_info['correlation']
            
            all_r2_scores.append(r2)
            all_rmse_values.append(rmse)
            all_correlations.append(abs(correlation))
            
            # 质量评估
            if r2 > 0.95 and rmse < 0.5 and abs(correlation) > 0.95:
                proj_quality = "Excellent"
            elif r2 > 0.90 and rmse < 1.0 and abs(correlation) > 0.90:
                proj_quality = "Good"
            elif r2 > 0.80 and rmse < 2.0 and abs(correlation) > 0.80:
                proj_quality = "Fair"
            else:
                proj_quality = "Poor"
            
            quality_report["projector_reports"][proj_id] = {
                "quality": proj_quality,
                "r2_score": r2,
                "rmse": rmse,
                "correlation": correlation,
                "model_type": model_info['model_type'],
                "sample_count": model_info['n_samples']
            }
        
        # 整体质量
        avg_r2 = np.mean(all_r2_scores)
        avg_rmse = np.mean(all_rmse_values)
        avg_corr = np.mean(all_correlations)
        
        if avg_r2 > 0.95 and avg_rmse < 0.5:
            overall_quality = "Excellent"
        elif avg_r2 > 0.90 and avg_rmse < 1.0:
            overall_quality = "Good"
        elif avg_r2 > 0.80 and avg_rmse < 2.0:
            overall_quality = "Fair"
        else:
            overall_quality = "Poor"
        
        quality_report["overall_quality"] = overall_quality
        quality_report["average_r2"] = avg_r2
        quality_report["average_rmse"] = avg_rmse
        quality_report["average_correlation"] = avg_corr
        quality_report["num_calibrated_projectors"] = len(self.calibration_results)
        
        # 生成建议
        recommendations = []
        if avg_r2 > 0.95:
            recommendations.append("四频相位展开标定质量优秀，可以用于高精度测量")
        elif avg_r2 > 0.90:
            recommendations.append("四频相位展开标定质量良好，适合一般精度测量")
        else:
            recommendations.append("四频相位展开标定质量需要改进，建议检查数据质量")
        
        if avg_rmse < 0.5:
            recommendations.append("测量精度优秀 (RMSE < 0.5mm)")
        elif avg_rmse < 1.0:
            recommendations.append("测量精度良好 (RMSE < 1.0mm)")
        
        recommendations.append("使用了完整的四频五步相移展开算法，获得了绝对相位信息")
        
        quality_report["recommendations"] = recommendations
        
        return quality_report


def test_integrated_unwrapping_calibration():
    """测试集成相位展开的标定方法"""
    print("=== 测试四频相位展开标定方法 ===")
    
    calibrator = IntegratedPhaseUnwrappingCalibration()
    
    # 测试单个投影仪
    result = calibrator.calibrate_projector_with_unwrapping(0)
    
    if result:
        print("四频相位展开标定测试成功!")
        
        # 保存结果
        calibrator.calibration_results[0] = result
        quality_report = calibrator.save_calibration_results("integrated_unwrapping_test_results")
        
        # 打印质量报告
        print("\n质量报告:")
        print(f"标定方法: {quality_report['method']}")
        print(f"像素周期: {quality_report['pixel_periods']}")
        print(f"整体质量: {quality_report['overall_quality']}")
        print(f"平均R²: {quality_report['average_r2']:.6f}")
        print(f"平均RMSE: {quality_report['average_rmse']:.4f} mm")
        print(f"平均相关系数: {quality_report['average_correlation']:.6f}")
        
    else:
        print("四频相位展开标定测试失败")


if __name__ == "__main__":
    test_integrated_unwrapping_calibration()
