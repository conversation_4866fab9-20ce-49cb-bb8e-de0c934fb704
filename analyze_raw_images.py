#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析原始图像数据，检查是否存在高度相关的变化
"""

import numpy as np
import cv2
import os
import matplotlib.pyplot as plt


def analyze_image_intensity_changes():
    """分析图像强度随高度的变化"""
    print("=== 分析图像强度随高度的变化 ===")
    
    # 选择几个代表性的图像进行分析
    test_images = [1, 5, 10, 15, 20]  # 投影仪0的不同频率图像
    positions = [0, 2, 4, 6, 8, 10]  # 不同高度位置
    
    for img_idx in test_images:
        print(f"\n分析图像 {img_idx}:")
        
        intensities_by_position = []
        
        for pos in positions:
            test_folder = os.path.join("标定数据", f"Test-{pos}")
            img_path = os.path.join(test_folder, f"{img_idx}.bmp")
            
            if os.path.exists(img_path):
                img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
                if img is not None:
                    mean_intensity = np.mean(img)
                    std_intensity = np.std(img)
                    intensities_by_position.append(mean_intensity)
                    print(f"  位置 {pos} (高度 {pos}mm): 均值={mean_intensity:.1f}, 标准差={std_intensity:.1f}")
                else:
                    intensities_by_position.append(0)
                    print(f"  位置 {pos}: 图像读取失败")
            else:
                intensities_by_position.append(0)
                print(f"  位置 {pos}: 文件不存在")
        
        # 计算强度变化的相关性
        if len(intensities_by_position) > 1:
            heights = [pos * 1.0 for pos in positions]
            correlation = np.corrcoef(intensities_by_position, heights)[0, 1]
            print(f"  强度-高度相关系数: {correlation:.6f}")
            
            # 计算强度变化范围
            intensity_range = max(intensities_by_position) - min(intensities_by_position)
            print(f"  强度变化范围: {intensity_range:.1f}")


def visualize_image_differences():
    """可视化不同高度位置的图像差异"""
    print("\n=== 可视化图像差异 ===")
    
    # 选择一个图像进行详细分析
    img_idx = 5  # 投影仪0的8像素周期第5张图像
    
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle(f'Image {img_idx} at Different Heights', fontsize=16)
    
    positions = [0, 2, 4, 6, 8, 10]
    
    for i, pos in enumerate(positions):
        row = i // 3
        col = i % 3
        
        test_folder = os.path.join("标定数据", f"Test-{pos}")
        img_path = os.path.join(test_folder, f"{img_idx}.bmp")
        
        if os.path.exists(img_path):
            img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
            if img is not None:
                axes[row, col].imshow(img, cmap='gray')
                axes[row, col].set_title(f'Height {pos}mm')
                axes[row, col].axis('off')
            else:
                axes[row, col].text(0.5, 0.5, 'Read Failed', ha='center', va='center')
                axes[row, col].set_title(f'Height {pos}mm')
        else:
            axes[row, col].text(0.5, 0.5, 'File Not Found', ha='center', va='center')
            axes[row, col].set_title(f'Height {pos}mm')
    
    plt.tight_layout()
    plt.savefig('image_height_comparison.png', dpi=150, bbox_inches='tight')
    plt.show()


def analyze_phase_calculation_directly():
    """直接分析相位计算过程"""
    print("\n=== 直接分析相位计算 ===")
    
    # 选择8像素周期的5张图像进行分析
    img_indices = [1, 2, 3, 4, 5]  # 投影仪0的8像素周期图像
    positions = [0, 5, 10]  # 三个高度位置
    
    for pos in positions:
        print(f"\n分析位置 {pos} (高度 {pos}mm):")
        
        test_folder = os.path.join("标定数据", f"Test-{pos}")
        
        # 加载5张相移图像
        images = []
        for img_idx in img_indices:
            img_path = os.path.join(test_folder, f"{img_idx}.bmp")
            if os.path.exists(img_path):
                img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
                if img is not None:
                    images.append(img.astype(np.float32))
                else:
                    print(f"  图像 {img_idx} 读取失败")
                    images.append(np.zeros((480, 640), dtype=np.float32))
            else:
                print(f"  图像 {img_idx} 不存在")
                images.append(np.zeros((480, 640), dtype=np.float32))
        
        if len(images) == 5:
            I1, I2, I3, I4, I5 = images
            
            # 五步相移算法
            numerator = 2 * (I2 - I4)
            denominator = 2 * I3 - I1 - I5
            
            # 避免除零
            denominator = np.where(np.abs(denominator) < 1e-10, 1e-10, denominator)
            
            # 计算包裹相位
            wrapped_phase = np.arctan2(numerator, denominator)
            
            # 计算调制度
            ac_component = np.sqrt(numerator**2 + denominator**2) / 2
            dc_component = (I1 + I2 + I3 + I4 + I5) / 5
            modulation = np.where(dc_component > 0, ac_component / dc_component, 0)
            
            # 统计信息
            print(f"  包裹相位范围: [{np.min(wrapped_phase):.3f}, {np.max(wrapped_phase):.3f}] rad")
            print(f"  包裹相位均值: {np.mean(wrapped_phase):.3f} rad")
            print(f"  包裹相位标准差: {np.std(wrapped_phase):.3f} rad")
            print(f"  调制度范围: [{np.min(modulation):.3f}, {np.max(modulation):.3f}]")
            print(f"  调制度均值: {np.mean(modulation):.3f}")
            
            # 检查图像质量
            print(f"  图像强度统计:")
            for i, img in enumerate(images, 1):
                print(f"    I{i}: 均值={np.mean(img):.1f}, 标准差={np.std(img):.1f}")


def check_image_naming_and_structure():
    """检查图像命名和结构是否正确"""
    print("\n=== 检查图像命名和结构 ===")
    
    # 检查Test-0文件夹的图像
    test_folder = "标定数据/Test-0"
    
    if not os.path.exists(test_folder):
        print(f"测试文件夹不存在: {test_folder}")
        return
    
    print(f"检查文件夹: {test_folder}")
    
    # 列出所有图像文件
    image_files = []
    for i in range(1, 85):  # 1-84
        img_path = os.path.join(test_folder, f"{i}.bmp")
        if os.path.exists(img_path):
            image_files.append(i)
    
    print(f"找到图像文件: {len(image_files)} 个")
    print(f"图像编号范围: {min(image_files)} - {max(image_files)}")
    
    # 检查投影仪0的图像（1-20）
    print("\n投影仪0的图像 (1-20):")
    proj0_images = [i for i in image_files if 1 <= i <= 20]
    print(f"  找到 {len(proj0_images)} 个图像")
    if len(proj0_images) < 20:
        missing = [i for i in range(1, 21) if i not in proj0_images]
        print(f"  缺失图像: {missing}")
    
    # 检查8像素周期的图像（1-5）
    print("\n8像素周期图像 (1-5):")
    freq8_images = [i for i in proj0_images if 1 <= i <= 5]
    print(f"  找到 {len(freq8_images)} 个图像")
    
    # 检查这些图像的实际内容
    if len(freq8_images) >= 5:
        print("  检查图像内容:")
        for img_idx in freq8_images:
            img_path = os.path.join(test_folder, f"{img_idx}.bmp")
            img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
            if img is not None:
                print(f"    图像 {img_idx}: 尺寸={img.shape}, 均值={np.mean(img):.1f}, 最小值={np.min(img)}, 最大值={np.max(img)}")
            else:
                print(f"    图像 {img_idx}: 读取失败")


def main():
    """主函数"""
    print("原始图像数据分析")
    print("="*50)
    
    # 检查图像结构
    check_image_naming_and_structure()
    
    # 分析强度变化
    analyze_image_intensity_changes()
    
    # 直接分析相位计算
    analyze_phase_calculation_directly()
    
    # 可视化图像差异
    visualize_image_differences()


if __name__ == "__main__":
    main()
