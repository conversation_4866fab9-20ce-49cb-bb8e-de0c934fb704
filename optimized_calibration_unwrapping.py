#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的标定相位展开算法
针对标定场景的特点进行优化
"""

import numpy as np
import cv2
import os
import matplotlib.pyplot as plt
import pickle
import json
from typing import List, Tuple, Dict, Optional
from sklearn.linear_model import LinearRegression, RANSACRegressor
from sklearn.preprocessing import PolynomialFeatures
from sklearn.pipeline import Pipeline
from sklearn.metrics import r2_score, mean_squared_error
import warnings
warnings.filterwarnings('ignore')


class OptimizedCalibrationUnwrapping:
    """优化的标定相位展开类"""
    
    def __init__(self, calibration_data_folder: str = "标定数据"):
        """初始化"""
        self.calibration_data_folder = calibration_data_folder
        
        # 投影仪配置
        self.projector_configs = {
            0: {"name": "Projector_0", "image_range": (1, 20)},
            1: {"name": "Projector_1", "image_range": (21, 40)},
            2: {"name": "Projector_2", "image_range": (41, 60)},
            3: {"name": "Projector_3", "image_range": (61, 80)}
        }
        
        # 频率配置
        self.frequency_configs = {
            8: {"image_indices": [1, 2, 3, 4, 5], "name": "8px_period"},      
            64: {"image_indices": [6, 7, 8, 9, 10], "name": "64px_period"},    
            512: {"image_indices": [11, 12, 13, 14, 15], "name": "512px_period"}, 
            1080: {"image_indices": [16, 17, 18, 19, 20], "name": "1080px_period"} 
        }
        
        self.height_step = 1.0  # mm
        self.num_positions = 11
        self.calibration_results = {}
        
    def load_five_step_images(self, test_folder: str, projector_id: int, frequency: int) -> List[np.ndarray]:
        """加载五步相移图像"""
        start_idx, _ = self.projector_configs[projector_id]["image_range"]
        img_indices = self.frequency_configs[frequency]["image_indices"]
        
        images = []
        for img_idx in img_indices:
            actual_img_idx = start_idx + img_idx - 1
            img_path = os.path.join(test_folder, f"{actual_img_idx}.bmp")
            
            if os.path.exists(img_path):
                img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
                if img is not None:
                    images.append(img.astype(np.float32))
                else:
                    return None
            else:
                return None
        
        return images if len(images) == 5 else None
    
    def calculate_wrapped_phase_and_modulation(self, images: List[np.ndarray]) -> Tuple[np.ndarray, np.ndarray]:
        """计算包裹相位和调制度"""
        if len(images) != 5:
            raise ValueError("需要5张相移图像")
        
        I1, I2, I3, I4, I5 = images
        
        # 五步相移算法
        numerator = 2 * (I2 - I4)
        denominator = 2 * I3 - I1 - I5
        denominator = np.where(np.abs(denominator) < 1e-10, 1e-10, denominator)
        
        wrapped_phase = np.arctan2(numerator, denominator)
        
        # 计算调制度
        ac_component = np.sqrt(numerator**2 + denominator**2) / 2
        dc_component = (I1 + I2 + I3 + I4 + I5) / 5
        modulation = np.where(dc_component > 0, ac_component / dc_component, 0)
        
        return wrapped_phase, modulation
    
    def optimized_two_frequency_unwrapping(self, low_freq_phase: np.ndarray, high_freq_phase: np.ndarray,
                                         low_period: int, high_period: int) -> np.ndarray:
        """
        优化的双频相位展开算法
        Args:
            low_freq_phase: 低频包裹相位
            high_freq_phase: 高频包裹相位
            low_period: 低频周期
            high_period: 高频周期
        Returns:
            unwrapped_phase: 展开相位
        """
        # 计算频率比
        freq_ratio = low_period / high_period
        
        # 计算理论相位差
        theoretical_high_phase = low_freq_phase * freq_ratio
        theoretical_high_wrapped = np.angle(np.exp(1j * theoretical_high_phase))
        
        # 计算相位差
        phase_diff = high_freq_phase - theoretical_high_wrapped
        
        # 展开相位差（处理-π到π的跳跃）
        k = np.round(phase_diff / (2 * np.pi))
        
        # 计算展开的高频相位
        unwrapped_high_phase = high_freq_phase + 2 * np.pi * k
        
        return unwrapped_high_phase
    
    def multi_frequency_unwrapping_optimized(self, wrapped_phases: List[np.ndarray], 
                                           periods: List[int]) -> np.ndarray:
        """
        优化的多频相位展开
        Args:
            wrapped_phases: 包裹相位列表 [低频到高频]
            periods: 对应的周期列表
        Returns:
            unwrapped_phase: 最终展开相位
        """
        if len(wrapped_phases) != len(periods):
            raise ValueError("相位和周期数量不匹配")
        
        # 从最低频开始
        current_unwrapped = wrapped_phases[0].copy()  # 最低频作为基础
        
        # 逐级展开到高频
        for i in range(1, len(wrapped_phases)):
            current_unwrapped = self.optimized_two_frequency_unwrapping(
                current_unwrapped, wrapped_phases[i], periods[i-1], periods[i]
            )
        
        return current_unwrapped
    
    def process_position_with_optimized_unwrapping(self, test_folder: str, projector_id: int) -> Optional[Dict]:
        """
        使用优化算法处理单个位置
        Returns:
            result: 包含各频率相位和最终展开相位的字典
        """
        try:
            print(f"    使用优化相位展开处理...")
            
            # 加载所有频率的图像并计算包裹相位
            frequencies = [1080, 512, 64, 8]  # 从低频到高频
            wrapped_phases = []
            modulations = []
            
            for freq in frequencies:
                images = self.load_five_step_images(test_folder, projector_id, freq)
                if images is None:
                    print(f"    频率 {freq} 图像加载失败")
                    return None
                
                wrapped_phase, modulation = self.calculate_wrapped_phase_and_modulation(images)
                wrapped_phases.append(wrapped_phase)
                modulations.append(modulation)
            
            # 创建质量掩码
            quality_mask = np.ones_like(wrapped_phases[0], dtype=bool)
            for mod in modulations:
                quality_mask &= (mod > 0.05)
            
            # 使用优化的多频展开
            unwrapped_phase = self.multi_frequency_unwrapping_optimized(wrapped_phases, frequencies)
            
            # 应用质量掩码
            unwrapped_phase[~quality_mask] = np.nan
            
            # 输出统计信息
            valid_phase = unwrapped_phase[~np.isnan(unwrapped_phase)]
            if len(valid_phase) > 0:
                print(f"    展开相位范围: [{np.min(valid_phase):.3f}, {np.max(valid_phase):.3f}] rad")
                print(f"    展开相位均值: {np.mean(valid_phase):.3f} rad")
                print(f"    有效像素比例: {len(valid_phase) / unwrapped_phase.size * 100:.1f}%")
                
                # 检查各频率的包裹相位统计
                for i, freq in enumerate(frequencies):
                    valid_wrapped = wrapped_phases[i][quality_mask]
                    if len(valid_wrapped) > 0:
                        print(f"    频率{freq}包裹相位均值: {np.mean(valid_wrapped):.3f} rad")
            else:
                print(f"    没有有效的展开相位")
                return None
            
            return {
                'wrapped_phases': wrapped_phases,
                'modulations': modulations,
                'unwrapped_phase': unwrapped_phase,
                'quality_mask': quality_mask,
                'frequencies': frequencies
            }
            
        except Exception as e:
            print(f"    优化相位展开处理失败: {e}")
            return None
    
    def collect_optimized_unwrapped_data(self, projector_id: int) -> Tuple[List[np.ndarray], List[float]]:
        """收集优化展开相位数据"""
        print(f"正在收集投影仪 {projector_id} 的优化展开相位数据...")
        
        unwrapped_phases = []
        heights = []
        
        for position in range(self.num_positions):
            test_folder = os.path.join(self.calibration_data_folder, f"Test-{position}")
            
            if not os.path.exists(test_folder):
                print(f"警告: 文件夹不存在 {test_folder}")
                continue
            
            print(f"  处理位置 {position} (高度 {position * self.height_step:.1f} mm)...")
            
            result = self.process_position_with_optimized_unwrapping(test_folder, projector_id)
            
            if result is not None:
                unwrapped_phases.append(result['unwrapped_phase'])
                heights.append(position * self.height_step)
            else:
                print(f"    跳过位置 {position}（处理失败）")
        
        print(f"成功收集到 {len(unwrapped_phases)} 个位置的优化展开相位数据")
        return unwrapped_phases, heights
    
    def analyze_phase_height_relationship(self, unwrapped_phases: List[np.ndarray], 
                                        heights: List[float]) -> Dict:
        """分析相位-高度关系"""
        print("分析相位-高度关系...")
        
        # 计算每个位置的平均相位
        mean_phases = []
        std_phases = []
        
        for i, (phase_map, height) in enumerate(zip(unwrapped_phases, heights)):
            valid_phase = phase_map[~np.isnan(phase_map)]
            if len(valid_phase) > 0:
                mean_phase = np.mean(valid_phase)
                std_phase = np.std(valid_phase)
                mean_phases.append(mean_phase)
                std_phases.append(std_phase)
                print(f"  位置 {i} (高度 {height}mm): 平均相位 = {mean_phase:.6f} rad, 标准差 = {std_phase:.6f} rad")
            else:
                mean_phases.append(np.nan)
                std_phases.append(np.nan)
                print(f"  位置 {i} (高度 {height}mm): 无有效相位")
        
        # 计算相位-高度相关性
        valid_indices = ~np.isnan(mean_phases)
        if np.sum(valid_indices) > 1:
            valid_mean_phases = np.array(mean_phases)[valid_indices]
            valid_heights = np.array(heights)[valid_indices]
            
            correlation = np.corrcoef(valid_mean_phases, valid_heights)[0, 1]
            print(f"平均相位-高度相关系数: {correlation:.6f}")
            
            # 计算相位变化范围
            phase_range = np.max(valid_mean_phases) - np.min(valid_mean_phases)
            print(f"相位变化范围: {phase_range:.6f} rad")
            
            return {
                'mean_phases': mean_phases,
                'std_phases': std_phases,
                'correlation': correlation,
                'phase_range': phase_range,
                'valid_positions': np.sum(valid_indices)
            }
        else:
            print("没有足够的有效数据进行相关性分析")
            return None
    
    def enhanced_phase_difference_method(self, unwrapped_phases: List[np.ndarray], 
                                       heights: List[float],
                                       reference_position: int = 0) -> Tuple[List[np.ndarray], List[float]]:
        """
        增强的相位差方法
        使用展开相位计算更准确的相位差
        """
        print(f"计算增强相位差（参考位置: {reference_position}）...")
        
        if reference_position >= len(unwrapped_phases):
            print(f"错误: 参考位置 {reference_position} 超出范围")
            return [], []
        
        reference_phase = unwrapped_phases[reference_position]
        enhanced_phase_diffs = []
        diff_heights = []
        
        for i, unwrapped_phase in enumerate(unwrapped_phases):
            if i == reference_position:
                # 参考位置的相位差为0
                phase_diff = np.zeros_like(reference_phase)
            else:
                # 计算展开相位差
                phase_diff = unwrapped_phase - reference_phase
                
                # 只保留两个图像都有效的像素
                valid_mask = (~np.isnan(unwrapped_phase)) & (~np.isnan(reference_phase))
                phase_diff[~valid_mask] = np.nan
            
            enhanced_phase_diffs.append(phase_diff)
            diff_heights.append(heights[i])
            
            # 输出统计信息
            valid_diff = phase_diff[~np.isnan(phase_diff)]
            if len(valid_diff) > 0:
                print(f"  位置 {i}: 增强相位差均值 = {np.mean(valid_diff):.6f} rad, 标准差 = {np.std(valid_diff):.6f} rad")
            else:
                print(f"  位置 {i}: 无有效增强相位差")
        
        return enhanced_phase_diffs, diff_heights
    
    def extract_enhanced_phase_height_pairs(self, enhanced_phase_diffs: List[np.ndarray], 
                                          heights: List[float],
                                          sample_ratio: float = 0.001) -> Tuple[np.ndarray, np.ndarray]:
        """提取增强相位差-高度数据对"""
        all_phase_diffs = []
        all_heights = []
        
        for phase_diff, height in zip(enhanced_phase_diffs, heights):
            valid_mask = ~np.isnan(phase_diff)
            valid_diffs = phase_diff[valid_mask]
            
            if len(valid_diffs) > 0:
                # 随机采样
                n_samples = max(1000, int(len(valid_diffs) * sample_ratio))
                if len(valid_diffs) > n_samples:
                    indices = np.random.choice(len(valid_diffs), n_samples, replace=False)
                    sampled_diffs = valid_diffs[indices]
                else:
                    sampled_diffs = valid_diffs
                
                all_phase_diffs.extend(sampled_diffs)
                all_heights.extend([height] * len(sampled_diffs))
        
        return np.array(all_phase_diffs), np.array(all_heights)
    
    def fit_enhanced_phase_model(self, phase_diffs: np.ndarray, heights: np.ndarray) -> Dict:
        """拟合增强相位差-高度模型"""
        print(f"拟合增强相位差-高度模型，数据点数: {len(phase_diffs)}")
        
        # 数据预处理
        phase_std = np.std(phase_diffs)
        phase_mean = np.mean(phase_diffs)
        valid_mask = np.abs(phase_diffs - phase_mean) < 3 * phase_std
        
        clean_phase_diffs = phase_diffs[valid_mask]
        clean_heights = heights[valid_mask]
        
        print(f"数据清理后: {len(clean_phase_diffs)} 个数据点")
        
        if len(clean_phase_diffs) < 100:
            print("警告: 有效数据点太少")
            return None
        
        # 计算相关系数
        correlation = np.corrcoef(clean_phase_diffs, clean_heights)[0, 1]
        print(f"增强相位差-高度相关系数: {correlation:.6f}")
        
        # 尝试不同模型
        models = [
            ('linear', LinearRegression()),
            ('polynomial_2', Pipeline([
                ('poly', PolynomialFeatures(degree=2)),
                ('linear', LinearRegression())
            ])),
            ('robust_linear', RANSACRegressor(
                LinearRegression(),
                min_samples=100,
                residual_threshold=0.5,
                random_state=42
            ))
        ]
        
        best_model = None
        best_score = -np.inf
        best_name = ""
        
        X = clean_phase_diffs.reshape(-1, 1)
        
        for model_name, model in models:
            try:
                model.fit(X, clean_heights)
                y_pred = model.predict(X)
                r2 = r2_score(clean_heights, y_pred)
                rmse = np.sqrt(mean_squared_error(clean_heights, y_pred))
                
                print(f"{model_name} 模型: R² = {r2:.6f}, RMSE = {rmse:.4f} mm")
                
                if r2 > best_score:
                    best_model = model
                    best_score = r2
                    best_name = model_name
                    
            except Exception as e:
                print(f"{model_name} 模型拟合失败: {e}")
        
        if best_model is None:
            print("所有模型拟合都失败")
            return None
        
        # 使用最佳模型计算最终结果
        y_pred = best_model.predict(X)
        r2 = r2_score(clean_heights, y_pred)
        rmse = np.sqrt(mean_squared_error(clean_heights, y_pred))
        
        # 计算残差统计
        residuals = clean_heights - y_pred
        residual_std = np.std(residuals)
        
        model_info = {
            'model': best_model,
            'model_type': best_name,
            'r2_score': r2,
            'rmse': rmse,
            'residual_std': residual_std,
            'correlation': correlation,
            'n_samples': len(clean_phase_diffs),
            'enhanced_phase_diff_range': (np.min(clean_phase_diffs), np.max(clean_phase_diffs)),
            'height_range': (np.min(clean_heights), np.max(clean_heights))
        }
        
        print(f"最佳模型: {best_name}")
        print(f"  相关系数: {correlation:.6f}")
        print(f"  R² 分数: {r2:.6f}")
        print(f"  RMSE: {rmse:.4f} mm")
        print(f"  残差标准差: {residual_std:.4f} mm")
        
        return model_info
    
    def calibrate_projector_with_enhanced_unwrapping(self, projector_id: int, 
                                                   reference_position: int = 0) -> Dict:
        """使用增强相位展开进行标定"""
        print(f"\n=== 使用增强相位展开标定投影仪 {projector_id} ===")
        
        # 收集优化展开相位数据
        unwrapped_phases, heights = self.collect_optimized_unwrapped_data(projector_id)
        
        if len(unwrapped_phases) == 0:
            print(f"投影仪 {projector_id} 没有有效数据，跳过标定")
            return None
        
        # 分析相位-高度关系
        analysis_result = self.analyze_phase_height_relationship(unwrapped_phases, heights)
        
        if analysis_result is None or abs(analysis_result['correlation']) < 0.1:
            print("相位-高度相关性太低，尝试增强相位差方法...")
            
            # 使用增强相位差方法
            enhanced_phase_diffs, diff_heights = self.enhanced_phase_difference_method(
                unwrapped_phases, heights, reference_position)
            
            if len(enhanced_phase_diffs) == 0:
                print(f"投影仪 {projector_id} 增强相位差计算失败，跳过标定")
                return None
            
            # 提取增强相位差-高度对
            phase_diffs, height_values = self.extract_enhanced_phase_height_pairs(
                enhanced_phase_diffs, diff_heights)
            
            if len(phase_diffs) == 0:
                print(f"投影仪 {projector_id} 没有有效的增强相位差-高度对，跳过标定")
                return None
            
            print(f"提取到 {len(phase_diffs)} 个有效的增强相位差-高度对")
            
            # 拟合模型
            model_info = self.fit_enhanced_phase_model(phase_diffs, height_values)
            
            if model_info is None:
                print(f"投影仪 {projector_id} 模型拟合失败")
                return None
            
            # 保存标定结果
            calibration_result = {
                'projector_id': projector_id,
                'projector_name': self.projector_configs[projector_id]["name"],
                'reference_position': reference_position,
                'model_info': model_info,
                'unwrapped_phases': unwrapped_phases,
                'enhanced_phase_diffs': enhanced_phase_diffs,
                'heights': heights,
                'calibration_phase_diffs': phase_diffs,
                'calibration_heights': height_values,
                'method': 'enhanced_unwrapping',
                'analysis_result': analysis_result
            }
            
            print(f"\n投影仪 {projector_id} 增强相位展开标定完成!")
            return calibration_result
        
        else:
            print("相位-高度关系良好，可以直接使用展开相位进行标定")
            # 这里可以添加直接使用展开相位的标定方法
            return None


def test_optimized_unwrapping_calibration():
    """测试优化相位展开标定方法"""
    print("=== 测试优化相位展开标定方法 ===")
    
    calibrator = OptimizedCalibrationUnwrapping()
    
    # 测试单个投影仪
    result = calibrator.calibrate_projector_with_enhanced_unwrapping(0)
    
    if result:
        print("优化相位展开标定测试成功!")
        
        # 保存结果
        calibrator.calibration_results[0] = result
        
        # 简单的质量评估
        model_info = result['model_info']
        print(f"\n质量评估:")
        print(f"  方法: {result['method']}")
        print(f"  R² 分数: {model_info['r2_score']:.6f}")
        print(f"  RMSE: {model_info['rmse']:.4f} mm")
        print(f"  相关系数: {model_info['correlation']:.6f}")
        
        if model_info['r2_score'] > 0.9:
            print("  质量: 优秀")
        elif model_info['r2_score'] > 0.8:
            print("  质量: 良好")
        else:
            print("  质量: 需要改进")
            
    else:
        print("优化相位展开标定测试失败")


if __name__ == "__main__":
    test_optimized_unwrapping_calibration()
