#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于最终标定结果的数据重建与融合程序
使用final_calibration_results_four_freq中的TIFF映射表
"""

import numpy as np
import cv2
import os
import matplotlib.pyplot as plt
from pathlib import Path
from typing import List, Tuple, Dict, Optional
import argparse
from tifffile import imread as tiff_imread
from scipy import stats

# 导入相位展开算法
from four_freq_phase_unwrap import five_step_phase, unwrap_multifrequency


class FinalCalibrationFusion:
    """基于最终标定结果的融合器"""
    
    def __init__(self, calibration_folder: str = "final_calibration_results_four_freq"):
        """
        初始化融合器
        
        参数:
            calibration_folder: 标定结果文件夹路径
        """
        self.calibration_folder = Path(calibration_folder)
        self.mapping_tables_folder = self.calibration_folder / "mapping_tables"
        
        # 相位展开参数 - 四频
        self.periods = [1080.0, 512.0, 64.0, 8.0]  # 低频到高频
        
        # 投影仪配置：每个投影仪对应的图像编号范围
        self.projector_configs = {
            0: {"name": "DLP-0", "image_range": (1, 20)},
            1: {"name": "DLP-1", "image_range": (21, 40)},
            2: {"name": "DLP-2", "image_range": (41, 60)},
            3: {"name": "DLP-3", "image_range": (61, 80)}
        }
        
        # 频率配置：每个频率对应的5张相移图像
        self.frequency_configs = {
            8: {"image_indices": [1, 2, 3, 4, 5], "freq_idx": 3},      # 高频
            64: {"image_indices": [6, 7, 8, 9, 10], "freq_idx": 2},    
            512: {"image_indices": [11, 12, 13, 14, 15], "freq_idx": 1}, 
            1080: {"image_indices": [16, 17, 18, 19, 20], "freq_idx": 0} # 低频
        }
        
        # 加载映射表
        self.mapping_tables = {}
        self.load_mapping_tables()
        
        # CCD参数 - 与generate_point_cloud.py保持一致
        self.CCD_DX = 0.0154  # mm/pixel (相机像素的物理尺寸)
        
    def load_mapping_tables(self):
        """加载所有投影仪的多项式映射表"""
        print("加载TIFF映射表...")
        
        for proj_id in range(4):
            proj_name = f"DLP-{proj_id}"
            self.mapping_tables[proj_id] = {}
            
            # 加载多项式系数（通常是3次多项式：常数项、一次项、二次项、三次项）
            for degree in range(3):  # 0, 1, 2 对应常数项、一次项、二次项
                tiff_file = self.mapping_tables_folder / f"{proj_name}-{degree}.tiff"
                
                if tiff_file.exists():
                    try:
                        coeff_map = tiff_imread(str(tiff_file)).astype(np.float64)
                        self.mapping_tables[proj_id][degree] = coeff_map
                        print(f"  已加载 {proj_name}-{degree}.tiff, 形状: {coeff_map.shape}")
                    except Exception as e:
                        print(f"  警告: 无法加载 {tiff_file}: {e}")
                        self.mapping_tables[proj_id][degree] = None
                else:
                    print(f"  警告: 映射表文件不存在: {tiff_file}")
                    self.mapping_tables[proj_id][degree] = None
            
        print(f"映射表加载完成，支持 {len(self.mapping_tables)} 个投影仪")
    
    def load_phase_shift_images(self, test_folder: str, projector_id: int) -> Dict[int, List[np.ndarray]]:
        """
        加载指定投影仪的所有频率相移图像
        
        参数:
            test_folder: 测试文件夹路径
            projector_id: 投影仪ID (0-3)
        
        返回:
            images_by_frequency: 按频率组织的图像字典
        """
        test_path = Path(test_folder)
        start_idx, _ = self.projector_configs[projector_id]["image_range"]
        images_by_frequency = {}
        
        for frequency, config in self.frequency_configs.items():
            frequency_images = []
            for img_idx in config["image_indices"]:
                actual_img_idx = start_idx + img_idx - 1
                img_path = test_path / f"{actual_img_idx}.bmp"
                
                if img_path.exists():
                    try:
                        img = cv2.imread(str(img_path), cv2.IMREAD_GRAYSCALE)
                        if img is not None:
                            frequency_images.append(img.astype(np.float64))
                        else:
                            print(f"警告: 无法读取图像 {img_path}")
                            return None
                    except Exception as e:
                        print(f"警告: 读取图像失败 {img_path}: {e}")
                        return None
                else:
                    print(f"警告: 图像文件不存在 {img_path}")
                    return None
            
            if len(frequency_images) == 5:
                images_by_frequency[frequency] = frequency_images
            else:
                print(f"警告: 投影仪 {projector_id} 频率 {frequency} 的图像数量不足")
                return None
        
        return images_by_frequency
    
    def phase_unwrapping_four_frequency(self, images_by_frequency: Dict[int, List[np.ndarray]]) -> Tuple[np.ndarray, Dict]:
        """
        四频相位展开
        
        参数:
            images_by_frequency: 按频率组织的图像字典
        
        返回:
            unwrapped_phase: 展开相位
            phase_info: 相位信息
        """
        try:
            # 按低频到高频的顺序组织图像
            freq_order = [1080, 512, 64, 8]
            frames_list = []
            
            for freq in freq_order:
                if freq in images_by_frequency:
                    freq_images = images_by_frequency[freq]
                    frames_array = np.stack(freq_images, axis=-1)  # (H, W, 5)
                    frames_list.append(frames_array)
                else:
                    raise ValueError(f"缺少频率 {freq} 的图像")
            
            # 先计算包裹相位
            wrapped_phases = []
            for frames_array in frames_list:
                wrapped_phase = five_step_phase(frames_array)
                wrapped_phases.append(wrapped_phase)
            
            # 使用四频展开算法
            unwrapped_phases = unwrap_multifrequency(wrapped_phases, self.periods)
            unwrapped_phase = unwrapped_phases[-1]  # 最高频的展开相位
            
            phase_info = {
                "periods": self.periods,
                "wrapped_phases": wrapped_phases,
                "unwrapped_phases": unwrapped_phases,
                "valid_pixels": np.sum(np.isfinite(unwrapped_phase))
            }
            
            return unwrapped_phase, phase_info
            
        except Exception as e:
            print(f"相位展开失败: {e}")
            return None, {"error": str(e)}
    
    def phase_to_height_polynomial(self, phase: np.ndarray, projector_id: int) -> np.ndarray:
        """
        使用多项式映射将相位转换为高度
        
        参数:
            phase: 展开相位
            projector_id: 投影仪ID
        
        返回:
            height_map: 高度图
        """
        if projector_id not in self.mapping_tables:
            raise ValueError(f"投影仪 {projector_id} 的映射表未加载")
        
        mapping_table = self.mapping_tables[projector_id]
        
        # 检查映射表完整性
        if 0 not in mapping_table or mapping_table[0] is None:
            raise ValueError(f"投影仪 {projector_id} 缺少常数项映射表")
        
        # 获取映射表尺寸
        ref_shape = mapping_table[0].shape
        
        # 调整相位图尺寸以匹配映射表
        if phase.shape != ref_shape:
            phase_resized = cv2.resize(phase, (ref_shape[1], ref_shape[0]), 
                                     interpolation=cv2.INTER_LINEAR)
        else:
            phase_resized = phase.copy()
        
        # 计算多项式：h = c0 + c1*φ + c2*φ² + c3*φ³
        height_map = np.zeros_like(phase_resized)
        
        # 常数项
        if 0 in mapping_table and mapping_table[0] is not None:
            height_map += mapping_table[0]
        
        # 一次项
        if 1 in mapping_table and mapping_table[1] is not None:
            height_map += mapping_table[1] * phase_resized
        
        # 二次项
        if 2 in mapping_table and mapping_table[2] is not None:
            height_map += mapping_table[2] * (phase_resized ** 2)
        
        # 三次项（如果存在）
        if 3 in mapping_table and mapping_table[3] is not None:
            height_map += mapping_table[3] * (phase_resized ** 3)
        
        # 处理无效值
        valid_phase = np.isfinite(phase_resized)
        height_map[~valid_phase] = np.nan
        
        return height_map
    
    def analyze_depth_quality(self, depth_map: np.ndarray, projector_id: int) -> Dict:
        """分析深度图质量"""
        total_pixels = depth_map.size
        valid_mask = np.isfinite(depth_map)
        valid_count = np.sum(valid_mask)
        valid_ratio = valid_count / total_pixels
        
        quality_info = {
            "projector_id": projector_id,
            "total_pixels": total_pixels,
            "valid_pixels": valid_count,
            "valid_ratio": valid_ratio,
            "is_valid": valid_ratio >= 0.1  # 至少10%有效像素
        }
        
        if valid_count > 100:  # 需要足够的有效像素进行统计
            valid_depths = depth_map[valid_mask]
            
            # 基本统计
            depth_stats = {
                "min": np.min(valid_depths),
                "max": np.max(valid_depths),
                "mean": np.mean(valid_depths),
                "std": np.std(valid_depths),
                "median": np.median(valid_depths),
                "range": np.max(valid_depths) - np.min(valid_depths)
            }
            
            # 异常值检测
            if len(valid_depths) > 10:
                z_scores = np.abs(stats.zscore(valid_depths))
                outlier_mask = z_scores > 3.0
                outlier_ratio = np.sum(outlier_mask) / len(valid_depths)
            else:
                outlier_ratio = 0
            
            # 质量评分
            quality_score = self.calculate_quality_score(depth_stats, outlier_ratio, valid_ratio)
            
            quality_info.update({
                "depth_stats": depth_stats,
                "outlier_ratio": outlier_ratio,
                "quality_score": quality_score
            })
        else:
            quality_info.update({
                "depth_stats": None,
                "outlier_ratio": 1.0,
                "quality_score": 0.0
            })
        
        return quality_info
    
    def calculate_quality_score(self, depth_stats: Dict, outlier_ratio: float, valid_ratio: float) -> float:
        """计算质量分数"""
        # 有效比例权重
        score = valid_ratio * 0.4
        
        # 稳定性权重（标准差）
        if depth_stats["std"] > 0:
            stability_score = max(0, 1 - depth_stats["std"] / 10)  # 10mm为最大可接受标准差
            score += stability_score * 0.3
        
        # 异常值权重
        outlier_score = max(0, 1 - outlier_ratio * 2)
        score += outlier_score * 0.3
        
        return min(score, 1.0)
    
    def reconstruct_single_projector(self, test_folder: str, projector_id: int) -> Tuple[np.ndarray, Dict]:
        """重建单个投影仪的深度图"""
        print(f"\n重建投影仪 {projector_id} ({self.projector_configs[projector_id]['name']})...")
        
        try:
            # 1. 加载图像
            images_by_frequency = self.load_phase_shift_images(test_folder, projector_id)
            if images_by_frequency is None:
                return None, {"projector_id": projector_id, "error": "图像加载失败", "is_valid": False}
            
            # 2. 相位展开
            unwrapped_phase, phase_info = self.phase_unwrapping_four_frequency(images_by_frequency)
            if unwrapped_phase is None:
                return None, {"projector_id": projector_id, "error": "相位展开失败", "is_valid": False}
            
            # 3. 相位转高度
            height_map = self.phase_to_height_polynomial(unwrapped_phase, projector_id)
            
            # 4. 质量分析
            quality_info = self.analyze_depth_quality(height_map, projector_id)
            quality_info["phase_info"] = phase_info
            
            print(f"  完成重建: 有效比例 {quality_info['valid_ratio']:.1%}, "
                  f"质量分数 {quality_info['quality_score']:.3f}")
            
            return height_map, quality_info
            
        except Exception as e:
            print(f"  重建失败: {e}")
            return None, {"projector_id": projector_id, "error": str(e), "is_valid": False}
    
    def adaptive_fusion(self, depth_maps: List[np.ndarray], 
                       quality_infos: List[Dict],
                       depth_threshold: float = 0.5) -> Tuple[np.ndarray, Dict]:
        """
        自适应融合算法 - 集成20微米精细异常检测
        
        参数:
            depth_maps: 深度图列表
            quality_infos: 质量信息列表
            depth_threshold: 深度差异阈值 (mm) - 默认使用20微米精细检测
        """
        print(f"\n执行自适应融合（集成20微米精细异常检测）...")
        
        # 使用20微米精细阈值（单位：mm）
        FINE_THRESHOLD = 0.02  # 20微米 = 0.02毫米
        COARSE_THRESHOLD = depth_threshold  # 用户指定的粗糙阈值
        
        # 筛选有效的投影仪数据
        valid_projectors = []
        valid_depth_maps = []
        
        for i, (depth_map, quality_info) in enumerate(zip(depth_maps, quality_infos)):
            if depth_map is not None and quality_info.get("is_valid", False):
                valid_projectors.append(i)
                valid_depth_maps.append(depth_map)
        
        fusion_info = {
            "total_projectors": len(depth_maps),
            "valid_projectors": valid_projectors,
            "valid_count": len(valid_projectors),
            "fine_threshold_20um": FINE_THRESHOLD,
            "coarse_threshold": COARSE_THRESHOLD
        }
        
        print(f"  有效投影仪: {valid_projectors} ({len(valid_projectors)}/{len(depth_maps)})")
        print(f"  精细阈值: {FINE_THRESHOLD*1000:.0f}微米")
        print(f"  粗糙阈值: {COARSE_THRESHOLD*1000:.0f}微米")
        
        if len(valid_projectors) == 0:
            print("  错误: 没有有效的投影仪数据")
            fusion_info["fusion_strategy"] = "failed"
            return np.full((2848, 2848), np.nan), fusion_info
        
        elif len(valid_projectors) == 1:
            print(f"  策略: 使用单投影仪数据 (投影仪 {valid_projectors[0]})")
            fusion_info["fusion_strategy"] = "single_projector"
            fused_depth = valid_depth_maps[0].copy()
        
        elif len(valid_projectors) == 2:
            print(f"  策略: 双投影仪20微米精细融合")
            fusion_info["fusion_strategy"] = "dual_projector_20um"
            fused_depth = self.fuse_two_depths_20um_threshold(
                valid_depth_maps[0], valid_depth_maps[1], FINE_THRESHOLD)
        
        else:
            print(f"  策略: 多投影仪20微米精细融合")
            fusion_info["fusion_strategy"] = "multi_projector_20um"
            fused_depth = self.fuse_multiple_depths_20um_threshold(
                valid_depth_maps, FINE_THRESHOLD)
        
        # 统计融合结果
        valid_mask = np.isfinite(fused_depth)
        fusion_info.update({
            "final_valid_pixels": np.sum(valid_mask),
            "final_coverage": np.sum(valid_mask) / fused_depth.size
        })
        
        if np.sum(valid_mask) > 0:
            valid_depths = fused_depth[valid_mask]
            fusion_info["final_depth_stats"] = {
                "min": np.min(valid_depths),
                "max": np.max(valid_depths),
                "mean": np.mean(valid_depths),
                "std": np.std(valid_depths),
                "range": np.max(valid_depths) - np.min(valid_depths)
            }
        
        return fused_depth, fusion_info
    
    def fuse_two_depths_20um_threshold(self, depth1: np.ndarray, depth2: np.ndarray, 
                                      threshold: float = 0.02) -> np.ndarray:
        """双投影仪融合 - 20微米阈值精细异常检测"""
        print(f"    执行双投影仪20微米精细融合...")
        
        valid1 = np.isfinite(depth1)
        valid2 = np.isfinite(depth2)
        
        fused = np.full_like(depth1, np.nan)
        
        # 两个都有效的位置
        both_valid = valid1 & valid2
        both_valid_count = np.sum(both_valid)
        
        if both_valid_count > 0:
            # 计算深度差异
            depth_diff = np.abs(depth1[both_valid] - depth2[both_valid])
            
            # 检查哪些位置的差异超过20微米阈值
            abnormal_mask = depth_diff > threshold
            normal_mask = ~abnormal_mask
            
            abnormal_count = np.sum(abnormal_mask)
            normal_count = np.sum(normal_mask)
            
            print(f"      两个投影仪都有效: {both_valid_count:,} 像素")
            print(f"      深度差异正常(<20微米): {normal_count:,} 像素 ({normal_count/both_valid_count:.1%})")
            print(f"      深度差异异常(>20微米): {abnormal_count:,} 像素 ({abnormal_count/both_valid_count:.1%})")
            
            # 创建索引数组来映射回原始位置
            both_valid_indices = np.where(both_valid)
            
            # 正常位置：取平均值
            if normal_count > 0:
                normal_indices = (both_valid_indices[0][normal_mask], both_valid_indices[1][normal_mask])
                fused[normal_indices] = (depth1[normal_indices] + depth2[normal_indices]) / 2
            
            # 异常位置：去掉异常值，保留更合理的那个
            if abnormal_count > 0:
                abnormal_indices = (both_valid_indices[0][abnormal_mask], both_valid_indices[1][abnormal_mask])
                
                # 使用局部中位数策略：选择距离局部中位数更近的值
                depth1_abnormal = depth1[abnormal_indices]
                depth2_abnormal = depth2[abnormal_indices]
                
                # 计算局部中位数（使用周围正常区域）
                if normal_count > 100:
                    local_median = np.median(fused[np.isfinite(fused)])
                else:
                    # 如果正常区域太少，使用全局中位数
                    combined_depths = np.concatenate([depth1_abnormal, depth2_abnormal])
                    local_median = np.median(combined_depths)
                
                diff1_to_median = np.abs(depth1_abnormal - local_median)
                diff2_to_median = np.abs(depth2_abnormal - local_median)
                
                # 选择距离中位数更近的值
                choose_depth1 = diff1_to_median <= diff2_to_median
                
                fused[abnormal_indices] = np.where(choose_depth1, depth1_abnormal, depth2_abnormal)
                
                print(f"      异常位置处理: 保留投影仪1 {np.sum(choose_depth1)} 个，投影仪2 {np.sum(~choose_depth1)} 个")
        
        # 只有一个有效的位置：直接使用该值
        only1_valid = valid1 & (~valid2)
        only2_valid = valid2 & (~valid1)
        
        fused[only1_valid] = depth1[only1_valid]
        fused[only2_valid] = depth2[only2_valid]
        
        print(f"      仅投影仪1有效: {np.sum(only1_valid):,} 像素")
        print(f"      仅投影仪2有效: {np.sum(only2_valid):,} 像素")
        
        return fused
    
    def fuse_multiple_depths_20um_threshold(self, depth_maps: List[np.ndarray], 
                                          threshold: float = 0.02) -> np.ndarray:
        """多投影仪融合（3个或4个）- 20微米阈值精细异常检测"""
        print(f"    执行多投影仪20微米精细融合...")
        
        if len(depth_maps) < 3:
            raise ValueError("此方法需要至少3个深度图")
        
        depth_stack = np.stack(depth_maps, axis=0)  # (N, H, W)
        valid_mask = np.isfinite(depth_stack)  # (N, H, W)
        valid_count = np.sum(valid_mask, axis=0)  # (H, W)
        
        fused = np.full_like(depth_maps[0], np.nan)
        
        height, width = depth_maps[0].shape
        
        # 统计信息
        processed_pixels = 0
        interpolated_pixels = 0
        outlier_removed_count = 0
        fine_agreement_count = 0
        
        for r in range(0, height, 4):  # 使用步长优化性能
            for c in range(0, width, 4):
                # 处理4x4块以提高效率
                r_end = min(r + 4, height)
                c_end = min(c + 4, width)
                
                for rr in range(r, r_end):
                    for cc in range(c, c_end):
                        pixel_valid_count = valid_count[rr, cc]
                        
                        if pixel_valid_count == 0:
                            # 没有有效数据，保持NaN
                            continue
                        elif pixel_valid_count == 1:
                            # 只有一个有效值，直接使用
                            for proj_idx in range(len(depth_maps)):
                                if valid_mask[proj_idx, rr, cc]:
                                    fused[rr, cc] = depth_stack[proj_idx, rr, cc]
                                    break
                            processed_pixels += 1
                        else:
                            # 多个有效值，执行20微米精细检测
                            valid_depths = depth_stack[valid_mask[:, rr, cc], rr, cc]
                            
                            if len(valid_depths) == 2:
                                # 两个值：检查20微米差异
                                depth_diff = abs(valid_depths[0] - valid_depths[1])
                                if depth_diff <= threshold:
                                    # 差异在20微米内，取平均
                                    fused[rr, cc] = np.mean(valid_depths)
                                    fine_agreement_count += 1
                                else:
                                    # 差异超过20微米，选择更合理的值
                                    median_val = np.median(valid_depths)
                                    diff_to_median = np.abs(valid_depths - median_val)
                                    fused[rr, cc] = valid_depths[np.argmin(diff_to_median)]
                                    outlier_removed_count += 1
                            else:
                                # 三个或更多值：检查是否有异常值
                                median_depth = np.median(valid_depths)
                                depth_diffs = np.abs(valid_depths - median_depth)
                                
                                # 找出差异在20微米内的值
                                normal_mask = depth_diffs <= threshold
                                normal_depths = valid_depths[normal_mask]
                                
                                if len(normal_depths) >= len(valid_depths) * 0.5:  # 至少一半正常
                                    # 有足够正常值，使用正常值的平均
                                    fused[rr, cc] = np.mean(normal_depths)
                                    if len(normal_depths) == len(valid_depths):
                                        fine_agreement_count += 1
                                    else:
                                        outlier_removed_count += 1
                                else:
                                    # 异常值太多，使用插值
                                    interpolated_value = self.interpolate_from_neighbors_fine(
                                        fused, rr, cc, height, width)
                                    if np.isfinite(interpolated_value):
                                        fused[rr, cc] = interpolated_value
                                        interpolated_pixels += 1
                                    else:
                                        # 插值失败，使用中位数
                                        fused[rr, cc] = median_depth
                            
                            processed_pixels += 1
        
        print(f"      处理像素: {processed_pixels:,}")
        print(f"      20微米内一致: {fine_agreement_count:,} 位置")
        print(f"      移除异常值: {outlier_removed_count:,} 位置") 
        print(f"      插值处理: {interpolated_pixels:,} 位置")
        
        return fused
    
    def interpolate_from_neighbors_fine(self, depth_map: np.ndarray, row: int, col: int, 
                                      height: int, width: int, window_size: int = 3) -> float:
        """从周边像素插值 - 精细版本"""
        # 定义较小的邻域窗口以保持精度
        half_window = window_size // 2
        
        r_start = max(0, row - half_window)
        r_end = min(height, row + half_window + 1)
        c_start = max(0, col - half_window)
        c_end = min(width, col + half_window + 1)
        
        # 提取邻域
        neighborhood = depth_map[r_start:r_end, c_start:c_end]
        
        # 找出有效的邻居值
        valid_neighbors = neighborhood[np.isfinite(neighborhood)]
        
        if len(valid_neighbors) >= 2:
            # 使用邻居的中位数，更稳定
            return np.median(valid_neighbors)
        elif len(valid_neighbors) == 1:
            # 只有一个邻居
            return valid_neighbors[0]
        else:
            # 没有有效邻居
                         return np.nan
    
    def fuse_two_depths(self, depth1: np.ndarray, depth2: np.ndarray, threshold: float) -> np.ndarray:
        """兼容性函数：调用20微米精细融合"""
        return self.fuse_two_depths_20um_threshold(depth1, depth2, threshold)
    
    def fuse_multiple_depths(self, depth_maps: List[np.ndarray], threshold: float) -> np.ndarray:
        """兼容性函数：调用20微米精细融合"""
        if len(depth_maps) < 2:
            return depth_maps[0] if depth_maps else np.full((2848, 2848), np.nan)
        elif len(depth_maps) == 2:
            return self.fuse_two_depths_20um_threshold(depth_maps[0], depth_maps[1], threshold)
        else:
            return self.fuse_multiple_depths_20um_threshold(depth_maps, threshold)
    
    def save_results(self, fused_depth: np.ndarray, 
                    fusion_info: Dict,
                    quality_infos: List[Dict],
                    output_folder: str):
        """保存融合结果"""
        output_path = Path(output_folder)
        output_path.mkdir(exist_ok=True)
        
        # 保存深度图
        depth_file = output_path / "fused_depth.npy"
        np.save(depth_file, fused_depth)
        print(f"\n融合深度图已保存: {depth_file}")
        
        # 保存融合信息
        info_file = output_path / "fusion_info.txt"
        with open(info_file, 'w', encoding='utf-8') as f:
            f.write("基于最终标定结果的数据融合报告\n")
            f.write("="*50 + "\n\n")
            
            f.write(f"融合策略: {fusion_info['fusion_strategy']}\n")
            f.write(f"有效投影仪: {fusion_info['valid_projectors']} ({fusion_info['valid_count']}/{fusion_info['total_projectors']})\n")
            f.write(f"深度阈值: {fusion_info['coarse_threshold']*1000:.0f}微米 (精细阈值: {fusion_info['fine_threshold_20um']*1000:.0f}微米)\n\n")
            
            f.write("各投影仪质量分析:\n")
            for quality_info in quality_infos:
                proj_id = quality_info["projector_id"]
                if "error" in quality_info:
                    f.write(f"  投影仪 {proj_id}: 失败 - {quality_info['error']}\n")
                elif quality_info.get("is_valid", False):
                    f.write(f"  投影仪 {proj_id}: 有效比例 {quality_info['valid_ratio']:.1%}, "
                           f"质量分数 {quality_info['quality_score']:.3f}\n")
                else:
                    f.write(f"  投影仪 {proj_id}: 无效 - 有效数据不足\n")
            
            f.write(f"\n融合结果:\n")
            f.write(f"  最终有效像素: {fusion_info['final_valid_pixels']:,}\n")
            f.write(f"  最终覆盖率: {fusion_info['final_coverage']:.2%}\n")
            
            if "final_depth_stats" in fusion_info:
                stats = fusion_info["final_depth_stats"]
                f.write(f"\n深度统计 (mm):\n")
                f.write(f"  范围: {stats['min']:.2f} ~ {stats['max']:.2f}\n")
                f.write(f"  均值: {stats['mean']:.2f}\n")
                f.write(f"  标准差: {stats['std']:.3f}\n")
                f.write(f"  深度范围: {stats['range']:.2f}\n")
        
        print(f"融合信息已保存: {info_file}")
    
    def save_point_cloud(self, depth_map: np.ndarray, 
                        test_folder: str,
                        output_file: str) -> int:
        """保存点云"""
        height, width = depth_map.shape
        
        # 创建坐标网格
        x_coords = np.arange(width, dtype=np.float32) * self.CCD_DX
        y_coords = np.arange(height, dtype=np.float32) * self.CCD_DX
        x_grid, y_grid = np.meshgrid(x_coords, y_coords)
        
        # 创建点云
        points = np.stack((x_grid.ravel(), y_grid.ravel(), depth_map.ravel()), axis=-1)
        
        # 过滤有效点
        valid_mask = np.isfinite(depth_map) & (depth_map > -10) & (depth_map < 50)
        valid_mask = valid_mask.ravel()
        filtered_points = points[valid_mask]
        
        if len(filtered_points) == 0:
            print("警告: 没有有效的点云数据")
            return 0
        
        # 加载颜色纹理
        colors = self.load_color_texture(test_folder, height, width)
        if colors is not None:
            filtered_colors = colors.reshape(-1, 3)[valid_mask]
        else:
            filtered_colors = np.full((len(filtered_points), 3), [128, 128, 128], dtype=np.uint8)
        
        # 保存为PLY格式
        self.save_ply(output_file, filtered_points, filtered_colors)
        
        return len(filtered_points)
    
    def load_color_texture(self, test_folder: str, height: int, width: int) -> Optional[np.ndarray]:
        """加载彩色纹理"""
        try:
            test_path = Path(test_folder)
            
            # 加载RGB通道（假设81-83是颜色通道）
            blue_path = test_path / "81.bmp"
            green_path = test_path / "82.bmp"
            red_path = test_path / "83.bmp"
            
            if all(p.exists() for p in [blue_path, green_path, red_path]):
                b = cv2.imread(str(blue_path), cv2.IMREAD_GRAYSCALE)
                g = cv2.imread(str(green_path), cv2.IMREAD_GRAYSCALE)
                r = cv2.imread(str(red_path), cv2.IMREAD_GRAYSCALE)
                
                if all(img is not None for img in [b, g, r]):
                    # 调整尺寸匹配深度图
                    if b.shape != (height, width):
                        b = cv2.resize(b, (width, height))
                        g = cv2.resize(g, (width, height))
                        r = cv2.resize(r, (width, height))
                    
                    color_texture = np.stack([r, g, b], axis=-1)  # RGB格式
                    return color_texture
            
            print("警告: 无法加载完整的颜色纹理，使用默认颜色")
            return None
            
        except Exception as e:
            print(f"加载颜色纹理失败: {e}")
            return None
    
    def save_ply(self, filename: str, points: np.ndarray, colors: np.ndarray):
        """保存PLY格式点云"""
        with open(filename, 'w') as f:
            # PLY文件头
            f.write("ply\n")
            f.write("format ascii 1.0\n")
            f.write(f"element vertex {len(points)}\n")
            f.write("property float x\n")
            f.write("property float y\n")
            f.write("property float z\n")
            f.write("property uchar red\n")
            f.write("property uchar green\n")
            f.write("property uchar blue\n")
            f.write("end_header\n")
            
            # 点云数据
            for i in range(len(points)):
                x, y, z = points[i]
                r, g, b = colors[i]
                f.write(f"{x:.6f} {y:.6f} {z:.6f} {r} {g} {b}\n")
        
        print(f"点云已保存: {filename}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="基于最终标定结果的数据重建与融合")
    parser.add_argument("--test_folder", type=str, default="测试/pcb", help="测试数据文件夹")
    parser.add_argument("--calibration_folder", type=str, default="final_calibration_results_four_freq", help="标定结果文件夹")
    parser.add_argument("--output_folder", type=str, default="final_fusion_results", help="输出文件夹")
    parser.add_argument("--depth_threshold", type=float, default=0.5, help="深度融合阈值(mm)")
    parser.add_argument("--use_20um_detection", action="store_true", help="强制使用20微米精细异常检测")
    
    args = parser.parse_args()
    
    # 创建融合器
    print("="*60)
    print("基于最终标定结果的数据重建与融合程序")
    print("="*60)
    
    fusion_engine = FinalCalibrationFusion(args.calibration_folder)
    
    # 创建输出文件夹
    output_path = Path(args.output_folder)
    output_path.mkdir(exist_ok=True)
    
    try:
        # 重建所有投影仪的深度图
        print(f"\n开始从测试数据重建深度图: {args.test_folder}")
        
        depth_maps = []
        quality_infos = []
        
        for proj_id in range(4):
            depth_map, quality_info = fusion_engine.reconstruct_single_projector(args.test_folder, proj_id)
            depth_maps.append(depth_map)
            quality_infos.append(quality_info)
        
        # 执行自适应融合
        fused_depth, fusion_info = fusion_engine.adaptive_fusion(
            depth_maps, quality_infos, args.depth_threshold)
        
        # 保存结果
        fusion_engine.save_results(fused_depth, fusion_info, quality_infos, args.output_folder)
        
        # 保存点云
        point_cloud_file = output_path / "fused_point_cloud.ply"
        point_count = fusion_engine.save_point_cloud(
            fused_depth, args.test_folder, str(point_cloud_file))
        
        # 打印总结
        print(f"\n{'='*60}")
        print("融合完成！")
        print(f"{'='*60}")
        print(f"融合策略: {fusion_info['fusion_strategy']}")
        print(f"有效投影仪: {fusion_info['valid_projectors']}")
        print(f"最终覆盖率: {fusion_info['final_coverage']:.2%}")
        print(f"点云点数: {point_count:,}")
        print(f"结果保存在: {args.output_folder}")
        
    except Exception as e:
        print(f"\n融合过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 