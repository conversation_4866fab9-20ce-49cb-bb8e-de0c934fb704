#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版融合程序 - 单光机深度图异常值排查改进
提供更严格的质量评估和更精确的异常值检测
"""

import numpy as np
import cv2
from scipy import stats, ndimage
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt

class EnhancedDepthQualityAnalyzer:
    """增强版深度图质量分析器"""
    
    def __init__(self):
        # 严格的质量评估参数
        self.min_valid_ratio = 0.3          # 提高到30%有效像素
        self.min_valid_pixels = 5000        # 提高最小有效像素数
        self.z_score_threshold = 2.5        # 降低Z-score阈值提高敏感度
        self.max_std_threshold = 3.0        # 降低标准差阈值到3mm
        self.gradient_threshold = 5.0       # 深度梯度异常阈值(mm/pixel)
        self.local_window_size = 21         # 局部异常检测窗口大小
        self.depth_range_limit = 50.0       # 合理深度范围限制(mm)
        
    def enhanced_depth_quality_analysis(self, depth_map: np.ndarray, projector_id: int) -> Dict:
        """增强版深度图质量分析"""
        print(f"    执行增强版质量分析 (投影仪 {projector_id})...")
        
        total_pixels = depth_map.size
        valid_mask = np.isfinite(depth_map)
        valid_count = np.sum(valid_mask)
        valid_ratio = valid_count / total_pixels
        
        # 基础质量信息
        quality_info = {
            "projector_id": projector_id,
            "total_pixels": total_pixels,
            "valid_pixels": valid_count,
            "valid_ratio": valid_ratio,
            "enhanced_analysis": True
        }
        
        # 严格的有效性检查
        is_valid_basic = (valid_ratio >= self.min_valid_ratio and 
                         valid_count >= self.min_valid_pixels)
        
        if not is_valid_basic:
            quality_info.update({
                "is_valid": False,
                "rejection_reason": f"有效像素不足: {valid_ratio:.1%} < {self.min_valid_ratio:.1%} 或 {valid_count} < {self.min_valid_pixels}",
                "quality_score": 0.0,
                "enhanced_score": 0.0
            })
            return quality_info
        
        # 进行详细分析
        valid_depths = depth_map[valid_mask]
        
        # 1. 基本统计分析
        depth_stats = self._compute_depth_statistics(valid_depths)
        
        # 2. 多层次异常值检测
        outlier_analysis = self._enhanced_outlier_detection(depth_map, valid_mask, valid_depths)
        
        # 3. 空间连续性分析
        spatial_analysis = self._spatial_continuity_analysis(depth_map)
        
        # 4. 物理合理性检查
        physical_analysis = self._physical_reasonableness_check(valid_depths, depth_stats)
        
        # 5. 综合质量评分
        quality_scores = self._comprehensive_quality_scoring(
            depth_stats, outlier_analysis, spatial_analysis, physical_analysis, valid_ratio)
        
        # 最终有效性判断
        is_valid_enhanced = self._final_validity_check(quality_scores, outlier_analysis)
        
        quality_info.update({
            "is_valid": is_valid_enhanced,
            "depth_stats": depth_stats,
            "outlier_analysis": outlier_analysis,
            "spatial_analysis": spatial_analysis,
            "physical_analysis": physical_analysis,
            "quality_score": quality_scores["basic_score"],      # 兼容原版
            "enhanced_score": quality_scores["enhanced_score"],  # 新增强化评分
            "quality_scores": quality_scores,
            "rejection_reason": None if is_valid_enhanced else quality_scores.get("rejection_reason", "综合质量不达标")
        })
        
        print(f"      基础评分: {quality_scores['basic_score']:.3f}")
        print(f"      增强评分: {quality_scores['enhanced_score']:.3f}")
        print(f"      最终结果: {'通过' if is_valid_enhanced else '拒绝'}")
        
        return quality_info
    
    def _compute_depth_statistics(self, valid_depths: np.ndarray) -> Dict:
        """计算深度统计信息"""
        return {
            "min": np.min(valid_depths),
            "max": np.max(valid_depths),
            "mean": np.mean(valid_depths),
            "std": np.std(valid_depths),
            "median": np.median(valid_depths),
            "q25": np.percentile(valid_depths, 25),
            "q75": np.percentile(valid_depths, 75),
            "iqr": np.percentile(valid_depths, 75) - np.percentile(valid_depths, 25),
            "range": np.max(valid_depths) - np.min(valid_depths),
            "mad": np.median(np.abs(valid_depths - np.median(valid_depths)))  # 中位数绝对偏差
        }
    
    def _enhanced_outlier_detection(self, depth_map: np.ndarray, valid_mask: np.ndarray, 
                                   valid_depths: np.ndarray) -> Dict:
        """增强版异常值检测"""
        
        # 1. 全局Z-score检测（更严格）
        z_scores = np.abs(stats.zscore(valid_depths))
        global_outliers = z_scores > self.z_score_threshold
        global_outlier_ratio = np.sum(global_outliers) / len(valid_depths)
        
        # 2. IQR异常值检测
        q25, q75 = np.percentile(valid_depths, [25, 75])
        iqr = q75 - q25
        iqr_lower = q25 - 1.5 * iqr
        iqr_upper = q75 + 1.5 * iqr
        iqr_outliers = (valid_depths < iqr_lower) | (valid_depths > iqr_upper)
        iqr_outlier_ratio = np.sum(iqr_outliers) / len(valid_depths)
        
        # 3. 局部异常值检测
        local_outlier_ratio = self._local_outlier_detection(depth_map, valid_mask)
        
        # 4. 综合异常值评估
        combined_outliers = global_outliers | iqr_outliers
        combined_outlier_ratio = np.sum(combined_outliers) / len(valid_depths)
        
        return {
            "global_z_outlier_ratio": global_outlier_ratio,
            "iqr_outlier_ratio": iqr_outlier_ratio,
            "local_outlier_ratio": local_outlier_ratio,
            "combined_outlier_ratio": combined_outlier_ratio,
            "outlier_mask": combined_outliers,
            "z_score_threshold": self.z_score_threshold,
            "severe_outliers": global_outlier_ratio > 0.15,  # 15%以上认为严重
            "moderate_outliers": combined_outlier_ratio > 0.1  # 10%以上认为中等
        }
    
    def _local_outlier_detection(self, depth_map: np.ndarray, valid_mask: np.ndarray) -> float:
        """局部异常值检测"""
        height, width = depth_map.shape
        local_outlier_count = 0
        checked_pixels = 0
        
        # 使用滑动窗口检测局部异常
        half_window = self.local_window_size // 2
        
        for r in range(half_window, height - half_window, self.local_window_size):
            for c in range(half_window, width - half_window, self.local_window_size):
                if valid_mask[r, c]:
                    # 提取局部窗口
                    window = depth_map[r-half_window:r+half_window+1, 
                                     c-half_window:c+half_window+1]
                    window_valid = valid_mask[r-half_window:r+half_window+1,
                                            c-half_window:c+half_window+1]
                    
                    if np.sum(window_valid) > 10:  # 窗口内有足够有效像素
                        window_depths = window[window_valid]
                        window_median = np.median(window_depths)
                        center_depth = depth_map[r, c]
                        
                        # 检查中心像素是否为局部异常
                        if abs(center_depth - window_median) > 2 * np.std(window_depths):
                            local_outlier_count += 1
                    
                    checked_pixels += 1
        
        return local_outlier_count / max(checked_pixels, 1)
    
    def _spatial_continuity_analysis(self, depth_map: np.ndarray) -> Dict:
        """空间连续性分析"""
        
        # 计算深度梯度
        grad_y, grad_x = np.gradient(depth_map)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        
        # 只分析有效区域的梯度
        valid_mask = np.isfinite(depth_map)
        if np.sum(valid_mask) == 0:
            return {"gradient_analysis": "无有效数据"}
        
        valid_gradients = gradient_magnitude[valid_mask]
        
        # 梯度异常检测
        high_gradient_ratio = np.sum(valid_gradients > self.gradient_threshold) / len(valid_gradients)
        
        # 空间一致性评估
        consistency_score = max(0, 1 - high_gradient_ratio * 3)  # 梯度异常越多，一致性越差
        
        return {
            "mean_gradient": np.mean(valid_gradients),
            "max_gradient": np.max(valid_gradients),
            "high_gradient_ratio": high_gradient_ratio,
            "spatial_consistency": consistency_score,
            "gradient_threshold": self.gradient_threshold,
            "smooth_surface": high_gradient_ratio < 0.05  # 5%以下认为表面平滑
        }
    
    def _physical_reasonableness_check(self, valid_depths: np.ndarray, depth_stats: Dict) -> Dict:
        """物理合理性检查"""
        
        # 深度范围检查
        range_reasonable = depth_stats["range"] <= self.depth_range_limit
        
        # 分布合理性检查
        mean_median_diff = abs(depth_stats["mean"] - depth_stats["median"])
        skewness = stats.skew(valid_depths)
        kurtosis = stats.kurtosis(valid_depths)
        
        # 分布健康性评估
        distribution_healthy = (
            abs(skewness) < 2.0 and          # 偏度合理
            abs(kurtosis) < 7.0 and          # 峰度合理
            mean_median_diff < depth_stats["std"]  # 均值中位数差异合理
        )
        
        # 标准差合理性
        std_reasonable = depth_stats["std"] <= self.max_std_threshold
        
        return {
            "depth_range_reasonable": range_reasonable,
            "depth_range": depth_stats["range"],
            "std_reasonable": std_reasonable,
            "distribution_healthy": distribution_healthy,
            "skewness": skewness,
            "kurtosis": kurtosis,
            "mean_median_diff": mean_median_diff,
            "physical_score": sum([range_reasonable, distribution_healthy, std_reasonable]) / 3.0
        }
    
    def _comprehensive_quality_scoring(self, depth_stats: Dict, outlier_analysis: Dict,
                                     spatial_analysis: Dict, physical_analysis: Dict,
                                     valid_ratio: float) -> Dict:
        """综合质量评分"""
        
        # 1. 基础评分（兼容原版）
        basic_score = self._calculate_basic_score(depth_stats, outlier_analysis["combined_outlier_ratio"], valid_ratio)
        
        # 2. 增强评分（更全面）
        # 有效性权重 (30%)
        validity_score = valid_ratio * 0.3
        
        # 稳定性权重 (25%) - 更严格的标准差评估
        stability_score = max(0, 1 - depth_stats["std"] / self.max_std_threshold) * 0.25
        
        # 异常值权重 (20%) - 多维度异常值评估
        outlier_score = max(0, 1 - outlier_analysis["combined_outlier_ratio"] * 2.5) * 0.2
        
        # 空间连续性权重 (15%)
        spatial_score = spatial_analysis.get("spatial_consistency", 0) * 0.15
        
        # 物理合理性权重 (10%)
        physical_score = physical_analysis["physical_score"] * 0.1
        
        enhanced_score = validity_score + stability_score + outlier_score + spatial_score + physical_score
        
        # 质量等级评估
        if enhanced_score >= 0.8:
            quality_grade = "优秀"
        elif enhanced_score >= 0.65:
            quality_grade = "良好"
        elif enhanced_score >= 0.5:
            quality_grade = "一般"
        else:
            quality_grade = "较差"
        
        return {
            "basic_score": basic_score,
            "enhanced_score": enhanced_score,
            "validity_score": validity_score,
            "stability_score": stability_score,
            "outlier_score": outlier_score,
            "spatial_score": spatial_score,
            "physical_score": physical_score,
            "quality_grade": quality_grade,
            "score_breakdown": {
                "有效性": f"{validity_score:.3f} (30%)",
                "稳定性": f"{stability_score:.3f} (25%)",
                "异常值": f"{outlier_score:.3f} (20%)",
                "空间性": f"{spatial_score:.3f} (15%)",
                "物理性": f"{physical_score:.3f} (10%)"
            }
        }
    
    def _calculate_basic_score(self, depth_stats: Dict, outlier_ratio: float, valid_ratio: float) -> float:
        """计算基础评分（兼容原版）"""
        score = valid_ratio * 0.4
        
        if depth_stats["std"] > 0:
            stability_score = max(0, 1 - depth_stats["std"] / self.max_std_threshold)
            score += stability_score * 0.3
        
        outlier_score = max(0, 1 - outlier_ratio * 2)
        score += outlier_score * 0.3
        
        return min(score, 1.0)
    
    def _final_validity_check(self, quality_scores: Dict, outlier_analysis: Dict) -> bool:
        """最终有效性检查"""
        
        # 增强评分阈值
        score_pass = quality_scores["enhanced_score"] >= 0.5  # 提高到0.5
        
        # 异常值检查
        outlier_pass = not outlier_analysis["severe_outliers"]  # 不能有严重异常值
        
        # 综合判断
        final_pass = score_pass and outlier_pass
        
        if not final_pass:
            if not score_pass:
                quality_scores["rejection_reason"] = f"综合评分过低: {quality_scores['enhanced_score']:.3f} < 0.5"
            elif not outlier_pass:
                quality_scores["rejection_reason"] = f"异常值过多: {outlier_analysis['combined_outlier_ratio']:.1%} > 15%"
        
        return final_pass

# 参数调整建议
ENHANCED_PARAMETERS = {
    "质量评估参数": {
        "min_valid_ratio": "0.1 → 0.3 (10% → 30%)",
        "min_valid_pixels": "100 → 5000",
        "z_score_threshold": "3.0 → 2.5",
        "max_std_threshold": "10mm → 3mm"
    },
    
    "新增检测功能": {
        "局部异常检测": "21x21滑动窗口",
        "梯度异常检测": "5mm/pixel阈值",
        "IQR异常检测": "四分位距方法",
        "物理合理性检查": "深度范围、分布健康性"
    },
    
    "质量评分改进": {
        "权重调整": "有效性30% + 稳定性25% + 异常值20% + 空间性15% + 物理性10%",
        "多维度评估": "基础评分 + 增强评分",
        "严格阈值": "enhanced_score ≥ 0.5 才通过"
    }
}

def print_improvement_analysis():
    """打印改进分析报告"""
    print("=" * 80)
    print("🔍 单光机深度图异常值排查改进分析")
    print("=" * 80)
    
    print("\n📊 当前问题总结:")
    print("1. 质量评估阈值过于宽松 (10%有效像素就通过)")
    print("2. 异常值检测方法单一 (仅Z-score)")
    print("3. 缺少空间连续性检查")
    print("4. 物理合理性验证不足")
    print("5. 质量评分权重不合理")
    
    print("\n🚀 改进方案要点:")
    for category, improvements in ENHANCED_PARAMETERS.items():
        print(f"\n▶ {category}:")
        for item, detail in improvements.items():
            print(f"  • {item}: {detail}")
    
    print("\n📈 预期改进效果:")
    print("✅ 提高融合精度: 通过严格的单光机质量控制")
    print("✅ 减少异常值: 多层次异常检测机制")
    print("✅ 增强鲁棒性: 物理合理性和空间连续性检查")
    print("✅ 智能评分: 综合多维度质量评估")
    print("✅ 保持兼容: 原有接口和参数保持可用")
    
    print("\n💡 实施建议:")
    print("1. 逐步部署: 先测试增强评分，再启用严格阈值")
    print("2. 参数可调: 根据实际数据调整阈值参数")
    print("3. 性能平衡: 局部检测可选择性启用")
    print("4. 质量监控: 增加详细的质量分析报告")
    
    print("=" * 80)

if __name__ == "__main__":
    print_improvement_analysis() 