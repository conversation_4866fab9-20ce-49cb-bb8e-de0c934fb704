"""
practical_phase_unwrap.py
=========================

实用的相位展开算法改进版本
专注于实际效果而非理论完美

作者：AI Assistant
日期：2025-07-05
"""

import numpy as np
import cv2
from scipy import ndimage
from typing import List, Tu<PERSON>, Dict
from four_freq_phase_unwrap import five_step_phase, unwrap_multifrequency

class PracticalPhaseUnwrapper:
    """实用的相位展开器"""
    
    def __init__(self, periods: List[float] = None):
        """初始化实用相位展开器"""
        self.periods = periods if periods is not None else [1080.0, 512.0, 64.0, 8.0]
    
    def enhanced_five_step_phase(self, frames: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        增强的五步相移算法
        
        参数:
            frames: 五步相移图像
            
        返回:
            wrapped_phase: 包裹相位
            modulation: 调制度
        """
        # 使用原始算法计算相位
        wrapped_phase = five_step_phase(frames)
        
        # 计算调制度
        I0, I1, I2, I3, I4 = [frames[..., i] for i in range(5)]
        A = 2.0 * (I1 - I3)
        B = I0 - 2.0 * I2 + I4
        modulation = np.sqrt(A**2 + B**2) / 2.0
        
        return wrapped_phase, modulation
    
    def robust_unwrap_multifrequency(self, 
                                   wrapped_phases: List[np.ndarray],
                                   modulations: List[np.ndarray]) -> List[np.ndarray]:
        """
        鲁棒的多频相位展开
        
        参数:
            wrapped_phases: 包裹相位列表
            modulations: 调制度列表
            
        返回:
            unwrapped_phases: 展开相位列表
        """
        # 使用原始展开算法作为基础
        unwrapped_phases = unwrap_multifrequency(wrapped_phases, self.periods)
        
        # 对每个频率进行后处理
        for i in range(len(unwrapped_phases)):
            unwrapped_phases[i] = self._post_process_phase(
                unwrapped_phases[i], modulations[i])
        
        return unwrapped_phases
    
    def _post_process_phase(self, phase: np.ndarray, modulation: np.ndarray) -> np.ndarray:
        """相位后处理"""
        processed_phase = phase.copy()
        
        # 基于调制度的质量控制
        # 使用动态阈值而不是固定阈值
        if np.sum(modulation > 0) > 0:
            mod_threshold = np.percentile(modulation[modulation > 0], 10)  # 使用10%分位数
            mod_threshold = max(mod_threshold, 0.01)  # 最小阈值
        else:
            mod_threshold = 0.01
        
        low_quality_mask = modulation < mod_threshold
        
        # 对低质量区域进行平滑处理
        if np.sum(low_quality_mask) > 0:
            # 使用高斯滤波平滑低质量区域
            smoothed = cv2.GaussianBlur(processed_phase.astype(np.float32), (5, 5), 1.0)
            processed_phase[low_quality_mask] = smoothed[low_quality_mask]
        
        return processed_phase
    
    def detect_and_fix_unwrap_errors(self, phase: np.ndarray, 
                                   modulation: np.ndarray) -> np.ndarray:
        """检测和修复展开错误"""
        fixed_phase = phase.copy()
        
        # 检测大的相位跳跃
        grad_x = cv2.Sobel(phase, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(phase, cv2.CV_64F, 0, 1, ksize=3)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        
        # 动态阈值：基于梯度分布
        valid_gradients = gradient_magnitude[np.isfinite(gradient_magnitude)]
        if len(valid_gradients) > 0:
            grad_threshold = np.percentile(valid_gradients, 95)
            grad_threshold = max(grad_threshold, np.pi)  # 至少π的阈值
        else:
            grad_threshold = np.pi
        
        # 检测错误区域
        error_mask = gradient_magnitude > grad_threshold
        
        # 修复错误：使用中值滤波
        if np.sum(error_mask) > 0:
            # 膨胀错误区域
            kernel = np.ones((3, 3), np.uint8)
            expanded_error = cv2.dilate(error_mask.astype(np.uint8), kernel, iterations=1)
            
            # 对错误区域应用中值滤波
            median_filtered = cv2.medianBlur(fixed_phase.astype(np.float32), 5)
            fixed_phase[expanded_error.astype(bool)] = median_filtered[expanded_error.astype(bool)]
        
        return fixed_phase
    
    def unwrap_four_frequency_practical(self, frames_list: List[np.ndarray]) -> Tuple[np.ndarray, Dict]:
        """
        实用的四频相位展开
        
        参数:
            frames_list: 四个频率的图像组
            
        返回:
            final_phase: 最终相位
            info: 处理信息
        """
        print("开始实用相位展开算法...")
        
        # 1. 计算包裹相位和调制度
        print("1. 计算包裹相位和调制度...")
        wrapped_phases = []
        modulations = []
        
        for i, frames in enumerate(frames_list):
            wrapped, mod = self.enhanced_five_step_phase(frames)
            wrapped_phases.append(wrapped)
            modulations.append(mod)
            
            # 统计有效像素（使用更宽松的标准）
            valid_count = np.sum((mod > 0.005) & np.isfinite(wrapped))
            total_count = wrapped.size
            print(f"  频率 {i} (周期 {self.periods[i]:.0f}px): 有效像素 {valid_count:,} ({valid_count/total_count:.1%})")
        
        # 2. 鲁棒相位展开
        print("2. 执行鲁棒相位展开...")
        unwrapped_phases = self.robust_unwrap_multifrequency(wrapped_phases, modulations)
        
        # 3. 错误检测和修复
        print("3. 检测和修复展开错误...")
        final_phase = self.detect_and_fix_unwrap_errors(
            unwrapped_phases[-1], modulations[-1])
        
        # 4. 最终清理
        print("4. 最终清理...")
        final_phase = self._final_cleanup(final_phase, modulations[-1])
        
        # 生成信息
        info = self._generate_info(wrapped_phases, modulations, final_phase)
        
        print("实用相位展开完成！")
        return final_phase, info
    
    def _final_cleanup(self, phase: np.ndarray, modulation: np.ndarray) -> np.ndarray:
        """最终清理"""
        cleaned_phase = phase.copy()
        
        # 只对极低质量的区域设为NaN
        extremely_low_quality = modulation < 0.001
        cleaned_phase[extremely_low_quality] = np.nan
        
        # 对剩余的NaN值进行插值
        nan_mask = ~np.isfinite(cleaned_phase)
        if np.sum(nan_mask) > 0 and np.sum(~nan_mask) > 0:
            # 使用OpenCV的inpaint进行插值
            mask_uint8 = nan_mask.astype(np.uint8) * 255
            try:
                inpainted = cv2.inpaint(cleaned_phase.astype(np.float32), 
                                      mask_uint8, 3, cv2.INPAINT_TELEA)
                cleaned_phase[nan_mask] = inpainted[nan_mask]
            except:
                # 如果inpaint失败，使用简单的邻域平均
                cleaned_phase = self._simple_interpolate(cleaned_phase, nan_mask)
        
        return cleaned_phase
    
    def _simple_interpolate(self, phase: np.ndarray, nan_mask: np.ndarray) -> np.ndarray:
        """简单插值"""
        interpolated = phase.copy()
        height, width = phase.shape
        
        # 多次迭代填充
        for iteration in range(5):
            updated = False
            for i in range(1, height-1):
                for j in range(1, width-1):
                    if nan_mask[i, j]:
                        # 检查8邻域
                        neighbors = []
                        for di in [-1, 0, 1]:
                            for dj in [-1, 0, 1]:
                                if di == 0 and dj == 0:
                                    continue
                                ni, nj = i + di, j + dj
                                if not nan_mask[ni, nj] and np.isfinite(interpolated[ni, nj]):
                                    neighbors.append(interpolated[ni, nj])
                        
                        if len(neighbors) >= 3:
                            interpolated[i, j] = np.median(neighbors)
                            nan_mask[i, j] = False
                            updated = True
            
            if not updated:
                break
        
        return interpolated
    
    def _generate_info(self, wrapped_phases: List[np.ndarray], 
                      modulations: List[np.ndarray], 
                      final_phase: np.ndarray) -> Dict:
        """生成处理信息"""
        total_pixels = final_phase.size
        final_valid = np.isfinite(final_phase)
        final_valid_count = np.sum(final_valid)
        
        # 统计每个频率
        freq_stats = []
        for i, (wrapped, mod) in enumerate(zip(wrapped_phases, modulations)):
            valid_count = np.sum((mod > 0.005) & np.isfinite(wrapped))
            freq_stats.append({
                "frequency_idx": i,
                "period": self.periods[i],
                "valid_pixels": valid_count,
                "coverage": valid_count / total_pixels,
                "avg_modulation": np.mean(mod[mod > 0.005]) if np.sum(mod > 0.005) > 0 else 0
            })
        
        info = {
            "total_pixels": total_pixels,
            "frequency_stats": freq_stats,
            "final_valid_pixels": final_valid_count,
            "final_coverage": final_valid_count / total_pixels,
            "final_phase_range": {
                "min": np.min(final_phase[final_valid]) if final_valid_count > 0 else 0,
                "max": np.max(final_phase[final_valid]) if final_valid_count > 0 else 0,
                "mean": np.mean(final_phase[final_valid]) if final_valid_count > 0 else 0
            }
        }
        
        return info
    
    def print_info(self, info: Dict):
        """打印处理信息"""
        print("\n=== 实用相位展开统计信息 ===")
        print(f"总像素数: {info['total_pixels']:,}")
        
        print("\n各频率统计:")
        for stat in info["frequency_stats"]:
            print(f"  频率 {stat['frequency_idx']} (周期 {stat['period']:.0f}px):")
            print(f"    有效像素: {stat['valid_pixels']:,} ({stat['coverage']:.1%})")
            print(f"    平均调制度: {stat['avg_modulation']:.3f}")
        
        print(f"\n最终结果:")
        print(f"  有效像素: {info['final_valid_pixels']:,}")
        print(f"  覆盖率: {info['final_coverage']:.1%}")
        
        phase_range = info['final_phase_range']
        print(f"  相位范围: {phase_range['min']:.3f} ~ {phase_range['max']:.3f}")
        print(f"  平均相位: {phase_range['mean']:.3f}")
        print("============================\n")
