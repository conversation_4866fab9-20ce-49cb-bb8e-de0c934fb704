"""
depth_domain_correction.py
==========================

深度域离散点修正程序

专门针对深度域的离散点和异常值进行检测和修正

功能：
1. 深度域异常检测
2. 离散点识别
3. 空间连续性修正
4. 表面平滑优化

作者：AI Assistant
日期：2025-07-05
"""

import numpy as np
import cv2
from pathlib import Path
import argparse
import time
from typing import Dict, Tuple
from scipy import ndimage
from scipy.spatial import cKDTree
import matplotlib.pyplot as plt

class DepthDomainCorrector:
    """深度域修正器"""
    
    def __init__(self, calibration_prefix: str = "fast_improved_calibration"):
        self.calibration_prefix = calibration_prefix
        self.num_engines = 4
        
        # 加载标定参数
        self.calibration_params = self.load_calibration_parameters()
        
        # 深度域修正参数
        self.correction_params = {
            # 离散点检测参数
            "isolation_radius": 5,  # 孤立点检测半径
            "min_neighbors": 3,  # 最小邻居数
            "depth_jump_threshold": 5.0,  # 深度跳跃阈值(mm)
            
            # 统计异常检测参数
            "outlier_z_threshold": 3.0,  # Z-score阈值
            "local_window_size": 11,  # 局部窗口大小
            
            # 表面连续性参数
            "surface_smoothness_threshold": 2.0,  # 表面平滑度阈值
            "gradient_threshold": 1.0,  # 梯度阈值
            
            # 修正参数
            "max_correction_iterations": 3,  # 最大修正迭代次数
            "interpolation_method": "rbf",  # 插值方法
            "smoothing_sigma": 1.0,  # 平滑参数
        }
    
    def load_calibration_parameters(self) -> Dict:
        """加载标定参数"""
        print("加载标定参数...")
        
        calibration_params = {}
        
        for engine_idx in range(self.num_engines):
            slope_file = f"{self.calibration_prefix}_engine{engine_idx}_slope.npy"
            intercept_file = f"{self.calibration_prefix}_engine{engine_idx}_intercept.npy"
            
            if Path(slope_file).exists() and Path(intercept_file).exists():
                slope = np.load(slope_file)
                intercept = np.load(intercept_file)
                calibration_params[engine_idx] = {"slope": slope, "intercept": intercept}
                print(f"  ✓ 光机 {engine_idx}")
            else:
                print(f"  ✗ 光机 {engine_idx}: 标定文件不存在")
        
        return calibration_params
    
    def load_phases(self, phases_dir: str) -> Dict[int, np.ndarray]:
        """加载相位数据"""
        print("加载相位数据...")
        
        phases_path = Path(phases_dir)
        phases = {}
        
        for engine_idx in range(self.num_engines):
            # 尝试加载增强修正的相位
            enhanced_file = phases_path / f"enhanced_corrected_phase_engine{engine_idx}.npy"
            corrected_file = phases_path / f"corrected_unwrapped_phase_engine{engine_idx}.npy"
            original_file = phases_path / f"original_unwrapped_phase_engine{engine_idx}.npy"
            
            phase_file = None
            if enhanced_file.exists():
                phase_file = enhanced_file
                print(f"  ✓ 光机 {engine_idx}: 使用增强修正相位")
            elif corrected_file.exists():
                phase_file = corrected_file
                print(f"  ✓ 光机 {engine_idx}: 使用修正相位")
            elif original_file.exists():
                phase_file = original_file
                print(f"  ✓ 光机 {engine_idx}: 使用原始相位")
            
            if phase_file:
                phase = np.load(phase_file)
                phases[engine_idx] = phase
                
                valid_mask = np.isfinite(phase)
                valid_count = np.sum(valid_mask)
                total_pixels = valid_mask.size
                
                print(f"    形状: {phase.shape}, 有效像素: {valid_count:,} ({valid_count/total_pixels:.1%})")
        
        return phases
    
    def convert_phase_to_depth(self, phase: np.ndarray, engine_idx: int) -> np.ndarray:
        """将相位转换为深度"""
        if engine_idx not in self.calibration_params:
            return None
        
        slope = self.calibration_params[engine_idx]["slope"]
        intercept = self.calibration_params[engine_idx]["intercept"]
        
        depth_map = slope * phase + intercept
        
        # 保持NaN值
        valid_mask = np.isfinite(phase)
        depth_map[~valid_mask] = np.nan
        
        return depth_map
    
    def detect_isolated_points(self, depth_map: np.ndarray) -> np.ndarray:
        """检测孤立点"""
        print("    检测孤立点...")
        
        valid_mask = np.isfinite(depth_map)
        if np.sum(valid_mask) == 0:
            return np.zeros_like(depth_map, dtype=bool)
        
        height, width = depth_map.shape
        isolation_radius = self.correction_params["isolation_radius"]
        min_neighbors = self.correction_params["min_neighbors"]
        
        isolated_mask = np.zeros_like(depth_map, dtype=bool)
        
        # 创建结构元素
        y, x = np.ogrid[-isolation_radius:isolation_radius+1, -isolation_radius:isolation_radius+1]
        structure = (x*x + y*y) <= isolation_radius*isolation_radius
        
        # 对每个有效点检查邻居数量
        for r in range(height):
            for c in range(width):
                if not valid_mask[r, c]:
                    continue
                
                # 提取邻域
                r_start = max(0, r - isolation_radius)
                r_end = min(height, r + isolation_radius + 1)
                c_start = max(0, c - isolation_radius)
                c_end = min(width, c + isolation_radius + 1)
                
                neighborhood = valid_mask[r_start:r_end, c_start:c_end]
                neighbor_count = np.sum(neighborhood) - 1  # 减去自己
                
                if neighbor_count < min_neighbors:
                    isolated_mask[r, c] = True
        
        isolated_count = np.sum(isolated_mask)
        print(f"      发现 {isolated_count:,} 个孤立点")
        
        return isolated_mask
    
    def detect_depth_jumps(self, depth_map: np.ndarray) -> np.ndarray:
        """检测深度跳跃"""
        print("    检测深度跳跃...")
        
        valid_mask = np.isfinite(depth_map)
        if np.sum(valid_mask) == 0:
            return np.zeros_like(depth_map, dtype=bool)
        
        jump_threshold = self.correction_params["depth_jump_threshold"]
        
        # 计算深度梯度
        grad_x = cv2.Sobel(depth_map, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(depth_map, cv2.CV_64F, 0, 1, ksize=3)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        
        # 检测大梯度点
        jump_mask = valid_mask & (gradient_magnitude > jump_threshold)
        
        # 形态学操作去除噪声
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        jump_mask = cv2.morphologyEx(jump_mask.astype(np.uint8), cv2.MORPH_OPEN, kernel).astype(bool)
        
        jump_count = np.sum(jump_mask)
        print(f"      发现 {jump_count:,} 个深度跳跃点")
        
        return jump_mask
    
    def detect_statistical_outliers(self, depth_map: np.ndarray) -> np.ndarray:
        """检测统计异常值"""
        print("    检测统计异常值...")
        
        valid_mask = np.isfinite(depth_map)
        if np.sum(valid_mask) == 0:
            return np.zeros_like(depth_map, dtype=bool)
        
        window_size = self.correction_params["local_window_size"]
        z_threshold = self.correction_params["outlier_z_threshold"]
        
        outlier_mask = np.zeros_like(depth_map, dtype=bool)
        
        # 局部统计异常检测
        half_window = window_size // 2
        height, width = depth_map.shape
        
        for r in range(half_window, height - half_window):
            for c in range(half_window, width - half_window):
                if not valid_mask[r, c]:
                    continue
                
                # 提取局部窗口
                window = depth_map[r-half_window:r+half_window+1, c-half_window:c+half_window+1]
                window_valid = valid_mask[r-half_window:r+half_window+1, c-half_window:c+half_window+1]
                
                valid_values = window[window_valid]
                if len(valid_values) < 5:
                    continue
                
                # 计算Z-score
                window_mean = np.mean(valid_values)
                window_std = np.std(valid_values)
                
                if window_std > 0:
                    z_score = abs(depth_map[r, c] - window_mean) / window_std
                    if z_score > z_threshold:
                        outlier_mask[r, c] = True
        
        outlier_count = np.sum(outlier_mask)
        print(f"      发现 {outlier_count:,} 个统计异常值")
        
        return outlier_mask
    
    def detect_surface_discontinuities(self, depth_map: np.ndarray) -> np.ndarray:
        """检测表面不连续性"""
        print("    检测表面不连续性...")
        
        valid_mask = np.isfinite(depth_map)
        if np.sum(valid_mask) == 0:
            return np.zeros_like(depth_map, dtype=bool)
        
        smoothness_threshold = self.correction_params["surface_smoothness_threshold"]
        
        # 计算二阶导数（曲率）
        # 确保数据类型正确
        depth_map_float32 = depth_map.astype(np.float32)
        laplacian = cv2.Laplacian(depth_map_float32, cv2.CV_32F, ksize=3)
        
        # 检测高曲率点
        discontinuity_mask = valid_mask & (np.abs(laplacian) > smoothness_threshold)
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        discontinuity_mask = cv2.morphologyEx(
            discontinuity_mask.astype(np.uint8),
            cv2.MORPH_OPEN,
            kernel
        ).astype(bool)
        
        discontinuity_count = np.sum(discontinuity_mask)
        print(f"      发现 {discontinuity_count:,} 个表面不连续点")
        
        return discontinuity_mask
    
    def intelligent_depth_interpolation(self, depth_map: np.ndarray, outlier_mask: np.ndarray) -> np.ndarray:
        """智能深度插值"""
        print("    执行智能深度插值...")
        
        corrected_depth = depth_map.copy()
        valid_mask = np.isfinite(depth_map) & (~outlier_mask)
        
        if np.sum(valid_mask) == 0:
            return corrected_depth
        
        # 1. 小区域用中值滤波
        small_outliers = outlier_mask.copy()
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        
        for _ in range(2):
            median_filtered = cv2.medianBlur(corrected_depth.astype(np.float32), 5)
            opened = cv2.morphologyEx(small_outliers.astype(np.uint8), cv2.MORPH_OPEN, kernel)
            small_outlier_mask = small_outliers & (opened == 0)
            
            if np.sum(small_outlier_mask) == 0:
                break
            
            corrected_depth[small_outlier_mask] = median_filtered[small_outlier_mask]
            small_outliers &= (~small_outlier_mask)
        
        # 2. 大区域用距离加权插值
        if np.sum(small_outliers) > 0:
            height, width = depth_map.shape
            y_coords, x_coords = np.mgrid[0:height, 0:width]
            
            # 有效点
            valid_points = np.column_stack((
                y_coords[valid_mask].ravel(),
                x_coords[valid_mask].ravel()
            ))
            valid_values = corrected_depth[valid_mask]
            
            # 需要插值的点
            invalid_points = np.column_stack((
                y_coords[small_outliers].ravel(),
                x_coords[small_outliers].ravel()
            ))
            
            if len(valid_points) > 0 and len(invalid_points) > 0:
                # 使用KD树进行最近邻搜索
                tree = cKDTree(valid_points)
                distances, indices = tree.query(invalid_points, k=min(8, len(valid_points)))
                
                # 距离加权插值
                if distances.ndim == 1:
                    distances = distances.reshape(-1, 1)
                    indices = indices.reshape(-1, 1)
                
                weights = 1.0 / (distances + 0.1)  # 避免除零
                weights /= np.sum(weights, axis=1, keepdims=True)
                
                interpolated_values = np.sum(valid_values[indices] * weights, axis=1)
                corrected_depth[small_outliers] = interpolated_values
        
        # 3. 表面平滑
        smoothing_sigma = self.correction_params["smoothing_sigma"]
        if smoothing_sigma > 0:
            corrected_depth = cv2.GaussianBlur(corrected_depth, (5, 5), smoothing_sigma)
        
        # 保持原始NaN区域
        original_nan_mask = ~np.isfinite(depth_map)
        corrected_depth[original_nan_mask] = np.nan
        
        repair_count = np.sum(outlier_mask & np.isfinite(corrected_depth))
        print(f"      修复了 {repair_count:,} 个点")
        
        return corrected_depth
    
    def correct_depth_outliers(self, depth_map: np.ndarray, engine_idx: int) -> np.ndarray:
        """修正深度异常值"""
        print(f"  深度域修正 - 光机 {engine_idx}...")
        
        if np.sum(np.isfinite(depth_map)) == 0:
            print("    ✗ 没有有效深度数据")
            return depth_map
        
        # 统计原始深度信息
        valid_mask = np.isfinite(depth_map)
        original_std = np.nanstd(depth_map)
        print(f"    原始深度标准差: {original_std:.3f} mm")
        
        # 1. 检测各类异常
        isolated_mask = self.detect_isolated_points(depth_map)
        jump_mask = self.detect_depth_jumps(depth_map)
        outlier_mask = self.detect_statistical_outliers(depth_map)
        discontinuity_mask = self.detect_surface_discontinuities(depth_map)
        
        # 合并所有异常掩码
        combined_mask = isolated_mask | jump_mask | outlier_mask | discontinuity_mask
        total_outliers = np.sum(combined_mask)
        
        print(f"    总异常点数: {total_outliers:,}")
        
        if total_outliers == 0:
            print("    ✓ 未发现深度异常")
            return depth_map
        
        # 2. 迭代修正
        current_depth = depth_map.copy()
        
        for iteration in range(self.correction_params["max_correction_iterations"]):
            print(f"    迭代 {iteration + 1}...")
            
            # 修正当前异常
            current_depth = self.intelligent_depth_interpolation(current_depth, combined_mask)
            
            # 重新检测异常
            new_outlier_mask = self.detect_statistical_outliers(current_depth)
            new_outlier_count = np.sum(new_outlier_mask)
            
            print(f"      剩余异常: {new_outlier_count:,}")
            
            if new_outlier_count < total_outliers * 0.1:  # 异常点减少90%
                break
            
            combined_mask = new_outlier_mask
        
        # 3. 评估修正效果
        final_std = np.nanstd(current_depth)
        improvement = (original_std - final_std) / original_std * 100
        
        print(f"    修正效果:")
        print(f"      原始标准差: {original_std:.3f} mm")
        print(f"      修正后标准差: {final_std:.3f} mm")
        print(f"      改善程度: {improvement:+.1f}%")
        
        return current_depth
    
    def process_dataset(self, phases_dir: str, output_dir: str):
        """处理数据集"""
        dataset_name = Path(phases_dir).name.replace("enhanced_corrected_results_", "").replace("phase_corrected_results_", "")
        print(f"\n{'='*80}")
        print(f"深度域离散点修正: {dataset_name}")
        print(f"{'='*80}")
        
        start_time = time.time()
        
        # 创建输出目录
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 1. 加载相位数据
        phases = self.load_phases(phases_dir)
        
        if not phases:
            print("✗ 没有相位数据")
            return
        
        # 2. 转换为深度并修正
        print("\n深度域修正处理:")
        print("-" * 40)
        
        corrected_depths = {}
        
        for engine_idx, phase in phases.items():
            print(f"\n处理光机 {engine_idx}...")
            
            # 转换为深度
            depth_map = self.convert_phase_to_depth(phase, engine_idx)
            
            if depth_map is None:
                print(f"  ✗ 光机 {engine_idx}: 深度转换失败")
                continue
            
            # 深度域修正
            corrected_depth = self.correct_depth_outliers(depth_map, engine_idx)
            corrected_depths[engine_idx] = corrected_depth
            
            # 保存修正后的深度
            depth_file = output_path / f"depth_corrected_engine{engine_idx}.npy"
            np.save(depth_file, corrected_depth)
            print(f"  ✓ 已保存: {depth_file}")
        
        # 完成
        processing_time = time.time() - start_time
        print(f"\n数据集 {dataset_name} 深度域修正完成!")
        print(f"处理时间: {processing_time:.1f} 秒")
        print(f"输出目录: {output_dir}")

def main():
    parser = argparse.ArgumentParser(description="深度域离散点修正程序")
    parser.add_argument("--phases_dir", type=str, default="enhanced_corrected_results_Test", 
                       help="相位数据目录")
    parser.add_argument("--output_dir", type=str, default="depth_corrected_results", 
                       help="输出目录")
    parser.add_argument("--calibration_prefix", type=str, default="fast_improved_calibration",
                       help="标定文件前缀")
    
    args = parser.parse_args()
    
    # 创建深度域修正器
    corrector = DepthDomainCorrector(calibration_prefix=args.calibration_prefix)
    
    if not corrector.calibration_params:
        print("✗ 标定参数加载失败，无法继续")
        return
    
    print("深度域离散点修正程序")
    print(f"相位目录: {args.phases_dir}")
    print(f"输出目录: {args.output_dir}")
    
    # 检查输入目录
    if not Path(args.phases_dir).exists():
        print(f"✗ 相位目录不存在: {args.phases_dir}")
        return
    
    # 执行深度域修正
    corrector.process_dataset(args.phases_dir, args.output_dir)

if __name__ == "__main__":
    main()
