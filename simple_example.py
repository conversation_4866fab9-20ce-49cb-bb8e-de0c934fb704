#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
四频五步相移相位展开简单示例
"""

from phase_unwrapping_four_frequency import FourFrequencyPhaseUnwrapping
from generate_test_images import generate_test_images
import matplotlib.pyplot as plt
import numpy as np


def simple_demo():
    """简单演示程序"""
    print("=== 四频五步相移相位展开演示 ===\n")
    
    # 1. 生成测试图像
    print("1. 生成测试图像...")
    generate_test_images(
        output_folder="demo_images",
        width=320,
        height=240,
        pixel_periods=[1080, 512, 64, 8],
        noise_level=0.01
    )
    
    # 2. 创建相位展开器
    print("2. 创建相位展开器...")
    unwrapper = FourFrequencyPhaseUnwrapping(pixel_periods=[1080, 512, 64, 8])
    
    # 3. 执行相位展开
    print("3. 执行相位展开...")
    results = unwrapper.process_phase_unwrapping(
        image_folder="demo_images",
        output_folder="demo_results",
        min_modulation=0.05
    )
    
    # 4. 显示关键结果
    print("4. 显示结果...")
    
    # 获取结果
    wrapped_phases = results['wrapped_phases']
    unwrapped_phase = results['unwrapped_phase_masked']
    quality_mask = results['quality_mask']
    
    # 创建简单的结果显示
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 显示最高频的包裹相位
    axes[0, 0].imshow(wrapped_phases[-1], cmap='hsv', vmin=-np.pi, vmax=np.pi)
    axes[0, 0].set_title('高频包裹相位 (8像素周期)')
    axes[0, 0].axis('off')
    
    # 显示最低频的包裹相位
    axes[0, 1].imshow(wrapped_phases[0], cmap='hsv', vmin=-np.pi, vmax=np.pi)
    axes[0, 1].set_title('低频包裹相位 (1080像素周期)')
    axes[0, 1].axis('off')
    
    # 显示展开相位
    axes[1, 0].imshow(unwrapped_phase, cmap='jet')
    axes[1, 0].set_title('展开相位')
    axes[1, 0].axis('off')
    
    # 显示质量掩码
    axes[1, 1].imshow(quality_mask, cmap='gray')
    axes[1, 1].set_title('质量掩码')
    axes[1, 1].axis('off')
    
    plt.tight_layout()
    plt.savefig('demo_results_summary.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    # 5. 输出统计信息
    print("\n5. 结果统计:")
    print(f"   图像尺寸: {unwrapped_phase.shape}")
    print(f"   有效像素比例: {np.sum(quality_mask) / quality_mask.size * 100:.1f}%")
    
    valid_phase = unwrapped_phase[~np.isnan(unwrapped_phase)]
    if len(valid_phase) > 0:
        print(f"   展开相位范围: [{np.min(valid_phase):.2f}, {np.max(valid_phase):.2f}] rad")
        print(f"   展开相位平均值: {np.mean(valid_phase):.2f} rad")
    
    print("\n演示完成！请查看生成的图像文件。")
    return results


if __name__ == "__main__":
    simple_demo()
