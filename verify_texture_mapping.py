#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
纹理贴图验证工具
检查点云文件中的颜色是否来自真实纹理图像
"""

import numpy as np
import cv2
from pathlib import Path
import argparse

def load_texture_reference(test_folder):
    """加载参考纹理图像"""
    test_path = Path(test_folder)
    
    # 加载RGB通道
    b_channel = cv2.imread(str(test_path / "81.bmp"), cv2.IMREAD_GRAYSCALE)
    g_channel = cv2.imread(str(test_path / "82.bmp"), cv2.IMREAD_GRAYSCALE)  
    r_channel = cv2.imread(str(test_path / "83.bmp"), cv2.IMREAD_GRAYSCALE)
    
    if any(img is None for img in [b_channel, g_channel, r_channel]):
        return None
    
    # 合并为RGB
    texture_rgb = np.stack([r_channel, g_channel, b_channel], axis=-1)
    return texture_rgb

def analyze_point_cloud_colors(ply_file):
    """分析点云文件中的颜色信息"""
    colors = []
    
    print(f"📖 分析点云文件: {ply_file}")
    
    with open(ply_file, 'r') as f:
        lines = f.readlines()
        
        # 找到数据开始位置
        data_start = 0
        vertex_count = 0
        for i, line in enumerate(lines):
            if line.startswith("element vertex"):
                vertex_count = int(line.split()[-1])
            elif line.startswith("end_header"):
                data_start = i + 1
                break
        
        print(f"  顶点数量: {vertex_count:,}")
        
        # 读取前1000个点的颜色进行分析
        sample_size = min(1000, vertex_count)
        for i in range(sample_size):
            line = lines[data_start + i].strip()
            parts = line.split()
            if len(parts) >= 6:
                r, g, b = int(parts[3]), int(parts[4]), int(parts[5])
                colors.append([r, g, b])
    
    colors = np.array(colors)
    print(f"  采样分析: {len(colors)} 个点")
    
    return colors

def compare_textures(texture_rgb, point_colors):
    """比较纹理图像和点云颜色"""
    print("\n🔍 纹理对比分析:")
    
    # 纹理图像统计
    texture_flat = texture_rgb.reshape(-1, 3)
    
    print(f"  纹理图像:")
    print(f"    形状: {texture_rgb.shape}")
    print(f"    颜色范围: R[{np.min(texture_flat[:, 0])}-{np.max(texture_flat[:, 0])}], "
          f"G[{np.min(texture_flat[:, 1])}-{np.max(texture_flat[:, 1])}], "
          f"B[{np.min(texture_flat[:, 2])}-{np.max(texture_flat[:, 2])}]")
    print(f"    颜色均值: R{np.mean(texture_flat[:, 0]):.1f}, "
          f"G{np.mean(texture_flat[:, 1]):.1f}, "
          f"B{np.mean(texture_flat[:, 2]):.1f}")
    
    print(f"  点云颜色:")
    print(f"    采样数量: {len(point_colors)}")
    print(f"    颜色范围: R[{np.min(point_colors[:, 0])}-{np.max(point_colors[:, 0])}], "
          f"G[{np.min(point_colors[:, 1])}-{np.max(point_colors[:, 1])}], "
          f"B[{np.min(point_colors[:, 2])}-{np.max(point_colors[:, 2])}]")
    print(f"    颜色均值: R{np.mean(point_colors[:, 0]):.1f}, "
          f"G{np.mean(point_colors[:, 1]):.1f}, "
          f"B{np.mean(point_colors[:, 2]):.1f}")
    
    # 检查颜色分布相似性
    texture_mean = np.mean(texture_flat, axis=0)
    point_mean = np.mean(point_colors, axis=0)
    color_similarity = np.linalg.norm(texture_mean - point_mean)
    
    print(f"\n📊 相似性分析:")
    print(f"  颜色均值差异: {color_similarity:.2f}")
    
    # 检查是否是伪彩色模式
    # 伪彩色通常有特定的模式（如jet colormap）
    unique_colors = len(np.unique(point_colors.view(np.dtype((np.void, point_colors.dtype.itemsize * 3)))))
    total_colors = len(point_colors)
    uniqueness_ratio = unique_colors / total_colors
    
    print(f"  颜色唯一性: {unique_colors}/{total_colors} ({uniqueness_ratio:.3f})")
    
    # 判断
    if color_similarity < 50 and uniqueness_ratio > 0.8:
        print("  ✅ 判断: 使用了真实纹理")
        return True
    elif uniqueness_ratio < 0.3:
        print("  ❌ 判断: 可能是伪彩色模式")
        return False
    else:
        print("  ⚠️  判断: 不确定")
        return None

def main():
    parser = argparse.ArgumentParser(description="验证点云纹理贴图")
    parser.add_argument("--test_folder", default="测试/Test", help="测试数据文件夹")
    parser.add_argument("--point_cloud", default="depth_aware_fusion_Test_textured/fused_point_cloud_corrected.ply", help="点云文件")
    
    args = parser.parse_args()
    
    print("🔍 纹理贴图验证工具")
    print("=" * 50)
    
    # 1. 加载参考纹理
    texture_rgb = load_texture_reference(args.test_folder)
    if texture_rgb is None:
        print("❌ 无法加载参考纹理图像")
        return
    
    # 2. 分析点云颜色
    if not Path(args.point_cloud).exists():
        print(f"❌ 点云文件不存在: {args.point_cloud}")
        return
    
    point_colors = analyze_point_cloud_colors(args.point_cloud)
    
    # 3. 对比分析
    is_textured = compare_textures(texture_rgb, point_colors)
    
    print("\n" + "=" * 50)
    if is_textured:
        print("🎉 验证结果: 点云成功应用了真实纹理!")
    elif is_textured is False:
        print("❌ 验证结果: 点云可能使用了伪彩色")
    else:
        print("⚠️  验证结果: 无法确定纹理类型")

if __name__ == "__main__":
    main() 