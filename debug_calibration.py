#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试标定程序，分析数据质量
"""

import numpy as np
import cv2
import os
import matplotlib.pyplot as plt
from phase_height_calibration import PhaseHeightCalibration


def analyze_single_image():
    """分析单张图像"""
    # 检查第一个测试文件夹的图像
    test_folder = "标定数据/Test-0"
    
    print("分析图像质量...")
    
    # 检查投影仪0的图像
    for i in range(1, 21):  # 投影仪0的图像范围
        img_path = os.path.join(test_folder, f"{i}.bmp")
        if os.path.exists(img_path):
            img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
            if img is not None:
                print(f"图像 {i}: 尺寸={img.shape}, 均值={np.mean(img):.1f}, 标准差={np.std(img):.1f}")
            else:
                print(f"图像 {i}: 读取失败")
        else:
            print(f"图像 {i}: 文件不存在")


def analyze_phase_data():
    """分析相位数据"""
    calibrator = PhaseHeightCalibration()
    
    print("分析相位数据...")
    
    # 处理几个位置的数据
    positions_to_check = [0, 5, 10]
    
    for pos in positions_to_check:
        test_folder = os.path.join("标定数据", f"Test-{pos}")
        print(f"\n分析位置 {pos} (高度 {pos * 1.0} mm):")
        
        if not os.path.exists(test_folder):
            print(f"  文件夹不存在: {test_folder}")
            continue
        
        # 处理投影仪0
        unwrapped_phase = calibrator.process_single_position(test_folder, 0)
        
        if unwrapped_phase is not None:
            valid_mask = ~np.isnan(unwrapped_phase)
            valid_phases = unwrapped_phase[valid_mask]
            
            if len(valid_phases) > 0:
                print(f"  有效像素数: {len(valid_phases):,}")
                print(f"  相位范围: [{np.min(valid_phases):.3f}, {np.max(valid_phases):.3f}] rad")
                print(f"  相位均值: {np.mean(valid_phases):.3f} rad")
                print(f"  相位标准差: {np.std(valid_phases):.3f} rad")
                
                # 检查相位分布
                unique_phases = len(np.unique(np.round(valid_phases, 3)))
                print(f"  唯一相位值数: {unique_phases}")
            else:
                print("  没有有效相位数据")
        else:
            print("  相位展开失败")


def visualize_phase_progression():
    """可视化相位随高度的变化"""
    calibrator = PhaseHeightCalibration()
    
    print("可视化相位随高度的变化...")
    
    phases_by_position = []
    heights = []
    
    # 收集所有位置的相位数据
    for pos in range(11):
        test_folder = os.path.join("标定数据", f"Test-{pos}")
        
        if os.path.exists(test_folder):
            unwrapped_phase = calibrator.process_single_position(test_folder, 0)
            
            if unwrapped_phase is not None:
                valid_mask = ~np.isnan(unwrapped_phase)
                valid_phases = unwrapped_phase[valid_mask]
                
                if len(valid_phases) > 1000:  # 确保有足够的数据点
                    # 随机采样1000个点
                    sample_indices = np.random.choice(len(valid_phases), 1000, replace=False)
                    sampled_phases = valid_phases[sample_indices]
                    
                    phases_by_position.extend(sampled_phases)
                    heights.extend([pos * 1.0] * len(sampled_phases))
    
    if phases_by_position:
        phases_array = np.array(phases_by_position)
        heights_array = np.array(heights)
        
        print(f"总数据点: {len(phases_array)}")
        print(f"相位范围: [{np.min(phases_array):.3f}, {np.max(phases_array):.3f}] rad")
        print(f"高度范围: [{np.min(heights_array):.1f}, {np.max(heights_array):.1f}] mm")
        
        # 创建散点图
        plt.figure(figsize=(12, 8))
        
        # 按高度分组显示
        colors = plt.cm.viridis(np.linspace(0, 1, 11))
        
        for i, height in enumerate(range(11)):
            mask = heights_array == height
            if np.any(mask):
                plt.scatter(phases_array[mask], heights_array[mask], 
                           c=[colors[i]], alpha=0.1, s=1, label=f'Height {height}mm')
        
        plt.xlabel('Phase (rad)')
        plt.ylabel('Height (mm)')
        plt.title('Phase vs Height Distribution')
        plt.grid(True, alpha=0.3)
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.tight_layout()
        plt.savefig('phase_height_distribution.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        # 分析相位-高度关系
        correlation = np.corrcoef(phases_array, heights_array)[0, 1]
        print(f"相位-高度相关系数: {correlation:.6f}")
        
        # 检查是否存在明显的线性关系
        if abs(correlation) < 0.1:
            print("警告: 相位与高度之间几乎没有相关性")
            print("可能的原因:")
            print("1. 相位展开算法有问题")
            print("2. 标定数据质量不好")
            print("3. 相位计算有误")
    else:
        print("没有收集到有效的相位数据")


def check_wrapped_phases():
    """检查包裹相位"""
    calibrator = PhaseHeightCalibration()
    
    print("检查包裹相位...")
    
    # 检查第一个位置的包裹相位
    test_folder = "标定数据/Test-0"
    
    # 加载图像
    images_by_frequency = calibrator.load_calibration_images(test_folder, 0)
    images = calibrator.convert_to_phase_unwrapper_format(images_by_frequency)
    
    # 计算包裹相位
    wrapped_phases, modulations = calibrator.phase_unwrapper.calculate_all_wrapped_phases(images)
    
    print(f"频率数量: {len(wrapped_phases)}")
    
    for i, (period, wrapped_phase) in enumerate(zip(calibrator.pixel_periods, wrapped_phases)):
        print(f"\n频率 {i} (周期 {period} 像素):")
        print(f"  相位范围: [{np.min(wrapped_phase):.3f}, {np.max(wrapped_phase):.3f}] rad")
        print(f"  相位均值: {np.mean(wrapped_phase):.3f} rad")
        print(f"  相位标准差: {np.std(wrapped_phase):.3f} rad")
        
        # 检查调制度
        modulation = modulations[i]
        print(f"  调制度范围: [{np.min(modulation):.3f}, {np.max(modulation):.3f}]")
        print(f"  调制度均值: {np.mean(modulation):.3f}")
        
        # 检查有效像素比例
        valid_mod_mask = modulation > 0.05
        valid_ratio = np.sum(valid_mod_mask) / modulation.size
        print(f"  有效像素比例: {valid_ratio * 100:.1f}%")


def improved_calibration_test():
    """改进的标定测试"""
    print("=== 改进的标定测试 ===")
    
    # 首先检查数据质量
    check_wrapped_phases()
    
    # 分析相位数据
    analyze_phase_data()
    
    # 可视化相位变化
    visualize_phase_progression()


if __name__ == "__main__":
    improved_calibration_test()
