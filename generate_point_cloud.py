import os
import cv2
import numpy as np
from tqdm import tqdm

# --- 常量定义 ---
RECONSTRUCTION_DIR = 'reconstruction_results'
COLOR_IMG_DIR = 'pcb'
OUTPUT_DIR = 'point_clouds'
CCD_DX = 0.0154  # 相机像素的物理尺寸, 单位 mm

# --- 函数定义 ---

def save_colored_point_cloud_to_ply(filepath, points, colors):
    """
    将带有颜色的点云数据保存为 PLY 文件。
    
    Args:
    - filepath (str): 输出的 .ply 文件路径。
    - points (np.array): (N, 3) 形状的数组, 包含 (x, y, z) 坐标。
    - colors (np.array): (N, 3) 形状的数组, 包含 (R, G, B) 颜色值 (0-255)。
    """
    # 将坐标和颜色数据合并
    vertices = np.hstack([points, colors])
    
    header = f"""ply
format ascii 1.0
element vertex {len(vertices)}
property float x
property float y
property float z
property uchar red
property uchar green
property uchar blue
end_header
"""
    with open(filepath, 'w') as f:
        f.write(header)
        # 为坐标使用浮点数格式，为颜色使用整数格式
        np.savetxt(f, vertices, fmt='%.6f %.6f %.6f %d %d %d')

# --- 主逻辑 ---

def main():
    """主执行函数"""
    # --- 目录检查 ---
    if not os.path.exists(RECONSTRUCTION_DIR):
        print(f"错误: 重建结果目录 '{RECONSTRUCTION_DIR}' 不存在。")
        print("请先运行 reconstruct_depth.py 脚本。")
        return

    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
        print(f"创建输出目录: {OUTPUT_DIR}")

    # 1. 加载并创建彩色纹理图
    print("正在加载彩色纹理图像...")
    try:
        blue_channel_path = os.path.join(COLOR_IMG_DIR, '81.bmp')
        green_channel_path = os.path.join(COLOR_IMG_DIR, '82.bmp')
        red_channel_path = os.path.join(COLOR_IMG_DIR, '83.bmp')

        b = cv2.imread(blue_channel_path, cv2.IMREAD_GRAYSCALE)
        g = cv2.imread(green_channel_path, cv2.IMREAD_GRAYSCALE)
        r = cv2.imread(red_channel_path, cv2.IMREAD_GRAYSCALE)

        if b is None or g is None or r is None:
            raise FileNotFoundError("一个或多个颜色通道图像缺失或无法读取。")
        
        # 使用cv2.merge按BGR顺序合并成彩色图
        color_texture = cv2.merge([b, g, r])
        print("彩色纹理图创建成功。")
        
    except Exception as e:
        print(f"加载彩色图像时出错: {e}")
        print("请确保 '81.bmp', '82.bmp', '83.bmp' 文件存在于 'pcb' 目录中。")
        return

    # --- 获取深度图文件 ---
    depth_map_files = [f for f in os.listdir(RECONSTRUCTION_DIR) if f.endswith('.npy')]
    if not depth_map_files:
        print(f"在 '{RECONSTRUCTION_DIR}' 目录中没有找到.npy格式的深度图。")
        return

    print(f"开始将深度图转换为带颜色的点云 (ccdDx = {CCD_DX} mm)...")

    for filename in tqdm(depth_map_files, desc="处理深度图"):
        depth_map_path = os.path.join(RECONSTRUCTION_DIR, filename)
        depth_map = np.load(depth_map_path)
        
        # 检查尺寸是否匹配
        if depth_map.shape != color_texture.shape[:2]:
            print(f"警告: 深度图 {filename} ({depth_map.shape}) 与彩色纹理 ({color_texture.shape[:2]}) 尺寸不匹配，已跳过。")
            continue

        height, width = depth_map.shape
        c_coords = np.arange(width, dtype=np.float32)
        r_coords = np.arange(height, dtype=np.float32)
        c_grid, r_grid = np.meshgrid(c_coords, r_coords)

        # 2. 计算真实世界的 X, Y, Z 坐标
        x_coords = c_grid * CCD_DX
        y_coords = r_grid * CCD_DX
        z_coords = depth_map

        # 4. 将坐标合并成 (N, 3) 的点集
        points = np.stack((x_coords.ravel(), y_coords.ravel(), z_coords.ravel()), axis=-1)
        
        # 5. 过滤掉由蒙版产生的无效点 (高度为0的点)
        valid_mask = points[:, 2] > 1e-6  # 使用小epsilon避免浮点精度问题
        filtered_points = points[valid_mask]

        # 6. 获取并过滤每个点的颜色
        colors_bgr = color_texture.reshape(-1, 3)
        colors_rgb = colors_bgr[:, ::-1]
        filtered_colors = colors_rgb[valid_mask]
        
        # 7. 保存为带颜色的 .ply 文件
        base_filename = os.path.splitext(filename)[0].replace('_depth_map', '_colored')
        output_path = os.path.join(OUTPUT_DIR, f'{base_filename}.ply')
        
        save_colored_point_cloud_to_ply(output_path, filtered_points, filtered_colors)
    
    print(f"\n带颜色的点云生成完毕！所有结果已保存至 '{OUTPUT_DIR}' 目录。")
    print("您可以使用 MeshLab, CloudCompare 等软件打开 .ply 文件查看。")

if __name__ == '__main__':
    main() 