#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
四频五步相移相位展开程序
像素周期：8, 64, 512, 1080 (从高频到低频)
展开顺序：从低频到高频
"""

import numpy as np
import cv2
import os
import matplotlib.pyplot as plt
from typing import List, Tuple, Optional


class FourFrequencyPhaseUnwrapping:
    """四频五步相移相位展开类"""
    
    def __init__(self, pixel_periods: List[int] = [1080, 512, 64, 8]):
        """
        初始化
        Args:
            pixel_periods: 像素周期列表，从低频到高频排列
        """
        self.pixel_periods = pixel_periods  # [1080, 512, 64, 8]
        self.frequencies = len(pixel_periods)
        self.phase_steps = 5  # 五步相移
        
    def load_images(self, image_folder: str) -> np.ndarray:
        """
        加载图像
        Args:
            image_folder: 图像文件夹路径
        Returns:
            images: 形状为 (frequencies, steps, height, width) 的图像数组
        """
        images = []
        
        for freq_idx in range(self.frequencies):
            freq_images = []
            for step in range(self.phase_steps):
                # 假设图像命名格式为: freq_{freq_idx}_step_{step}.png
                img_path = os.path.join(image_folder, f"freq_{freq_idx}_step_{step}.png")
                if os.path.exists(img_path):
                    img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
                    freq_images.append(img.astype(np.float32))
                else:
                    print(f"警告: 图像文件不存在 {img_path}")
                    # 创建一个空白图像作为占位符
                    if freq_images:
                        h, w = freq_images[0].shape
                        freq_images.append(np.zeros((h, w), dtype=np.float32))
                    else:
                        freq_images.append(np.zeros((480, 640), dtype=np.float32))
            
            images.append(freq_images)
        
        return np.array(images)
    
    def five_step_phase_calculation(self, images: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        五步相移算法计算相位
        Args:
            images: 形状为 (5, height, width) 的五幅图像
        Returns:
            wrapped_phase: 包裹相位 [-π, π]
            modulation: 调制度
        """
        I1, I2, I3, I4, I5 = images
        
        # 五步相移公式
        numerator = 2 * (I2 - I4)
        denominator = 2 * I3 - I1 - I5
        
        # 避免除零
        denominator = np.where(np.abs(denominator) < 1e-10, 1e-10, denominator)
        
        # 计算包裹相位
        wrapped_phase = np.arctan2(numerator, denominator)
        
        # 计算调制度
        ac_component = np.sqrt(numerator**2 + denominator**2) / 2
        dc_component = (I1 + I2 + I3 + I4 + I5) / 5
        modulation = np.where(dc_component > 0, ac_component / dc_component, 0)
        
        return wrapped_phase, modulation
    
    def calculate_all_wrapped_phases(self, images: np.ndarray) -> Tuple[List[np.ndarray], List[np.ndarray]]:
        """
        计算所有频率的包裹相位
        Args:
            images: 形状为 (frequencies, steps, height, width) 的图像数组
        Returns:
            wrapped_phases: 各频率的包裹相位列表
            modulations: 各频率的调制度列表
        """
        wrapped_phases = []
        modulations = []
        
        for freq_idx in range(self.frequencies):
            phase, mod = self.five_step_phase_calculation(images[freq_idx])
            wrapped_phases.append(phase)
            modulations.append(mod)
            
        return wrapped_phases, modulations
    
    def phase_unwrapping_hierarchical(self, wrapped_phases: List[np.ndarray]) -> np.ndarray:
        """
        分层相位展开算法
        Args:
            wrapped_phases: 各频率的包裹相位列表 [低频到高频]
        Returns:
            unwrapped_phase: 展开后的绝对相位
        """
        # 从最低频开始展开
        unwrapped_phase = wrapped_phases[0].copy()  # 最低频 (1080像素周期)
        
        # 逐级展开到高频
        for i in range(1, len(wrapped_phases)):
            current_period = self.pixel_periods[i]
            previous_period = self.pixel_periods[i-1]
            
            # 计算频率比
            freq_ratio = previous_period / current_period
            
            # 将前一级的展开相位缩放到当前频率
            scaled_phase = unwrapped_phase * freq_ratio
            
            # 计算当前频率的理论包裹相位
            theoretical_wrapped = np.angle(np.exp(1j * scaled_phase))
            
            # 计算相位差
            phase_diff = wrapped_phases[i] - theoretical_wrapped
            
            # 展开相位差
            k = np.round(phase_diff / (2 * np.pi))
            
            # 更新展开相位
            unwrapped_phase = wrapped_phases[i] + 2 * np.pi * k
            
        return unwrapped_phase
    
    def create_quality_mask(self, modulations: List[np.ndarray], 
                          min_modulation: float = 0.1) -> np.ndarray:
        """
        创建质量掩码
        Args:
            modulations: 各频率的调制度列表
            min_modulation: 最小调制度阈值
        Returns:
            quality_mask: 质量掩码
        """
        # 使用所有频率的调制度创建掩码
        combined_modulation = np.ones_like(modulations[0])
        
        for mod in modulations:
            combined_modulation *= (mod > min_modulation)
            
        return combined_modulation.astype(bool)
    
    def process_phase_unwrapping(self, image_folder: str, 
                               output_folder: str = "results",
                               min_modulation: float = 0.1) -> dict:
        """
        完整的相位展开处理流程
        Args:
            image_folder: 输入图像文件夹
            output_folder: 输出结果文件夹
            min_modulation: 最小调制度阈值
        Returns:
            results: 包含处理结果的字典
        """
        # 创建输出文件夹
        os.makedirs(output_folder, exist_ok=True)
        
        print("正在加载图像...")
        images = self.load_images(image_folder)
        
        print("正在计算包裹相位...")
        wrapped_phases, modulations = self.calculate_all_wrapped_phases(images)
        
        print("正在创建质量掩码...")
        quality_mask = self.create_quality_mask(modulations, min_modulation)
        
        print("正在进行分层相位展开...")
        unwrapped_phase = self.phase_unwrapping_hierarchical(wrapped_phases)
        
        # 应用质量掩码
        unwrapped_phase_masked = unwrapped_phase.copy()
        unwrapped_phase_masked[~quality_mask] = np.nan
        
        # 保存结果
        results = {
            'wrapped_phases': wrapped_phases,
            'modulations': modulations,
            'quality_mask': quality_mask,
            'unwrapped_phase': unwrapped_phase,
            'unwrapped_phase_masked': unwrapped_phase_masked
        }
        
        # 保存相位图
        self.save_phase_maps(results, output_folder)
        
        print(f"处理完成！结果保存在 {output_folder} 文件夹中")
        return results
    
    def save_phase_maps(self, results: dict, output_folder: str):
        """保存相位图"""
        # 保存包裹相位
        for i, phase in enumerate(results['wrapped_phases']):
            plt.figure(figsize=(10, 8))
            plt.imshow(phase, cmap='hsv', vmin=-np.pi, vmax=np.pi)
            plt.colorbar(label='Phase (rad)')
            plt.title(f'Wrapped Phase - Period {self.pixel_periods[i]} pixels')
            plt.savefig(os.path.join(output_folder, f'wrapped_phase_period_{self.pixel_periods[i]}.png'))
            plt.close()
        
        # 保存展开相位
        unwrapped = results['unwrapped_phase_masked']
        plt.figure(figsize=(10, 8))
        plt.imshow(unwrapped, cmap='jet')
        plt.colorbar(label='Unwrapped Phase (rad)')
        plt.title('Unwrapped Phase')
        plt.savefig(os.path.join(output_folder, 'unwrapped_phase.png'))
        plt.close()
        
        # 保存质量掩码
        plt.figure(figsize=(10, 8))
        plt.imshow(results['quality_mask'], cmap='gray')
        plt.colorbar()
        plt.title('Quality Mask')
        plt.savefig(os.path.join(output_folder, 'quality_mask.png'))
        plt.close()


def main():
    """主函数示例"""
    # 创建相位展开对象
    unwrapper = FourFrequencyPhaseUnwrapping(pixel_periods=[1080, 512, 64, 8])
    
    # 处理相位展开
    image_folder = "input_images"  # 请修改为实际的图像文件夹路径
    results = unwrapper.process_phase_unwrapping(image_folder)
    
    print("四频相位展开完成！")


if __name__ == "__main__":
    main()
