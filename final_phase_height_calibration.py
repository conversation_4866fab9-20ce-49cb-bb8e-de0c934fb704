#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版本的相位高度标定程序
基于发现的数据特点进行优化
"""

import numpy as np
import cv2
import os
import matplotlib.pyplot as plt
import pickle
from typing import List, Tuple, Dict, Optional
from sklearn.linear_model import LinearRegression, RANSACRegressor
from sklearn.preprocessing import PolynomialFeatures
from sklearn.pipeline import Pipeline
from sklearn.metrics import r2_score, mean_squared_error
import warnings
warnings.filterwarnings('ignore')


class FinalPhaseHeightCalibration:
    """最终版本的相位高度标定类"""
    
    def __init__(self, calibration_data_folder: str = "标定数据"):
        """初始化标定器"""
        self.calibration_data_folder = calibration_data_folder
        self.projector_configs = {
            0: {"name": "Projector_0", "image_range": (1, 20)},
            1: {"name": "Projector_1", "image_range": (21, 40)},
            2: {"name": "Projector_2", "image_range": (41, 60)},
            3: {"name": "Projector_3", "image_range": (61, 80)}
        }
        self.frequency_configs = {
            8: {"image_indices": [1, 2, 3, 4, 5]},      # 8像素周期
            64: {"image_indices": [6, 7, 8, 9, 10]},    # 64像素周期
            512: {"image_indices": [11, 12, 13, 14, 15]}, # 512像素周期
            1080: {"image_indices": [16, 17, 18, 19, 20]} # 1080像素周期
        }
        
        self.height_step = 1.0  # mm
        self.num_positions = 11
        self.calibration_results = {}
        
    def load_five_step_images(self, test_folder: str, projector_id: int, frequency: int) -> List[np.ndarray]:
        """
        加载指定频率的五步相移图像
        Args:
            test_folder: 测试文件夹路径
            projector_id: 投影仪ID
            frequency: 频率（像素周期）
        Returns:
            images: 五张相移图像列表
        """
        start_idx, _ = self.projector_configs[projector_id]["image_range"]
        img_indices = self.frequency_configs[frequency]["image_indices"]
        
        images = []
        for img_idx in img_indices:
            actual_img_idx = start_idx + img_idx - 1
            img_path = os.path.join(test_folder, f"{actual_img_idx}.bmp")
            
            if os.path.exists(img_path):
                img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
                if img is not None:
                    images.append(img.astype(np.float32))
                else:
                    print(f"警告: 无法读取图像 {img_path}")
                    # 使用前一张图像作为替代
                    if images:
                        images.append(images[-1].copy())
                    else:
                        images.append(np.zeros((2848, 2848), dtype=np.float32))
            else:
                print(f"警告: 图像文件不存在 {img_path}")
                if images:
                    images.append(images[-1].copy())
                else:
                    images.append(np.zeros((2848, 2848), dtype=np.float32))
        
        return images
    
    def calculate_wrapped_phase_and_modulation(self, images: List[np.ndarray]) -> Tuple[np.ndarray, np.ndarray]:
        """
        计算包裹相位和调制度
        Args:
            images: 五张相移图像
        Returns:
            wrapped_phase: 包裹相位
            modulation: 调制度
        """
        if len(images) != 5:
            raise ValueError("需要5张相移图像")
        
        I1, I2, I3, I4, I5 = images
        
        # 五步相移算法
        numerator = 2 * (I2 - I4)
        denominator = 2 * I3 - I1 - I5
        
        # 避免除零
        denominator = np.where(np.abs(denominator) < 1e-10, 1e-10, denominator)
        
        # 计算包裹相位
        wrapped_phase = np.arctan2(numerator, denominator)
        
        # 计算调制度
        ac_component = np.sqrt(numerator**2 + denominator**2) / 2
        dc_component = (I1 + I2 + I3 + I4 + I5) / 5
        modulation = np.where(dc_component > 0, ac_component / dc_component, 0)
        
        return wrapped_phase, modulation
    
    def calculate_intensity_based_height(self, test_folder: str, projector_id: int) -> Optional[float]:
        """
        基于图像强度计算高度（作为参考）
        从分析结果看，图像强度与高度有很强的相关性
        """
        try:
            # 使用第一张图像的平均强度
            start_idx, _ = self.projector_configs[projector_id]["image_range"]
            img_path = os.path.join(test_folder, f"{start_idx}.bmp")
            
            if os.path.exists(img_path):
                img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
                if img is not None:
                    return float(np.mean(img))
            
            return None
        except Exception as e:
            print(f"计算强度时出错: {e}")
            return None
    
    def collect_wrapped_phase_data(self, projector_id: int, frequency: int = 8) -> Tuple[List[np.ndarray], List[float], List[float]]:
        """
        收集包裹相位数据
        Args:
            projector_id: 投影仪ID
            frequency: 使用的频率（默认8像素周期，最敏感）
        Returns:
            phase_maps: 包裹相位图列表
            heights: 高度列表
            intensities: 强度列表（用于验证）
        """
        phase_maps = []
        heights = []
        intensities = []
        
        print(f"正在收集投影仪 {projector_id} 频率 {frequency} 的包裹相位数据...")
        
        for position in range(self.num_positions):
            test_folder = os.path.join(self.calibration_data_folder, f"Test-{position}")
            
            if not os.path.exists(test_folder):
                print(f"警告: 文件夹不存在 {test_folder}")
                continue
            
            print(f"  处理位置 {position} (高度 {position * self.height_step:.1f} mm)...")
            
            try:
                # 加载五步相移图像
                images = self.load_five_step_images(test_folder, projector_id, frequency)
                
                # 计算包裹相位和调制度
                wrapped_phase, modulation = self.calculate_wrapped_phase_and_modulation(images)
                
                # 计算强度（用于验证）
                intensity = self.calculate_intensity_based_height(test_folder, projector_id)
                
                phase_maps.append(wrapped_phase)
                heights.append(position * self.height_step)
                intensities.append(intensity if intensity is not None else 0.0)
                
            except Exception as e:
                print(f"    处理位置 {position} 时出错: {e}")
                continue
        
        print(f"投影仪 {projector_id} 收集到 {len(phase_maps)} 个有效位置的数据")
        return phase_maps, heights, intensities
    
    def extract_phase_height_pairs_with_spatial_info(self, phase_maps: List[np.ndarray], 
                                                   heights: List[float],
                                                   sample_ratio: float = 0.001) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """
        提取相位-高度对，保留空间信息
        Args:
            phase_maps: 相位图列表
            heights: 高度列表
            sample_ratio: 采样比例
        Returns:
            phases: 相位值
            height_values: 高度值
            x_coords: x坐标
            y_coords: y坐标
        """
        all_phases = []
        all_heights = []
        all_x_coords = []
        all_y_coords = []
        
        for phase_map, height in zip(phase_maps, heights):
            h, w = phase_map.shape
            
            # 创建坐标网格
            y_coords, x_coords = np.mgrid[0:h, 0:w]
            
            # 展平所有数组
            flat_phases = phase_map.flatten()
            flat_x = x_coords.flatten()
            flat_y = y_coords.flatten()
            flat_heights = np.full_like(flat_phases, height)
            
            # 随机采样
            n_total = len(flat_phases)
            n_samples = max(1000, int(n_total * sample_ratio))
            
            if n_total > n_samples:
                indices = np.random.choice(n_total, n_samples, replace=False)
                sampled_phases = flat_phases[indices]
                sampled_heights = flat_heights[indices]
                sampled_x = flat_x[indices]
                sampled_y = flat_y[indices]
            else:
                sampled_phases = flat_phases
                sampled_heights = flat_heights
                sampled_x = flat_x
                sampled_y = flat_y
            
            all_phases.extend(sampled_phases)
            all_heights.extend(sampled_heights)
            all_x_coords.extend(sampled_x)
            all_y_coords.extend(sampled_y)
        
        return (np.array(all_phases), np.array(all_heights), 
                np.array(all_x_coords), np.array(all_y_coords))
    
    def fit_spatial_phase_model(self, phases: np.ndarray, heights: np.ndarray, 
                              x_coords: np.ndarray, y_coords: np.ndarray) -> Dict:
        """
        拟合考虑空间位置的相位-高度模型
        相位 = f(高度, x, y)
        """
        print(f"拟合空间相位模型，数据点数: {len(phases)}")
        
        # 数据预处理
        # 移除异常值
        phase_std = np.std(phases)
        phase_mean = np.mean(phases)
        valid_mask = np.abs(phases - phase_mean) < 3 * phase_std
        
        clean_phases = phases[valid_mask]
        clean_heights = heights[valid_mask]
        clean_x = x_coords[valid_mask]
        clean_y = y_coords[valid_mask]
        
        print(f"数据清理后: {len(clean_phases)} 个数据点")
        
        if len(clean_phases) < 1000:
            print("警告: 有效数据点太少")
            return None
        
        # 归一化坐标
        x_norm = (clean_x - np.mean(clean_x)) / np.std(clean_x)
        y_norm = (clean_y - np.mean(clean_y)) / np.std(clean_y)
        
        # 构建特征矩阵：[height, x, y, height*x, height*y, x*y]
        X = np.column_stack([
            clean_heights,  # 高度
            x_norm,         # x坐标
            y_norm,         # y坐标
            clean_heights * x_norm,  # 高度-x交互项
            clean_heights * y_norm,  # 高度-y交互项
            x_norm * y_norm         # x-y交互项
        ])
        
        # 使用RANSAC回归提高鲁棒性
        ransac = RANSACRegressor(
            LinearRegression(),
            min_samples=1000,
            residual_threshold=0.5,  # 相位残差阈值
            random_state=42
        )
        
        try:
            ransac.fit(X, clean_phases)
            y_pred = ransac.predict(X)
            
            r2 = r2_score(clean_phases, y_pred)
            rmse = np.sqrt(mean_squared_error(clean_phases, y_pred))
            
            print(f"空间模型 - R²: {r2:.6f}, RMSE: {rmse:.4f} rad")
            
            # 计算残差统计
            residuals = clean_phases - y_pred
            residual_std = np.std(residuals)
            
            # 分析各特征的重要性
            coefficients = ransac.estimator_.coef_
            feature_names = ['height', 'x', 'y', 'height*x', 'height*y', 'x*y']
            
            print("特征重要性:")
            for name, coef in zip(feature_names, coefficients):
                print(f"  {name}: {coef:.6f}")
            
            model_info = {
                'model': ransac,
                'model_type': 'spatial_linear',
                'r2_score': r2,
                'rmse': rmse,
                'residual_std': residual_std,
                'n_samples': len(clean_phases),
                'phase_range': (np.min(clean_phases), np.max(clean_phases)),
                'height_range': (np.min(clean_heights), np.max(clean_heights)),
                'coefficients': coefficients,
                'feature_names': feature_names,
                'x_mean': np.mean(clean_x),
                'x_std': np.std(clean_x),
                'y_mean': np.mean(clean_y),
                'y_std': np.std(clean_y),
                'inlier_mask': ransac.inlier_mask_
            }
            
            return model_info
            
        except Exception as e:
            print(f"空间模型拟合失败: {e}")
            return None
    
    def calibrate_projector_final(self, projector_id: int, frequency: int = 8) -> Dict:
        """最终版本的投影仪标定"""
        print(f"\n=== 最终标定投影仪 {projector_id} (频率 {frequency}) ===")
        
        # 收集包裹相位数据
        phase_maps, heights, intensities = self.collect_wrapped_phase_data(projector_id, frequency)
        
        if len(phase_maps) == 0:
            print(f"投影仪 {projector_id} 没有有效数据，跳过标定")
            return None
        
        # 验证强度-高度关系
        if len(intensities) > 1:
            intensity_height_corr = np.corrcoef(intensities, heights)[0, 1]
            print(f"强度-高度相关系数: {intensity_height_corr:.6f}")
        
        # 提取相位-高度对（包含空间信息）
        phases, height_values, x_coords, y_coords = self.extract_phase_height_pairs_with_spatial_info(
            phase_maps, heights)
        
        if len(phases) == 0:
            print(f"投影仪 {projector_id} 没有有效的相位-高度对，跳过标定")
            return None
        
        print(f"提取到 {len(phases)} 个有效的相位-高度对")
        
        # 检查相位-高度相关性
        phase_height_corr = np.corrcoef(phases, height_values)[0, 1]
        print(f"相位-高度相关系数: {phase_height_corr:.6f}")
        
        # 拟合空间相位模型
        model_info = self.fit_spatial_phase_model(phases, height_values, x_coords, y_coords)
        
        if model_info is None:
            print(f"投影仪 {projector_id} 模型拟合失败")
            return None
        
        # 保存标定结果
        calibration_result = {
            'projector_id': projector_id,
            'projector_name': self.projector_configs[projector_id]["name"],
            'frequency': frequency,
            'model_info': model_info,
            'phase_maps': phase_maps,
            'heights': heights,
            'intensities': intensities,
            'calibration_phases': phases,
            'calibration_heights': height_values,
            'phase_height_correlation': phase_height_corr,
            'intensity_height_correlation': intensity_height_corr if len(intensities) > 1 else 0.0
        }
        
        # 打印标定结果
        print(f"最终标定完成:")
        print(f"  相位-高度相关系数: {phase_height_corr:.6f}")
        print(f"  强度-高度相关系数: {intensity_height_corr:.6f}")
        print(f"  R² 分数: {model_info['r2_score']:.6f}")
        print(f"  RMSE: {model_info['rmse']:.4f} rad")
        print(f"  残差标准差: {model_info['residual_std']:.4f} rad")
        print(f"  数据点数: {model_info['n_samples']}")
        
        return calibration_result
    
    def save_calibration_results(self, output_folder: str = "final_calibration_results"):
        """保存标定结果"""
        os.makedirs(output_folder, exist_ok=True)
        
        with open(os.path.join(output_folder, "calibration_results.pkl"), 'wb') as f:
            pickle.dump(self.calibration_results, f)
        
        print(f"标定结果已保存到 {output_folder}")


def test_final_calibration():
    """测试最终标定方法"""
    print("=== 测试最终标定方法 ===")
    
    calibrator = FinalPhaseHeightCalibration()
    
    # 测试投影仪0，使用8像素周期（最敏感）
    result = calibrator.calibrate_projector_final(0, frequency=8)
    
    if result:
        print("最终标定测试成功!")
        
        # 保存结果
        calibrator.calibration_results[0] = result
        calibrator.save_calibration_results("final_calibration_results")
        
        # 简单的质量评估
        r2 = result['model_info']['r2_score']
        rmse = result['model_info']['rmse']
        
        if r2 > 0.8 and rmse < 1.0:
            print("标定质量: 优秀")
        elif r2 > 0.5 and rmse < 2.0:
            print("标定质量: 良好")
        else:
            print("标定质量: 需要改进")
            
    else:
        print("最终标定测试失败")


if __name__ == "__main__":
    test_final_calibration()
