#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成四频五步相移测试图像
用于测试相位展开算法
"""

import numpy as np
import cv2
import os
import matplotlib.pyplot as plt


def generate_sinusoidal_pattern(width: int, height: int, period: int, 
                               phase_shift: float, amplitude: float = 127.5, 
                               offset: float = 127.5, noise_level: float = 0.05) -> np.ndarray:
    """
    生成正弦条纹图案
    Args:
        width: 图像宽度
        height: 图像高度
        period: 像素周期
        phase_shift: 相移 (弧度)
        amplitude: 振幅
        offset: 直流分量
        noise_level: 噪声水平
    Returns:
        pattern: 生成的图案
    """
    # 创建坐标网格
    x = np.arange(width)
    y = np.arange(height)
    X, Y = np.meshgrid(x, y)
    
    # 生成正弦条纹 (水平条纹)
    pattern = amplitude * np.sin(2 * np.pi * X / period + phase_shift) + offset
    
    # 添加噪声
    if noise_level > 0:
        noise = np.random.normal(0, noise_level * amplitude, pattern.shape)
        pattern += noise
    
    # 限制到有效范围
    pattern = np.clip(pattern, 0, 255)
    
    return pattern.astype(np.uint8)


def generate_test_images(output_folder: str = "input_images", 
                        width: int = 640, height: int = 480,
                        pixel_periods: list = [1080, 512, 64, 8],
                        noise_level: float = 0.05):
    """
    生成四频五步相移测试图像
    Args:
        output_folder: 输出文件夹
        width: 图像宽度
        height: 图像高度
        pixel_periods: 像素周期列表
        noise_level: 噪声水平
    """
    # 创建输出文件夹
    os.makedirs(output_folder, exist_ok=True)
    
    # 五步相移的相位
    phase_shifts = [0, np.pi/2, np.pi, 3*np.pi/2, 2*np.pi]
    
    print(f"正在生成测试图像到 {output_folder} 文件夹...")
    
    for freq_idx, period in enumerate(pixel_periods):
        print(f"生成频率 {freq_idx}: 周期 {period} 像素")
        
        for step_idx, phase_shift in enumerate(phase_shifts):
            # 生成图案
            pattern = generate_sinusoidal_pattern(
                width, height, period, phase_shift, noise_level=noise_level
            )
            
            # 保存图像
            filename = f"freq_{freq_idx}_step_{step_idx}.png"
            filepath = os.path.join(output_folder, filename)
            cv2.imwrite(filepath, pattern)
            
            print(f"  保存: {filename}")
    
    print("测试图像生成完成！")


def visualize_test_patterns(image_folder: str = "input_images", 
                           pixel_periods: list = [1080, 512, 64, 8]):
    """
    可视化生成的测试图案
    Args:
        image_folder: 图像文件夹
        pixel_periods: 像素周期列表
    """
    fig, axes = plt.subplots(len(pixel_periods), 5, figsize=(20, 4*len(pixel_periods)))
    
    for freq_idx, period in enumerate(pixel_periods):
        for step_idx in range(5):
            filename = f"freq_{freq_idx}_step_{step_idx}.png"
            filepath = os.path.join(image_folder, filename)
            
            if os.path.exists(filepath):
                img = cv2.imread(filepath, cv2.IMREAD_GRAYSCALE)
                axes[freq_idx, step_idx].imshow(img, cmap='gray')
                axes[freq_idx, step_idx].set_title(f'Period {period}, Step {step_idx}')
                axes[freq_idx, step_idx].axis('off')
            else:
                axes[freq_idx, step_idx].text(0.5, 0.5, 'Image\nNot Found', 
                                            ha='center', va='center', transform=axes[freq_idx, step_idx].transAxes)
                axes[freq_idx, step_idx].set_title(f'Period {period}, Step {step_idx}')
    
    plt.tight_layout()
    plt.savefig('test_patterns_overview.png', dpi=150, bbox_inches='tight')
    plt.show()


def add_surface_variation(pattern: np.ndarray, variation_amplitude: float = 50) -> np.ndarray:
    """
    添加表面高度变化模拟
    Args:
        pattern: 原始图案
        variation_amplitude: 变化幅度
    Returns:
        modified_pattern: 修改后的图案
    """
    height, width = pattern.shape
    
    # 创建表面高度变化 (低频变化)
    x = np.linspace(0, 4*np.pi, width)
    y = np.linspace(0, 3*np.pi, height)
    X, Y = np.meshgrid(x, y)
    
    surface_variation = variation_amplitude * (
        0.5 * np.sin(X) * np.cos(Y) + 
        0.3 * np.sin(2*X + np.pi/4) + 
        0.2 * np.cos(1.5*Y + np.pi/3)
    )
    
    # 应用变化
    modified_pattern = pattern.astype(np.float32) + surface_variation
    modified_pattern = np.clip(modified_pattern, 0, 255)
    
    return modified_pattern.astype(np.uint8)


def generate_realistic_test_images(output_folder: str = "realistic_test_images",
                                 width: int = 640, height: int = 480,
                                 pixel_periods: list = [1080, 512, 64, 8]):
    """
    生成更真实的测试图像（包含表面变化）
    """
    os.makedirs(output_folder, exist_ok=True)
    
    phase_shifts = [0, np.pi/2, np.pi, 3*np.pi/2, 2*np.pi]
    
    print(f"正在生成真实测试图像到 {output_folder} 文件夹...")
    
    for freq_idx, period in enumerate(pixel_periods):
        print(f"生成频率 {freq_idx}: 周期 {period} 像素")
        
        for step_idx, phase_shift in enumerate(phase_shifts):
            # 生成基础图案
            pattern = generate_sinusoidal_pattern(
                width, height, period, phase_shift, noise_level=0.02
            )
            
            # 添加表面变化
            realistic_pattern = add_surface_variation(pattern, variation_amplitude=30)
            
            # 保存图像
            filename = f"freq_{freq_idx}_step_{step_idx}.png"
            filepath = os.path.join(output_folder, filename)
            cv2.imwrite(filepath, realistic_pattern)
    
    print("真实测试图像生成完成！")


def main():
    """主函数"""
    print("=== 四频五步相移测试图像生成器 ===")
    
    # 生成基础测试图像
    print("\n1. 生成基础测试图像...")
    generate_test_images()
    
    # 生成真实测试图像
    print("\n2. 生成真实测试图像...")
    generate_realistic_test_images()
    
    # 可视化测试图案
    print("\n3. 可视化测试图案...")
    visualize_test_patterns()
    
    print("\n所有测试图像生成完成！")
    print("请运行 phase_unwrapping_four_frequency.py 来测试相位展开算法")


if __name__ == "__main__":
    main()
